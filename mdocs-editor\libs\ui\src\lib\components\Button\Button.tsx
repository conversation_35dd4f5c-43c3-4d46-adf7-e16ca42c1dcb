import { cn } from "@/lib/utils";
import "./button.css";

export interface ButtonProps {
  /** Is this the principal call to action on the page? */
  primary?: boolean;
  /** What background color to use */
  backgroundColor?: string;
  /** How large should the button be? */
  size?: 'small' | 'medium' | 'large';
  /** Button contents */
  label: string;
  /** Optional click handler */
  onClick?: () => void;
}

/** Primary UI component for user interaction */
const Button = ({
  primary = false,
  size = 'medium',
  backgroundColor,
  label,
  ...props
}: ButtonProps) => {
  const mode = primary ? 'storybook-button--primary' : 'storybook-button--secondary';

  let sizeClass = 'storybook-button--medium';
  if (size === 'small') {
    sizeClass = 'storybook-button--small';
  } else if (size === 'large') {
    sizeClass = 'storybook-button--large';
  }

  const className = 'storybook-button ' + sizeClass + ' ' + mode;

  return (
    <button
      type="button"
      className={cn(className, "border-2 border-red-500")}
      style={{ backgroundColor }}
      {...props}
    >
      {label}
    </button>
  );
};

export default Button;
