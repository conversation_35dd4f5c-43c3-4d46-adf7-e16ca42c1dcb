import { z } from "zod";

/**
 * MWORKS 元数据 (Metadata)格式: 函数库（Library）
 */
export type JlLibrary = z.infer<typeof JlLibrarySchema>;
/**
 * MWORKS 元数据 (Metadata)格式: 函数（Function）
 */
export type JlFunction = z.infer<typeof JlFunctionSchema>;
/**
 * MWORKS 元数据 (Metadata)格式: 语法/用法（Syntax/Usage）
 */
export type JlSyntax = z.infer<typeof JlSyntaxSchema>;
/**
 * MWORKS 元数据 (Metadata)格式: 示例（Example）
 */
export type JlExample = z.infer<typeof JlExampleSchema>;
/**
 * MWORKS 元数据 (Metadata)格式: 参数（Parameter）
 */
export type JlParameter = z.infer<typeof JlParameterSchema>;
/**
 * MWORKS 元数据 (Metadata)格式: 类（Class）
 */
export type JlClass = z.infer<typeof JlClassSchema>;
/**
 * MWORKS 元数据 (Metadata)格式: 属性（Property）
 */
export type JlProperty = z.infer<typeof JlPropertySchema>;
/**
 * MWORKS 元数据 (Metadata)格式: 枚举（Enum）
 */
export type JlEnum = z.infer<typeof JlEnumSchema>;
/**
 * MWORKS 元数据 (Metadata)格式: 枚举值（Variant）
 */
export type JlVariant = z.infer<typeof JlVariantSchema>;
/**
 * MWORKS 元数据 (Metadata)格式: Markdown 文本（Markdown）
 */
export type JlMarkdown = z.infer<typeof JlMarkdownSchema>;
/**
 * MWORKS 元数据 (Metadata)格式: 语义化版本号（Semantic versioning）
 */
export type JlSemVer = z.infer<typeof JlSemVerSchema>;
/**
 * MWORKS 元数据 (Metadata)格式: Syslab 版本（Syslab version）
 */
export type JlSyslabVer = z.infer<typeof JlSyslabVerSchema>;
/**
 * MWORKS 元数据 (Metadata)格式: URL
 */
export type JlURL = z.infer<typeof JlURLSchema>;
/**
 * MWORKS 元数据 (Metadata)格式: 参数类别（ParamKind）
 */
export type JlParamKind = z.infer<typeof JlParamKindSchema>;
/**
 * MWORKS 元数据 (Metadata)格式: 用法组选项（UsageGroupAt）
 */
export type JlUsageGroupAt = z.infer<typeof JlUsageGroupAtSchema>;
/**
 * MWORKS 元数据 (Metadata)格式: 国际化（I18n）
 */
export type JlI18n = z.infer<typeof JlI18nSchema>;
/**
 * MWORKS 元数据 (Metadata)格式: Syslab 版本, 例如 "2025a"
 */
export const JlSyslabVerSchema = z.string();
/**
 * MWORKS 元数据 (Metadata)格式: Markdown 文本
 */
export const JlMarkdownSchema = z.string();
/**
 * MWORKS 元数据 (Metadata)格式: 用法组选项（UsageGroupAt）
 */
export const JlUsageGroupAtSchema = z.enum(["none", "beforeParams", "afterParams"]);
/**
 * MWORKS 元数据 (Metadata)格式: URL
 */
export const JlURLSchema = z.url();
/**
 * MWORKS 元数据 (Metadata)格式: 国际化类型（I18n Type）
 */
export const JlI18nSchema = z.object({
  /** 中文内容 */
  zh: JlMarkdownSchema,
  /** 英文内容 */
  en: JlMarkdownSchema,
});
/**
 * 正则表达式：语义化版本号（Semantic versioning）
 */
const semverRegex = /^(0|[1-9]\d*)\.(0|[1-9]\d*)\.(0|[1-9]\d*)(?:-[0-9A-Za-z.-]+)?(?:\+[0-9A-Za-z.-]+)?$/;
/**
 * MWORKS 元数据 (Metadata)格式: 函数库版本号，使用 semantic versioning（不带 v 前缀）。
 */
export const JlSemVerSchema = z.string().regex(semverRegex, {
  message: "必须是符合 SemVer 规范的版本号（例如 1.0.0, 不能带 v 前缀）",
});
/**
 * MWORKS 元数据 (Metadata)格式: 参数类别（ParamKind）
 */
export const JlParamKindSchema = z.enum(["posIn", "keyword", "out"]);
/**
 * MWORKS 元数据 (Metadata)格式: 全局引用 - 参数类型（`GlobalRef<ParamType>`),
 * 参数类型本质上就是 Julia、MLang 等编程语言中可以使用的类型。我们使用 `GlobalRef<T>` 的形式，
 * 并放置在一个“隐藏”的名叫 builtins 的空间下提供使用，这样就能够区分格式（schema）内能使用的类型，
 * 以及编程语言使用的类型
 */
export const JlGlobalRef_ParamTypeSchema = z.string().refine((val) => val.startsWith("#/builtins/"), {
  message: "ParamType reference must start with #/builtins/",
});
/**
 * MWORKS 元数据 (Metadata)格式: 全局引用 - 函数（`GlobalRef<Function>`）
 */
export const JlGlobalRef_FunctionSchema = z.string().refine((val) => val.startsWith("#/functions/"), {
  message: "Function reference must start with #/functions/",
});
/**
 * MWORKS 元数据 (Metadata)格式: 全局引用 - 国际化（`GlobalRef<I18n>`）
 */
export const JlGlobalRef_I18nSchema = z.string().refine((val) => val.startsWith("#/i18n/"), {
  message: "i18n reference must start with #/i18n/",
});
/**
 * MWORKS 元数据 (Metadata)格式: 局部引用 - 参数（`GlobalRef<Parameter>`）
 */
export const JlLocalRef_ParameterSchema = z.string().refine((val) => val.startsWith("#params/"), {
  message: "Param reference must start with #params/",
});
/**
 * MWORKS 元数据 (Metadata)格式: 局部引用 - 示例（`GlobalRef<Example>`）
 */
export const JlLocalRef_ExampleSchema = z.string().refine((val) => val.startsWith("#examples/"), {
  message: "Example reference must start with #examples/",
});
/**
 * MWORKS 元数据 (Metadata)格式: 局部引用 - 语法（`GlobalRef<Syntax>`）
 */
export const JlLocalRef_SyntaxSchema = z.string().refine((val) => val.startsWith("#syntaxes/"), {
  message: "Syntax reference must start with #syntaxes/",
});
/**
 * MWORKS 元数据 (Metadata)格式: 示例（Example）
 */
export const JlExampleSchema = z.object({
  /** 示例的短描述 */
  summary: JlI18nSchema,
  /** 样例内容。 */
  description: JlI18nSchema,
  /** 样例对应的用法列表。 */
  syntaxes: z.array(JlLocalRef_SyntaxSchema),
});
/**
 * MWORKS 元数据 (Metadata)格式: 语法/用法（Syntax/Usage）
 */
export const JlSyntaxSchema = z.object({
  /** 语法的长描述。 */
  description: JlI18nSchema,
  /** 语法的组别号 */
  group: z.number().optional(),
  /** 语法引用的参数列表 */
  params: z.array(JlLocalRef_ParameterSchema),
  /** 是否为重复组 */
  repeated: z.boolean().optional(),
  /** 是否在参数列表内放置用法组 */
  usageGroupAt: JlUsageGroupAtSchema.optional(),
  /** 语法示例列表 */
  examples: z.array(JlLocalRef_ExampleSchema).optional(),
  /** 语法测试集列表 */
  tests: z.array(JlURLSchema).optional(),
});
/**
 * MWORKS 元数据 (Metadata)格式: 参数（Parameter）
 */
export const JlParameterSchema = z.object({
  /** 参数的短描述。 */
  summary: JlI18nSchema,
  /** 参数的长描述。 */
  description: JlI18nSchema,
  /** 参数类别 */
  kind: JlParamKindSchema,
  /** 参数类型 */
  type: z.array(JlGlobalRef_ParamTypeSchema).or(JlGlobalRef_ParamTypeSchema),
  /** 参数的元素类型。 */
  elemType: z.array(JlGlobalRef_ParamTypeSchema).or(JlGlobalRef_ParamTypeSchema).optional(),
  /** 参数的默认值 */
  default: z.string().optional(),
  /** 参数简短的示例代码 */
  examples: z.array(z.string()).optional(),
});
/**
 * MWORKS 元数据 (Metadata)格式: 函数（Function）
 */
export const JlFunctionSchema = z.object({
  /** 函数名称 */
  name: z.string().optional(),
  /** 函数的短描述 */
  summary: JlI18nSchema.optional(),
  /** 函数标签列表。 */
  tags: z.array(JlGlobalRef_I18nSchema),
  /** 函数从哪一 Syslab 版本中引入 */
  since: JlSyslabVerSchema,
  /** 函数是否已废弃 */
  deprecated: z.boolean().optional(),
  /** 函数的语法（用法） */
  syntaxes: z.record(z.string(), JlSyntaxSchema),
  /** 函数示例 */
  examples: z.record(z.string(), JlExampleSchema),
  /** 函数的参数列表 */
  params: z.record(z.string(), JlParameterSchema),
  /** 函数的脚注。 */
  footnote: JlI18nSchema.optional(),
  /** 另请参阅。 */
  seeAlso: z.array(JlGlobalRef_FunctionSchema),
  /** 函数变更记录列表 */
  changelogs: z.record(JlSemVerSchema, JlMarkdownSchema),
});
/**
 * MWORKS 元数据 (Metadata)格式: 属性（Property）
 */
export const JlPropertySchema = z.object({
  /** 属性的短描述 */
  summary: JlI18nSchema,
  /** 属性的长描述 */
  description: JlI18nSchema,
  /** 属性的类型 */
  type: z.array(JlGlobalRef_ParamTypeSchema).or(JlGlobalRef_ParamTypeSchema),
  /** 属性的元素类型 */
  elemType: z.array(JlGlobalRef_ParamTypeSchema).optional().or(JlGlobalRef_ParamTypeSchema).optional(),
  /** 属性的默认值 */
  default: z.string().optional(),
  /** 属性简短的示例代码。 */
  examples: z.array(z.string()).optional(),
});
/**
 * MWORKS 元数据 (Metadata)格式: Class（类）
 */
export const JlClassSchema = z.object({
  /** 类的短描述 */
  summary: JlI18nSchema,
  /** 类的长描述 */
  description: JlI18nSchema,
  /** 类的构造函数 */
  constructor: z.union([JlGlobalRef_FunctionSchema, z.array(JlGlobalRef_FunctionSchema)]).optional(),
  /** 类的属性列表 */
  properties: z.record(z.string(), JlPropertySchema).optional(),
  /** 类的关联函数列表 */
  functions: z.array(JlGlobalRef_FunctionSchema).optional(),
});
/**
 * MWORKS 元数据 (Metadata)格式: 枚举值（Variant）
 */
export const JlVariantSchema = z.object({
  /** 枚举值的短描述。 */
  summary: JlI18nSchema,
  /** 枚举值的长描述。 */
  description: JlI18nSchema,
  /** 枚举的值，如果为空，默认为枚举值的名字。 */
  value: z.string().optional(),
});
/**
 * MWORKS 元数据 (Metadata)格式: 枚举（Enum）
 */
export const JlEnumSchema = z.object({
  /** 枚举的短描述。 */
  summary: JlI18nSchema,
  /** 枚举的长描述。 */
  description: JlI18nSchema,
  /** 枚举的值列表。 */
  variants: z.record(z.string(), JlVariantSchema),
});
/**
 * MWORKS 元数据 (Metadata)格式: 函数库（Library）
 */
export const JlLibrarySchema = z.object({
  /** 函数库简要说明 */
  summary: JlI18nSchema.optional(),
  /** 函数库内定义的函数集合 */
  functions: z.record(z.string(), JlFunctionSchema),
  /** 函数库内定义的类型集合 */
  classes: z.record(z.string(), JlClassSchema).optional(),
  /** 函数库提供的其他枚举定义 */
  enums: z.record(z.string(), JlEnumSchema).optional(),
  /** 函数库内使用的国际化标签 */
  i18n: z.record(z.string(), JlI18nSchema).optional(),
});
