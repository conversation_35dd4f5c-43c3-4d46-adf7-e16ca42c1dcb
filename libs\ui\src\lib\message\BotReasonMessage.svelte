<script lang="ts">
  import ChevronDown from '@lucide/svelte/icons/chevron-down';
  import ChevronUp from '@lucide/svelte/icons/chevron-up';
  import Loader from '@lucide/svelte/icons/loader';
  import { cubicInOut } from 'svelte/easing';
  import { slide } from 'svelte/transition';
  import Button from '$lib/components/ui/button/button.svelte';
  import MarkdownRenderer from '$lib/markdown/MarkdownRenderer.svelte';

  interface Props {
    /** @prop {boolean} isExpanded - 是否展开推理文本. */
    isExpanded: boolean;
    /** @prop {boolean} isReasoning - 是否正在推理. */
    isReasoning: boolean;
    /** @prop {string} message - 推理消息 */
    message: string;
    /** @prop {string} reasoningTitle - 推理时按钮消息 */
    reasoningTitle: string;
    /** @prop {string} reasonFinishedTitle - 推理完成时按钮消息 */
    reasonFinishedTitle: string;
  }
  let { isExpanded, isReasoning, message, reasoningTitle, reasonFinishedTitle }: Props = $props();
</script>

<!--
@component
BotReasonMessage是用于显示LLM推理时文本的组件。

- 用法:
  ``` svelte
  <BotReasonMessage
    reasoningTitle="深度思考中"
    reasonFinishedTitle="已深度思考"
    {isExpanded}
    {isReasoning}
    {message}
  />
  ```
-->

<Button
  class="w-fit"
  variant="secondary"
  onclick={() => {
    isExpanded = !isExpanded;
  }}
>
  <div class="flex flex-row items-center gap-2">
    <div class="font-medium">
      {isReasoning ? reasoningTitle : reasonFinishedTitle}
    </div>
    {#if isReasoning}
      <Loader class="animate-spin" />
    {/if}
    {#if isExpanded}
      <ChevronUp />
    {:else}
      <ChevronDown />
    {/if}
  </div>
</Button>
{#if isExpanded}
  <div
    transition:slide={{ duration: 200, easing: cubicInOut }}
    class="border-l pl-4 wrap-break-word text-zinc-600 dark:text-zinc-400"
  >
    <MarkdownRenderer markdown={message} />
  </div>
{/if}
