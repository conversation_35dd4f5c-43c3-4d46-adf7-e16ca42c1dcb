/**
 * @summary 解析路径字符串，返回一个字符串数组。
 * @param path 路径字符串
 * @returns 路径的字符串数组
 * @example 
 * ```typescript
 * const result = getPath("#/params/X"); // ["params", "X"]
 * const result = getPath("#/builtins/Float64"); // ["/", "builtins", "Float64"]

 * ```
 */
export function getPath(path: string): string[] {
  const cleanPath = path.substring(1);
  const res: string[] = [];
  if (cleanPath.startsWith("/")) {
    res.push("/");
  }
  const names = cleanPath.split("/").filter((p) => p.length > 0);
  for (const name of names) {
    res.push(name);
  }
  return res;
}
/**
 * @summary 上下文对象
 * @description 为了在验证过程中传递状态和错误信息，需要引入一些共享状态量作为上下文
 * @property scope 是一个字符串数组，表示目前遍历到的路径
 * @property currentFunctionName 是一个字符串，表示目前遍历到的function的名字
 * @property params 是一个 Set，包含了所有已知的参数的全名，成员的格式为path/name，例如{"functionName/params/X"}
 * @property errors 全局的错误列表，为ValidationError[]类型，包含了所有的语法错误和语义错误信息
 * @example
 * ```typescript
 * // 一般情况下，用户不应该直接使用和修改 Context 的内容，
 * // 而应该使用指定的 API，如 validate_param 函数，它会更新 context.params
 * const context: Context = {
 *   currentFunctionName: "functionName",
 *   params: new Set([]),
 *   errors: []
 * };
 * const param = {
 *   // 参数对象
 * };
 * validate_param(context, "X", param);
 * // 现在 context.params 中会包含 "functionName/params/X"
 * // 如果param的语义有错误，context.errors 中会包含错误信息
 * ```
 */
export interface Context {
  scope: string[];
  currentFunctionName: string;
  params: Set<string>;
  errors: ValidationError[];
}
/**
 * @summary 验证时的错误信息
 * @member scope 错误发生的作用域，通常是函数名或类名
 * @member message 错误信息的字符串
 */
export class ValidationError {
  public scope: string[];
  public message: string;
  constructor(scope: string[], message: string) {
    this.scope = scope;
    this.message = message;
  }
}
