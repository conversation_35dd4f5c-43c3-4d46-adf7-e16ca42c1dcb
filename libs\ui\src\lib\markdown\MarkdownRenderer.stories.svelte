<script module lang="ts">
  //    👆 notice the module context, defineMeta does not work in a regular <script> tag - instance
  import { defineMeta } from '@storybook/addon-svelte-csf';
  import MarkdownRenderer from './MarkdownRenderer.svelte';
  import Textarea from '$lib/components/ui/textarea/textarea.svelte';

  //      👇 Get the Story component from the return value
  const { Story } = defineMeta({
    title: 'UI/MarkdownRenderer',
    component: MarkdownRenderer,
    decorators: [],
    parameters: {},
  });

  let inputStr = $state(`
# h1
## h2
### h3
#### h4
##### h5
###### h6

- ul
- ul
- ul

1. ol
2. ol
3. ol

inline code: \`println("hello world")\`

Julia code block:

\`\`\` julia
# function to calculate the volume of a sphere
function sphere_vol(r)
    # julia allows Unicode names (in UTF-8 encoding)
    # so either "pi" or the symbol π can be used
    return 4/3*pi*r^3
end

# functions can also be defined more succinctly
quadratic(a, sqr_term, b) = (-b + sqr_term) / 2a

# calculates x for 0 = a*x^2+b*x+c, arguments types can be defined in function definitions
function quadratic2(a::Float64, b::Float64, c::Float64)
    # unlike other languages 2a is equivalent to 2*a
    # a^2 is used instead of a**2 or pow(a,2)
    sqr_term = sqrt(b^2-4a*c)
    r1 = quadratic(a, sqr_term, b)
    r2 = quadratic(a, -sqr_term, b)
    # multiple values can be returned from a function using tuples
    # if the return keyword is omitted, the last term is returned
    r1, r2
end

vol = sphere_vol(3)
# @printf allows number formatting but does not automatically append the \n to statements, see below
using Printf
@printf "volume = %0.3f\n" vol 
#> volume = 113.097

quad1, quad2 = quadratic2(2.0, -2.0, -12.0)
println("result 1: ", quad1)
#> result 1: 3.0
println("result 2: ", quad2)
#> result 2: -2.0
\`\`\`

Modelica code block:

\`\`\` modelica
model LotkaVolterra "Lotka-Volterra equations"
    Real x(start = 1) "Prey";
    Real y(start = 1) "Predator";
    parameter Real lambda = 0.5;
    input Real u;
equation
    der(x) =  x - x * y - lambda * u;
    der(y) = -y + x * y -          u;
end LotkaVolterra;
\`\`\`

[goolge link](www.google.com)

| Month    | Savings |
| -------- | ------- |
| January  | $250    |
| February | $80     |
| March    | $420    |

$$\\int_0^\\infty x^2 dxt$$
  `);
</script>

<Textarea bind:value={inputStr} />

<Story name="Default" asChild>
  <MarkdownRenderer markdown={inputStr} />
</Story>
