<script lang="ts">
  import ChevronDown from '@lucide/svelte/icons/chevron-down';
  import ChevronRight from '@lucide/svelte/icons/chevron-right';
  import ExternalLink from '@lucide/svelte/icons/external-link';
  import { cubicInOut } from 'svelte/easing';
  import { slide } from 'svelte/transition';
  import Button from '$lib/components/ui/button/button.svelte';
  import MarkdownRenderer from '$lib/markdown/MarkdownRenderer.svelte';
  import type { ExternalLinkPart } from '@askme/lib-common';

  interface Props {
    /** @prop {ExternalLinkPart} externalLink - 外部链接信息. */
    externalLink: ExternalLinkPart;
  }

  const { externalLink }: Props = $props();
  let isExpanded = $state(false);
</script>

<!--
@component
ExternalSource是渲染外部链接的UI组件。

- 用法:
  ``` svelte
  <ExternalSource
    {externalLink}
  />
  ```
  -->

<div class="bg-secondary flex items-center space-x-2">
  <Button
    variant="ghost"
    onclick={() => {
      isExpanded = !isExpanded;
    }}
  >
    {#if isExpanded}
      <ChevronDown />
    {:else}
      <ChevronRight />
    {/if}
  </Button>
  <span class="truncate font-semibold">{externalLink.title}</span>
  <a href={externalLink.url} target="_blank" rel="noopener noreferrer">
    <ExternalLink size={12} />
  </a>
</div>

{#if isExpanded}
  <div
    transition:slide={{ duration: 200, easing: cubicInOut }}
    class="border-l pl-4 text-zinc-600 dark:text-zinc-400"
  >
    <MarkdownRenderer markdown={externalLink.chunkText} />
  </div>
{/if}
