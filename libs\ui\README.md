# lib-ui

本项目是基于 svelte 的 UI 组件库，用于支撑 askme 项目的前端开发。

## 用法

在其他项目中，通过下面这种方式使用本项目

```svetle
<script lang="ts">
  import { Counter } from '@askme/lib-ui';
</script>
```

## 构建

由于因为我们在根目录的`package.json` 中对 `@askme/lib-ui` 的引用为止是 `libs/ui/dist` 路径，
修改 `lib-ui` 项目的代码后，需要重新构建后才能在其他项目中看到效果。

```bash
pnpm build
```

单个组件的开发与维护基于 storybook 展开，可以通过下面的命令启动 storybook 服务：

```bash
pnpm storybook
```

## 开发

所有的组件都在 `src/lib` 目录下，按照下述规则进行开发维护：

- 每个组件一个文件夹，文件夹名即为组件名。例如：`src/lib/Counter/counter.svelte`
- storybook 及测试文件放在组件文件夹下，如：`src/lib/Counter/counter.stories.svelte`、`src/lib/Counter/counter.test.ts`
- 组件通过 `src/lib/index.ts` 导出后，才能在其他项目中通过 `import { Counter } from '@askme/lib-ui'` 的方式引用

## 上游的错误

- 为svelte storybook设置decorator会抛出类型错误，跟踪此[issue](https://github.com/storybookjs/storybook/issues/29951)
- sveltekit中，`replaceState`后`page` state不响应更新，与文档记录行为不符。进一步情况跟踪此[issue](https://github.com/sveltejs/kit/issues/10661)
