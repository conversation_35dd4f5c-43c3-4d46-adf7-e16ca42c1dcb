<script lang="ts">
  import type { Chat } from '@askme/lib-common/chat';
  import { Button } from '$lib/components/ui/button';
  import { fly } from 'svelte/transition';
  import type { SuggestedActionModel } from '@askme/lib-common';

  let {
    suggestedActions,
    chatClient,
  }: { suggestedActions: SuggestedActionModel[]; chatClient: Chat } = $props();
</script>

{#each suggestedActions as suggestedAction, i (suggestedAction.title)}
  <div
    in:fly|global={{ opacity: 0, y: 20, delay: 50 * i, duration: 400 }}
    class={i > 1 ? 'hidden sm:block' : 'block'}
  >
    <Button
      variant="ghost"
      onclick={async () => {
        await chatClient.append({
          role: 'user',
          content: suggestedAction.action,
        });
      }}
      class="h-auto w-full flex-1 items-start justify-start gap-1 rounded-xl border px-4 py-3.5 text-left text-sm sm:flex-col"
    >
      <span class="font-medium">{suggestedAction.title}</span>
      <span class="text-muted-foreground">
        {suggestedAction.label}
      </span>
    </Button>
  </div>
{/each}
