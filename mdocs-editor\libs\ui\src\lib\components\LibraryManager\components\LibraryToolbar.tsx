import { useState } from "react";
import { Button } from "@/lib/components/ui/button";
import { ImportDialog } from "../../ImportDialog/ImportDialog";

/**
 * LibraryToolbar 组件属性接口
 */
export interface LibraryToolbarProps {
  /** 是否禁用所有按钮 */
  disabled?: boolean;
  /** 库的数量，用于判断导出所有按钮状态 */
  libraryCount?: number;
  /** 导入回调函数 */
  onImport?: (file: File) => void | Promise<void>;
  /** 导出所有回调函数 */
  onExportAll?: () => void | Promise<void>;
}

/**
 * 库工具栏组件
 * 
 * 功能：
 * - 提供导入库的按钮和对话框
 * - 提供导出所有库的按钮
 * - 管理操作过程中的加载状态
 * - 处理操作过程中的错误
 */
export function LibraryToolbar({
  disabled = false,
  libraryCount = 0,
  onImport,
  onExportAll,
}: LibraryToolbarProps) {
  const [importDialogOpen, setImportDialogOpen] = useState(false);
  const [isExporting, setIsExporting] = useState(false);

  // 处理导入提交
  const handleImportSubmit = async (file: File) => {
    if (!onImport) return;

    try {
      await onImport(file);
      setImportDialogOpen(false);
    } catch (error) {
      console.error('导入函数库失败:', error);
      // 保持对话框打开，让用户可以重试
    }
  };

  // 处理导出所有
  const handleExportAll = async () => {
    if (!onExportAll || isExporting) return;

    try {
      setIsExporting(true);
      await onExportAll();
    } catch (error) {
      console.error('导出所有函数库失败:', error);
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <div className="flex items-center gap-2">
      {/* 导出所有按钮 */}
      {onExportAll && (
        <Button
          variant="outline"
          onClick={handleExportAll}
          disabled={disabled || isExporting || libraryCount === 0}
        >
          {isExporting ? "导出中..." : "导出所有"}
        </Button>
      )}

      {/* 导入按钮 */}
      <Button
        onClick={() => setImportDialogOpen(true)}
        disabled={disabled}
      >
        导入
      </Button>

      {/* 导入对话框 */}
      <ImportDialog
        open={importDialogOpen}
        onOpenChange={setImportDialogOpen}
        onImport={handleImportSubmit}
        allowedFileTypes=".json"
      />
    </div>
  );
}
