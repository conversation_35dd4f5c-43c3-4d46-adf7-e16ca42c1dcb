import React from "react";
import { Button } from "../../../ui/button";
import { Input } from "../../../ui/input";
import { Label } from "../../../ui/label";
import { Plus, X } from "lucide-react";

/**
 * 数组管理组件的属性接口
 * @interface ArrayManagerProps
 * @description 用于数组管理的属性类型定义。
 * @property label 字段标签
 * @property value 当前数组值
 * @property onChange 数组变化回调
 * @property disabled 是否禁用
 * @property placeholder 输入框占位符
 */
export interface ArrayManagerProps {
  label: string;
  value: string[];
  onChange: (value: string[]) => void;
  disabled?: boolean;
  placeholder?: string; 
  required?: boolean;
  addButtonText?: string;
  helpText?: string;
  minItems?: number;
  maxItems?: number;
  defaultNewValue?: string;
}

/**
 * 数组管理组件
 * 
 * 提供数组项的增删改功能，减少重复的数组管理代码
 */
export const ArrayManager: React.FC<ArrayManagerProps> = ({
  label,
  value = [],
  onChange,
  disabled = false,
  placeholder = '',
  required = false,
  addButtonText = '添加项目',
  helpText,
  minItems = 0,
  maxItems = Infinity,
  defaultNewValue = '',
}) => {
  /**
   * 添加新项目
   */
  const addItem = () => {
    if (value.length >= maxItems) return;
    onChange([...value, defaultNewValue]);
  };

  /**
   * 删除指定索引的项目
   * @param index 项目索引
   */
  const removeItem = (index: number) => {
    if (value.length <= minItems) return;
    onChange(value.filter((_, i) => i !== index));
  };

  /**
   * 更新指定索引的项目值
   * @param index 项目索引
   * @param newValue 新值
   */
  const updateItem = (index: number, newValue: string) => {
    const newArray = [...value];
    newArray[index] = newValue;
    onChange(newArray);
  };

  return (
    <div className="space-y-2">
      {/* 标签和添加按钮 */}
      <div className="flex items-center justify-between">
        <Label className="text-base font-medium">
          {label}{required && <span className="text-red-500 ml-1">*</span>}
        </Label>
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={addItem}
          disabled={disabled || value.length >= maxItems}
        >
          <Plus className="w-4 h-4 mr-1" />
          {addButtonText}
        </Button>
      </div>

      {/* 项目列表 */}
      <div className="space-y-2">
        {value.map((item, index) => {
          return (
            <div key={index} className="space-y-1">
              <div className="flex items-center gap-2">
                <Input
                  value={item}
                  onChange={(e) => updateItem(index, e.target.value)}
                  disabled={disabled}
                  placeholder={placeholder}
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => removeItem(index)}
                  disabled={disabled || value.length <= minItems}
                >
                  <X className="w-4 h-4" />
                </Button>
              </div>
            </div>
          );
        })}

        {/* 空状态提示 */}
        {value.length === 0 && (
          <div className="text-center py-4 text-gray-500 text-sm border-2 border-dashed border-gray-200 rounded-lg">
            暂无项目，点击上方按钮添加
          </div>
        )}
      </div>

      {/* 帮助文本 */}
      {helpText && (
        <span className="text-xs text-gray-500">
          {helpText}
        </span>
      )}
      <br></br>

      {/* 数量限制提示 */}
      {(minItems > 0 || maxItems < Infinity) && (
        <span className="text-xs text-gray-400">
          {minItems > 0 && maxItems < Infinity
            ? `需要 ${minItems}-${maxItems} 个项目`
            : minItems > 0
            ? `至少需要 ${minItems} 个项目`
            : `最多 ${maxItems} 个项目`}
          {` (当前: ${value.length})`}
        </span>
      )}
    </div>
  );
};
