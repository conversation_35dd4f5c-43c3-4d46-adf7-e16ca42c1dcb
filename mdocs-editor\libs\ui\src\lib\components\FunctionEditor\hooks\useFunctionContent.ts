import { useCallback } from "react";
import type { I18nText } from "../components/ui/I18nInput";
import type { FunctionContent } from "../FunctionEditor";

/**
 * 函数内容管理 Hook 的属性接口
 * @interface UseFunctionContentProps
 * @description 用于函数内容管理 Hook 的属性类型定义。
 * @property content 当前函数内容
 * @property onContentChange 内容更新回调
 */
export interface UseFunctionContentProps {
  /** 当前函数内容 */
  content: Partial<FunctionContent>;
  /** 内容更新回调 */
  onContentChange: (updates: Partial<FunctionContent>) => void;
}

/**
 * 函数内容管理 Hook 的返回值接口
 * @interface UseFunctionContentReturn
 * @description 用于函数内容管理 Hook 的返回值类型定义。
 * @property updateField 更新单个字段
 * @property updateI18nField 更新国际化字段
 */
export interface UseFunctionContentReturn {
  /** 更新单个字段 */
  updateField: (field: keyof FunctionContent, value: unknown) => void;
  /** 更新国际化字段 */
  updateI18nField: (
    field: keyof Pick<FunctionContent, "summary" | "footnote">,
    lang: keyof I18nText,
    value: string,
  ) => void;
}

/**
 * 函数内容管理 Hook
 *
 * 提供函数内容的更新方法，封装常用的更新逻辑
 *
 * @param props Hook 属性
 * @returns 内容更新方法
 */
export const useFunctionContent = ({ content, onContentChange }: UseFunctionContentProps): UseFunctionContentReturn => {
  /**
   * 更新单个字段
   * @param field 字段名
   * @param value 新值
   */
  const updateField = useCallback(
    (field: keyof FunctionContent, value: unknown) => {
      onContentChange({
        ...content,
        [field]: value,
      });
    },
    [content, onContentChange],
  );

  /**
   * 更新国际化字段
   * @param field 字段名
   * @param lang 语言
   * @param value 新值
   */
  const updateI18nField = useCallback(
    (field: keyof Pick<FunctionContent, "summary" | "footnote">, lang: keyof I18nText, value: string) => {
      const currentValue = content[field] as I18nText | undefined;
      updateField(field, {
        ...currentValue,
        [lang]: value,
      });
    },
    [content, updateField],
  );

  return {
    updateField,
    updateI18nField,
  };
};
