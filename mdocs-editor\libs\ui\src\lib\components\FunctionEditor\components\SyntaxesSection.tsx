import React, { useMemo, useCallback, useState } from "react";
import { But<PERSON> } from "../../ui/button";
import { Label } from "../../ui/label";
import { Plus, X } from "lucide-react";
import { I18nInput } from "./ui/I18nInput";
import { TagInput } from "./ui/TagInput";

import { useFunctionContent } from "../hooks/useFunctionContent";
import type { I18nText } from "./ui/I18nInput";
import { FunctionContent } from "../FunctionEditor";

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../../ui/select";
import { Options } from "../views/FormView";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "../../ui/dialog";

// MWORKS 元数据格式规范中定义的用法组选项
const SYNTAX_USAGEGROUPAT_OPTIONS: Options[] = [
  { value: "none", label: "不放置" },
  { value: "beforeParams", label: "放置在参数前" },
  { value: "afterParams", label: "放置在参数后" },
];

interface SyntaxContent {
  /** 语法的长描述。 */
  description: I18nText;
  /** 语法的组别号 */
  group?: number;
  /** 参数引用列表 */
  params: string[];
  /** 是否重复组 */
  repeated?: boolean;
  /** 用法组位置 */
  usageGroupAt?: string;
  /** 示例关联 */
  examples?: string[];
  /** 测试集关联 */
  tests?: string[];
}

export interface SyntaxesSectionProps {
  /** 函数内容数据 */
  content: Partial<FunctionContent>;
  /** 内容更新回调函数 */
  onContentChange: (updates: Partial<FunctionContent>) => void;
  /** 是否处于加载状态 */
  isLoading?: boolean;
  /** 函数名称 */
  functionName?: string;
}

/**
 * 语法管理部分组件
 *
 * 负责管理函数的语法定义，包括：
 * - 添加/删除语法
 * - 编辑语法名称和描述
 * - 管理语法参数
 * - 配置语法选项
 */
export const SyntaxesSection: React.FC<SyntaxesSectionProps> = ({
  content,
  onContentChange,
  isLoading = false,
  functionName = "functionName",
}) => {
  const { updateField } = useFunctionContent({
    content,
    onContentChange,
  });
  const [dialogSyntaxName, setDialogSyntaxName] = useState<string | null>(null);

  /**
   * 通用的选项生成函数（用于提供参数和示例的下拉选项）
   *  @param type 选项类型，'params' 或 'examples'
   *  @param currentValues 当前已选值
   *  @param isRepeated 是否为重复组
   * 
   *  @returns 选项列表
   */
  const generateOptions = useCallback((
    type: 'params' | 'examples',
    currentValues: string[] = [],
    isRepeated: boolean = false
  ): Options[] => {
    const options: Options[] = [];

    if (type === 'params') {
      const params = content.params || {};

      // 添加有效参数选项
      Object.keys(params).forEach(paramName => {
        const param = params[paramName];
        const summary = param?.summary?.zh || param?.summary?.en || paramName;

        const displayLabel = formatParamName(paramName, isRepeated);
        const description = (isRepeated && !isOutputParam(paramName)) ? `${summary} (重复参数)` : summary;

        options.push({
          value: `#params/${paramName}`,
          label: displayLabel,
          description: description
        });
      });

      // 添加已删除的参数，用于提示用户引用失效
      currentValues.forEach(paramRef => {
        if (paramRef.startsWith('#params/')) {
          const paramName = parseRef(paramRef, 'params');
          if (!params[paramName]) {
            const displayLabel = formatParamName(paramName, isRepeated);
            options.push({
              value: paramRef,
              label: `${displayLabel} (已删除，建议移除)`,
              description: "此参数已被删除，建议移除"
            });
          }
        }
      });
    } else {
      if (!content.examples || typeof content.examples !== 'object') {
        return options;
      }

      const examples = content.examples||{};

      // 添加有效示例选项
      Object.keys(examples).forEach(exampleName => {
        const example = examples[exampleName];
        const description = example?.summary?.zh || example?.summary?.en || `示例: ${exampleName}`;

        options.push({
          value: `#examples/${exampleName}`,
          label: exampleName,
          description: description
        });
      });

      // 添加已删除的示例
      currentValues.forEach(exampleRef => {
        if (exampleRef.startsWith('#examples/')) {
          const exampleName = parseRef(exampleRef, 'examples');
          if (!examples[exampleName]) {
            options.push({
              value: exampleRef,
              label: `${exampleName} (已删除，建议移除)`,
              description: "此示例已被删除，建议移除"
            });
          }
        }
      });
    }

    return options;
  }, [content.params, content.examples]);

  // 通用的引用解析函数
  const parseRef = useCallback((ref: string, type: 'params' | 'examples') => {
    const prefix = type === 'params' ? '#params/' : '#examples/';
    return ref.startsWith(prefix) ? ref.replace(prefix, '') : ref;
  }, []);

  // 判断是否为输出参数的工具函数
  const isOutputParam = useCallback((paramName: string): boolean => {
    const param = content.params?.[paramName];
    return param?.kind === 'out';
  }, [content.params]);

  // 处理参数名称的工具函数（考虑重复模式和输出参数）
  const formatParamName = useCallback((paramName: string, isRepeated: boolean): string => {
    return (isRepeated && !isOutputParam(paramName)) ? `${paramName}n` : paramName;
  }, [isOutputParam]);

  // 分类参数的工具函数
  const categorizeParams = useCallback((params: string[]) => {
    const allParams = params.map(param => {
      const paramName = parseRef(param, 'params');
      const paramData = content.params?.[paramName];
      return { name: paramName, param: paramData, ref: param };
    });

    return {
      outputParams: allParams.filter(({ param }) => param?.kind === 'out'),
      inputParams: allParams.filter(({ param }) => param?.kind !== 'out')
    };
  }, [parseRef, content.params]);



  // 根据参数生成语法ID
  const generateSyntaxName = useCallback((params: string[], isRepeated: boolean = false, usageGroupAt: string = 'none'): string => {
    // 处理空参数的情况
    if (params.length === 0) {
      if (usageGroupAt === 'beforeParams' || usageGroupAt === 'afterParams') {
        return '_';
      }
      return 'syntax1';
    }

    const paramNames = params.map(param => {
      const paramName = parseRef(param, 'params');
      return formatParamName(paramName, isRepeated);
    });

    if (usageGroupAt === 'beforeParams') {
      paramNames.unshift('_');
    } else if (usageGroupAt === 'afterParams') {
      paramNames.push('_');
    }

    return paramNames.join('_');
  }, [parseRef, content.params]);

  // 生成完整的语法预览字符串（包含输出参数）
  const generateSyntaxPreview = useCallback((params: string[], isRepeated: boolean = false, usageGroupAt: string = 'none'): string => {
    if (params.length === 0) {
      if (usageGroupAt === 'beforeParams' || usageGroupAt === 'afterParams') {
        return `${functionName}(___)`;
      }
      return `${functionName}(请选择参数)`;
    }

    // 分离输出参数和输入参数
    const { outputParams, inputParams } = categorizeParams(params);

    // 构建输入参数列表
    let inputParamNames = inputParams.map(({ name }) => name);

    if (isRepeated && inputParamNames.length > 0) {
      if (inputParamNames.length >= 2) {
        const pattern1 = inputParamNames.map(p => `${p}1`).join(',');
        const patternN = inputParamNames.map(p => `${p}n`).join(',');
        inputParamNames = [`${pattern1},...,${patternN}`];
      } else {
        const firstParam = inputParamNames[0];
        inputParamNames = [`${firstParam}1,...,${firstParam}n`];
      }
    }

    // 处理用法组
    if (usageGroupAt === 'beforeParams') {
      inputParamNames.unshift('___');
    } else if (usageGroupAt === 'afterParams') {
      inputParamNames.push('___');
    }

    const inputParamList = inputParamNames.join(', ');

    // 构建完整的语法字符串
    if (outputParams.length > 0) {
      const outputParamNames = outputParams.map(({ name }) => name);
      // 输出参数不参与重复模式，始终保持原始名称
      const outputList = outputParamNames.length > 1 ? `[${outputParamNames.join(', ')}]` : outputParamNames[0];
      return `${outputList} = ${functionName}(${inputParamList})`;
    } else {
      return `${functionName}(${inputParamList})`;
    }
  }, [parseRef, functionName, content.params]);

  // 获取所有存在的语法组
  const getExistingGroups = (): number[] => {
    const syntaxes = getSyntaxes();
    const groupNumbers = Object.values(syntaxes)
      .map(syntax => syntax.group || 1)
      .filter(group => group > 0);

    return [...new Set(groupNumbers)].sort((a, b) => a - b);
  };

  // 获取默认语法组号（最新的组，如果没有则为1）
  const getDefaultGroupNumber = (): number => {
    const groups = getExistingGroups();
    return groups.length > 0 ? groups[groups.length - 1] : 1;
  };

  /**
   * 获取语法记录
   */
  const getSyntaxes = (): Record<string, SyntaxContent> => {
    return (content.syntaxes as unknown as Record<string, SyntaxContent>) || {};
  };

  /**
   * 添加新语法到指定组
   */
  const addSyntax = (groupNumber?: number) => {
    const syntaxes = getSyntaxes();
    const targetGroup = groupNumber || getDefaultGroupNumber();
    const newSyntaxName = `syntax${Object.keys(syntaxes).length + 1}`;
    const newSyntax: SyntaxContent = {
      description: { zh: '', en: '' },
      group: targetGroup,
      params: [],
    };
    updateField('syntaxes', { [newSyntaxName]: newSyntax, ...syntaxes });
  };

  /**
   * 删除语法
   * @param syntaxName 语法名称
   */
  const removeSyntax = (syntaxName: string) => {
    const syntaxes = getSyntaxes();
    const newSyntaxes = { ...syntaxes };
    delete newSyntaxes[syntaxName];
    updateField('syntaxes', newSyntaxes);
  };

  /**
   * 更新语法字段
   * @param syntaxName 语法名称
   * @param field 字段名
   * @param value 新值
   */
  const updateSyntax = (syntaxName: string, field: keyof SyntaxContent, value: SyntaxContent[keyof SyntaxContent]) => {
    const syntaxes = getSyntaxes();
    const syntax = syntaxes[syntaxName];
    if (syntax) {
      const updatedSyntax = { ...syntax, [field]: value };

      // 自动生成语法名
      if ((field === 'params' && Array.isArray(value)) || field === 'repeated' || field === 'usageGroupAt') {
        const params = field === 'params' ? (value as string[]) : syntax.params || [];
        const isRepeated = field === 'repeated' ? (value as boolean) : syntax.repeated || false;
        const usageGroupAt = field === 'usageGroupAt' ? (value as string) : syntax.usageGroupAt || 'none';
        const newSyntaxName = generateSyntaxName(params, isRepeated, usageGroupAt);


        // 重命名语法- 保持顺序
        if (newSyntaxName !== syntaxName && !syntaxes[newSyntaxName]) {
          const newSyntaxes: Record<string, SyntaxContent> = {};
          Object.entries(syntaxes).forEach(([key, val]) => {
            if (key === syntaxName) {
              newSyntaxes[newSyntaxName] = updatedSyntax;
            } else {
              newSyntaxes[key] = val;
            }
          });
          updateField('syntaxes', newSyntaxes);
          return;
        }
      }

      // 更新语法
      updateField('syntaxes', { ...syntaxes, [syntaxName]: updatedSyntax });
    }
  };

  const syntaxes = getSyntaxes();

  // 按语法组分组
  const syntaxesByGroup = useMemo(() => {
    const groups: Record<number, Array<[string, SyntaxContent]>> = {};

    Object.entries(syntaxes).forEach(([name, syntax]) => {
      const groupNum = syntax.group || 1;
      if (!groups[groupNum]) {
        groups[groupNum] = [];
      }
      groups[groupNum].push([name, syntax]);
    });

    return groups;
  }, [syntaxes]);

  return (
    <div className="space-y-4">
      {/* 添加语法按钮 */}
      <div className="flex items-center justify-between">
        <span className="text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
          {Object.keys(syntaxes).length} 个语法
        </span>
        <div className="flex items-center gap-2">
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={() => {
              const groups = getExistingGroups();
              const newGroupNumber = groups.length > 0 ? Math.max(...groups) + 1 : 1;
              addSyntax(newGroupNumber);
            }}
            disabled={isLoading}
          >
            <Plus className="w-4 h-4 mr-1" />
            新建语法组
          </Button>
        </div>
      </div>

      {/* 按语法组显示语法列表 */}
      <div className="space-y-8">
        {getExistingGroups().map(groupNum => (
          <div key={groupNum} className="space-y-4">
            {/* 语法组标题 */}
            <div className="flex items-center gap-2 pb-2 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-800">语法组 {groupNum}</h3>
              <span className="text-sm text-gray-500">
                ({syntaxesByGroup[groupNum]?.length || 0} 个语法)
              </span>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => addSyntax(groupNum)}
                disabled={isLoading}
                className="ml-auto"
              >
                <Plus className="w-4 h-4 mr-1" />
                添加语法到此组
              </Button>
            </div>

            {/* 该组的语法列表 */}
            <div className="space-y-6">
              {(syntaxesByGroup[groupNum] || []).map(([syntaxName, syntax]) => (
                <div key={`${syntaxName}-${syntax.repeated}-${JSON.stringify(syntax.params)}`} className="border rounded-lg p-4 space-y-4">
                  {/* 语法ID和删除按钮 */}
                  <div className="flex items-center justify-between gap-4">
                    <div className="flex items-center gap-2 flex-1">
                      <Label className="text-base font-medium">语法ID</Label>
                      <span className="text-base font-mono bg-gray-100 px-2 py-1 rounded">
                        {syntaxName}
                      </span>
                      <span className="text-sm text-gray-500 ml-4">
                        语法组: {syntax.group || 1}
                      </span>
                    </div>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => setDialogSyntaxName(syntaxName)}
                      disabled={isLoading}
                      title="删除语法"
                    >
                      <X className="w-4 h-4" />
                    </Button>
                    <Dialog open={dialogSyntaxName === syntaxName} onOpenChange={(open)=>{
                      if (!open) setDialogSyntaxName(null);
                    }}>
                      <DialogContent className="sm:max-w-md">
                        <DialogHeader>
                          <DialogTitle>确认删除</DialogTitle>
                          <DialogDescription>
                            此操作无法撤销。您确定要删除语法 {syntaxName} 吗？
                          </DialogDescription>
                        </DialogHeader>
                        <DialogFooter>
                          {/* 取消按钮 - 关闭弹窗 */}
                          <Button onClick={() => {
                            setDialogSyntaxName(null)
                          }}>
                            取消
                          </Button>
                          {/* 确认删除按钮 - 执行删除并关闭弹窗 */}
                          <Button
                            onClick={() => {
                              removeSyntax(syntaxName);
                              setDialogSyntaxName(null);
                            }}
                          >
                            确认删除
                          </Button>
                        </DialogFooter>
                      </DialogContent>
                    </Dialog>
                  </div>

                  {/* 语法描述 */}
                  <I18nInput
                    label="语法描述"
                    value={syntax.description}
                    onChange={(value) => updateSyntax(syntaxName, 'description', value)}
                    disabled={isLoading}
                    type="textarea"
                    rows={3}
                    placeholderZh="描述此语法的用法和功能"
                    placeholderEn="Describe the usage and functionality of this syntax"
                    idPrefix={`syntax-desc-${syntaxName}`}
                    required={true}
                  />

                  {/* 语法内容 */}
                  <div className="space-y-2">
                    <Label className="text-base font-medium">语法内容 <span className="text-red-500">*</span></Label>

                    {/* 函数调用预览 */}
                    <div className="flex items-center p-2 bg-gray-50 rounded">
                      <span className="text-sm font-mono text-gray-700">
                        {(() => {
                          const params = syntax.params || [];
                          const usageGroupAt = syntax.usageGroupAt || 'none';
                          const isRepeated = syntax.repeated || false;

                          return generateSyntaxPreview(params, isRepeated, usageGroupAt);
                        })()}
                      </span>
                    </div>

                    {/* 参数选择 */}
                    <TagInput
                      label="引用参数"
                      tags={syntax.params || []}
                      availableTags={generateOptions('params', syntax.params || [], syntax.repeated || false)}
                      onTagAdd={(paramRef: string) => {
                        const currentParams = syntax.params || [];
                        updateSyntax(syntaxName, 'params', [...currentParams, paramRef]);
                      }}
                      onTagRemove={(index: number) => {
                        const currentParams = syntax.params || [];
                        const newParams = currentParams.filter((_, i) => i !== index);
                        updateSyntax(syntaxName, 'params', newParams);
                      }}
                      disabled={isLoading}
                      placeholder="选择参数"
                      className="text-base"
                    />

                    <span className="text-xs text-gray-500">
                      引用已有参数，勾选"重复参数组"后参数名将显示为 Xn 格式（表示参数可重复）。用法组位置通过下拉选择控制。
                    </span>
                  </div>

                  {/* 语法基本信息 */}
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label className="text-base font-medium">用法组位置</Label>
                      <Select
                        value={syntax.usageGroupAt || ''}
                        onValueChange={(value: string) => {
                          updateSyntax(syntaxName, 'usageGroupAt', value)
                        }}
                        disabled={isLoading}
                      >
                        <SelectTrigger className="w-full">
                          <SelectValue placeholder="选择位置" />
                        </SelectTrigger>
                        <SelectContent>
                          {SYNTAX_USAGEGROUPAT_OPTIONS.map((opt) => (
                            <SelectItem key={opt.value} value={opt.value} className="py-3">
                              <div className="flex flex-col">
                                <span className="font-medium">{opt.label}</span>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label className="text-base font-medium">重复参数组</Label>
                      <div className="flex items-center gap-2 h-10 px-3 border rounded-md bg-white">
                        <input
                          type="checkbox"
                          id={`repeated-${syntaxName}`}
                          checked={syntax.repeated || false}
                          onChange={(e) => updateSyntax(syntaxName, 'repeated', e.target.checked)}
                          disabled={isLoading}
                          className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
                        />
                        <Label htmlFor={`repeated-${syntaxName}`} className="text-sm text-gray-600">
                          参数名显示为 Xn 格式
                        </Label>
                      </div>
                    </div>
                  </div>

                  {/* 示例关联 */}
                  <TagInput
                    label="示例关联"
                    tags={syntax.examples || []}
                    availableTags={generateOptions('examples', syntax.examples || [])}
                    onTagAdd={(exampleRef: string) => {
                      const currentExamples = syntax.examples || [];
                      updateSyntax(syntaxName, 'examples', [...currentExamples, exampleRef]);
                    }}
                    onTagRemove={(index: number) => {
                      const currentExamples = syntax.examples || [];
                      const newExamples = currentExamples.filter((_, i) => i !== index);
                      updateSyntax(syntaxName, 'examples', newExamples);
                    }}
                    disabled={isLoading}
                    placeholder="选择示例"
                    className="text-base"
                  />

                  <span className="text-xs text-gray-500">
                    关联相关示例，帮助用户理解语法的具体用法。
                  </span>
                </div>
              ))}
            </div>
          </div>
        ))}

        {/* 空状态提示 */}
        {Object.keys(syntaxes).length === 0 && (
          <div className="text-center py-1 text-gray-500">
            <span className="mb-2 text-sm">暂无语法定义</span>
            <span className="text-sm">点击上方按钮添加语法</span>
          </div>
        )}
      </div>
    </div>
  );
};
