# 这里引入多阶段构建流程，目的是：
#  1. 减少最终镜像的大小
#  2. 移除镜像层中的源码内容: 注意 deps, runner 中并不包含实际源码，只包含依赖信息
#
# 需要额外注意的是，这里使用了 pnpm + monorepo 的管理方式，因此需要保留目录结构，
# 而不是直接对 apps/mdocs-editor 进行构建，否则会导致依赖关系丢失。
#
# 构建需要在项目根目录下进行:
#
# ```
# docker build -f apps/mdocs-editor/Dockerfile -t mdocs-editor:latest .
# ```
# 或使用docker-buildx 进行构建以消除警告:
# ```
# sudo docker buildx build -f apps/mdocs-editor/Dockerfile -t mdocs-editor:latest .
# ```

FROM node:22-alpine AS base

ENV NPM_REGISTRY=https://mirrors-dev.tongyuan.cc/npm
ENV PNPM_VERSION=10.10.0
ENV NEXT_TELEMETRY_DISABLED=1

FROM base AS deps
WORKDIR /app

COPY package.json pnpm-lock.yaml pnpm-workspace.yaml ./
COPY apps/mdocs-editor/package.json ./apps/mdocs-editor/
COPY libs/ui/package.json ./libs/ui/
COPY libs/schema/package.json ./libs/schema/

RUN npm config set registry $NPM_REGISTRY && \
    npm install -g pnpm@$PNPM_VERSION && \
    pnpm install --frozen-lockfile

FROM deps AS builder

COPY . .
COPY --from=deps /app/node_modules ./node_modules
COPY --from=deps /app/apps/mdocs-editor/node_modules ./apps/mdocs-editor/node_modules
COPY --from=deps /app/libs/ui/node_modules ./libs/ui/node_modules
COPY --from=deps /app/libs/schema/node_modules ./libs/schema/node_modules
# 迁移prisma schema需要安装prisma以执行prisma migrate
RUN npm config set registry $NPM_REGISTRY && \
    npm install -g prisma && \
    pnpm -C apps/mdocs-editor exec prisma generate
RUN pnpm run build:app

FROM base AS runner
WORKDIR /app

ENV NODE_ENV=production


RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=deps --chown=nextjs:nodejs /app/node_modules ./node_modules
COPY --from=deps --chown=nextjs:nodejs /app/apps/mdocs-editor/node_modules ./apps/mdocs-editor/node_modules

COPY --from=builder --chown=nextjs:nodejs /app/apps/mdocs-editor/prisma ./apps/mdocs-editor/prisma
COPY --from=builder --chown=nextjs:nodejs /app/apps/mdocs-editor/src/lib/generated ./apps/mdocs-editor/src/lib/generated
COPY --from=builder --chown=nextjs:nodejs /app/apps/mdocs-editor/public ./apps/mdocs-editor/public
COPY --from=builder --chown=nextjs:nodejs /app/apps/mdocs-editor/.next/standalone/apps/mdocs-editor ./apps/mdocs-editor
COPY --from=builder --chown=nextjs:nodejs /app/apps/mdocs-editor/.next/static ./apps/mdocs-editor/.next/static

USER nextjs

RUN npm config set registry $NPM_REGISTRY \
 && npm install -g pnpm@${PNPM_VERSION} \
 && npm install -g prisma

EXPOSE 3030


ENV PORT=3030

ENV HOSTNAME="0.0.0.0"
CMD ["node", "apps/mdocs-editor/server.js"]
