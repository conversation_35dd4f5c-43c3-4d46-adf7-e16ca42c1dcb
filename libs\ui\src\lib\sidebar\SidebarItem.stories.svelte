<script module lang="ts">
  //    👆 notice the module context, defineMeta does not work in a regular <script> tag - instance
  import { defineMeta } from '@storybook/addon-svelte-csf';
  import * as Sidebar from '$lib/components/ui/sidebar';
  import SidebarItem from './SidebarItem.svelte';

  //      👇 Get the Story component from the return value
  const { Story } = defineMeta({
    title: 'UI/SidebarItem',
    component: SidebarItem,
    decorators: [
      /* ... */
    ],
    parameters: {
      /* ... */
    },
  });
</script>

<Story name="Default" asChild>
  <Sidebar.GroupContent>
    <Sidebar.Menu>
      <SidebarItem
        isActive={false}
        content="汉堡好吃🍔！"
        id="6"
        onDelete={(id) => console.log('Delete Chat' + id)}
        onRename={(id) => console.log('Rename Chat' + id)}
        itemURL="hanbao"
      />
      <SidebarItem
        isActive={true}
        content="我们svelte真是太好用了"
        id="3"
        onDelete={(id) => console.log('Chat' + id)}
        itemURL="svelte"
        onRename={(id) => console.log('Rename Chat' + id)}
      />
    </Sidebar.Menu>
  </Sidebar.GroupContent>
</Story>
