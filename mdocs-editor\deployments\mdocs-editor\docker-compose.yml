version: "3.8"

services:
  db:
    image: postgres:17.6
    container_name: mdocs-editor-postgres
    restart: unless-stopped
    env_file:
      - ./.env
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5455:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 5s
      timeout: 5s
      retries: 5

  init-db:
    image: git.tongyuan.cc:5050/mdocs/deployment/mdocs-editor:latest
    container_name: mdocs-editor-init-db
    volumes:
      - ./.env:/app/apps/mdocs-editor/.env
    env_file:
      - ./.env
    depends_on:
      db:
        condition: service_healthy
    entrypoint: ["prisma", "migrate", "deploy", "--schema", "/app/apps/mdocs-editor/prisma/schema.prisma"]
    restart: "no"

  app:
    image: git.tongyuan.cc:5050/mdocs/deployment/mdocs-editor:latest
    container_name: mdocs-editor-app
    volumes:
      - ./.env:/app/apps/mdocs-editor/.env
    env_file:
      - ./.env
    ports:
      - "3030:3030"
    environment:
      NEXT_TELEMETRY_DISABLED: "1"
    depends_on:
      init-db:
        condition: service_completed_successfully
      db:
        condition: service_healthy

volumes:
  postgres_data: {}
