import { describe, it, expect } from 'vitest';
import {
  buildMessageTree,
  insertMessageNode,
  toLinearRecord,
  initMessageTree,
  updateMessageNode,
  findMessageNodeById,
} from './messageTree';
import type { UIMessage } from 'ai';
import type { SwitchAction } from './switchActions';

/**
 * 消息类型，与数据库中的Message模型对应
 */
export interface Message {
  /** 消息的唯一标识符 */
  id: string;
  /** 前一条消息的ID，如果是第一条消息则为null */
  preId: string | null;
  /** 消息的角色：用户或助手 */
  role: 'user' | 'assistant';
  /** 消息的内容部分 */
  parts: Array<string | { type: string; text: string }>;
  /** 消息创建时间 */
  createdAt: Date;
  /** 消息的附件，如果没有则为null */
  attachments: Record<string, unknown> | null;
  /** 消息所属的聊天ID */
  chatId: string;
}

/**
 * 将Message转换为UIMessage
 * @param msg 需要转换的Message对象
 * @returns 转换后的UIMessage对象
 */
export function convertToUIMessage(msg: Message): UIMessage {
  return {
    id: msg.id,
    role: msg.role as UIMessage['role'],
    parts: msg.parts as UIMessage['parts'],
    content: '',
    createdAt: msg.createdAt,
  };
}

describe('buildMessageTree', () => {
  // 测试空消息列表
  it('should return empty tree when messages array is empty', () => {
    const root = buildMessageTree([], convertToUIMessage);

    expect(root.child).toEqual([]);
    expect(root.infoMap.size).toBe(1); // 只包含 null 的初始映射
    expect(root.infoMap.get(null)).toEqual({
      level: 0,
      parentMsgId: null,
      childCount: 0,
    });
  });

  // 测试单个消息节点
  it('should build single node tree correctly', () => {
    const messages: Message[] = [
      {
        id: '1',
        preId: null,
        role: 'user',
        parts: ['Hello'],
        createdAt: new Date(),
        attachments: null,
        chatId: '1',
      },
    ];

    const root = buildMessageTree(messages, convertToUIMessage);

    expect(root.child.length).toBe(1);
    expect(root.child[0].id).toBe('1');
    expect(root.child[0].child).toEqual([]);
    expect(root.infoMap.get('1')).toEqual({
      level: 1,
      parentMsgId: null,
      childCount: 0,
    });
  });

  // 测试线性消息链
  it('should build linear chain correctly', () => {
    const messages: Message[] = [
      {
        id: '1',
        preId: null,
        role: 'user',
        parts: ['First'],
        createdAt: new Date(),
        attachments: null,
        chatId: '1',
      },
      {
        id: '2',
        preId: '1',
        role: 'assistant',
        parts: ['Second'],
        createdAt: new Date(),
        attachments: null,
        chatId: '1',
      },
      {
        id: '3',
        preId: '2',
        role: 'user',
        parts: ['Third'],
        createdAt: new Date(),
        attachments: null,
        chatId: '1',
      },
    ];

    const root = buildMessageTree(messages, convertToUIMessage);

    // 验证树结构
    expect(root.child.length).toBe(1);
    expect(root.child[0].id).toBe('1');
    expect(root.child[0].child.length).toBe(1);
    expect(root.child[0].child[0].id).toBe('2');
    expect(root.child[0].child[0].child.length).toBe(1);
    expect(root.child[0].child[0].child[0].id).toBe('3');

    // 验证层级信息
    expect(root.infoMap.get('1')).toEqual({
      level: 1,
      parentMsgId: null,
      childCount: 1,
    });
    expect(root.infoMap.get('2')).toEqual({
      level: 2,
      parentMsgId: '1',
      childCount: 1,
    });
    expect(root.infoMap.get('3')).toEqual({
      level: 3,
      parentMsgId: '2',
      childCount: 0,
    });
  });

  // 测试分支消息树
  it('should handle branching messages correctly', () => {
    const messages: Message[] = [
      {
        id: '1',
        preId: null,
        role: 'user',
        parts: ['Question'],
        createdAt: new Date(),
        attachments: null,
        chatId: '1',
      },
      {
        id: '2',
        preId: '1',
        role: 'assistant',
        parts: ['Answer 1'],
        createdAt: new Date(),
        attachments: null,
        chatId: '1',
      },
      {
        id: '3',
        preId: '1',
        role: 'assistant',
        parts: ['Answer 2'],
        createdAt: new Date(),
        attachments: null,
        chatId: '1',
      },
    ];

    const root = buildMessageTree(messages, convertToUIMessage);

    // 验证树结构
    expect(root.child.length).toBe(1);
    expect(root.child[0].id).toBe('1');
    expect(root.child[0].child.length).toBe(2);
    expect(root.child[0].child.map((node) => node.id)).toEqual(['2', '3']);

    // 验证层级信息
    expect(root.infoMap.get('1')).toEqual({
      level: 1,
      parentMsgId: null,
      childCount: 2,
    });
    expect(root.infoMap.get('2')).toEqual({
      level: 2,
      parentMsgId: '1',
      childCount: 0,
    });
    expect(root.infoMap.get('3')).toEqual({
      level: 2,
      parentMsgId: '1',
      childCount: 0,
    });
  });

  // 测试消息转换功能
  it('should correctly convert message content using provided conversion function', () => {
    const testDate = new Date();
    const messages: Message[] = [
      {
        id: '1',
        preId: null,
        role: 'user',
        parts: ['Test content'],
        createdAt: testDate,
        attachments: null,
        chatId: '1',
      },
    ];

    const root = buildMessageTree(messages, convertToUIMessage);

    expect(root.child[0]).toMatchObject({
      id: '1',
      role: 'user',
      parts: ['Test content'],
      content: '',
      createdAt: testDate,
    });
  });
});

describe('insertMessageNode', () => {
  it('should insert node at root level when parentId is null', () => {
    const root = initMessageTree();
    const message: UIMessage = {
      id: '1',
      role: 'user',
      content: 'Hello',
      parts: [],
      createdAt: new Date(),
    };

    insertMessageNode(root, message, null);

    expect(root.child.length).toBe(1);
    expect(root.child[0].id).toBe('1');
    expect(root.infoMap.get('1')).toEqual({
      level: 1,
      parentMsgId: null,
      childCount: 0,
    });
    expect(root.infoMap.get(null)?.childCount).toBe(1);
  });

  it('should insert node as child of existing node', () => {
    const root = initMessageTree();
    const parent: UIMessage = {
      id: '1',
      role: 'user',
      content: 'Parent',
      parts: [],
      createdAt: new Date(),
    };
    const child: UIMessage = {
      id: '2',
      role: 'assistant',
      content: 'Child',
      parts: [],
      createdAt: new Date(),
    };

    insertMessageNode(root, parent, null);
    insertMessageNode(root, child, '1');

    expect(root.child[0].child.length).toBe(1);
    expect(root.child[0].child[0].id).toBe('2');
    expect(root.infoMap.get('2')).toEqual({
      level: 2,
      parentMsgId: '1',
      childCount: 0,
    });
    expect(root.infoMap.get('1')?.childCount).toBe(1);
  });

  it('should throw error when inserting duplicate message id', () => {
    const root = initMessageTree();
    const message: UIMessage = {
      id: '1',
      role: 'user',
      content: 'Hello',
      parts: [],
      createdAt: new Date(),
    };

    insertMessageNode(root, message, null);

    expect(() => {
      insertMessageNode(root, message, null);
    }).toThrow('Message with id 1 already exists in the tree');
  });

  it('should throw error when parent node not found', () => {
    const root = initMessageTree();
    const message: UIMessage = {
      id: '1',
      role: 'user',
      content: 'Hello',
      parts: [],
      createdAt: new Date(),
    };

    expect(() => {
      insertMessageNode(root, message, 'non-existent');
    }).toThrow('Parent node with id non-existent not found in the tree');
  });

  it('should handle empty string parentId as null', () => {
    const root = initMessageTree();
    const message: UIMessage = {
      id: '1',
      role: 'user',
      content: 'Hello',
      parts: [],
      createdAt: new Date(),
    };

    insertMessageNode(root, message, '');

    expect(root.child.length).toBe(1);
    expect(root.infoMap.get('1')).toEqual({
      level: 1,
      parentMsgId: null,
      childCount: 0,
    });
  });
});

describe('updateMessageNode', () => {
  it('should throw error when message not found', () => {
    const root = initMessageTree();
    const message: UIMessage = {
      id: 'nonexistent',
      role: 'user',
      content: 'Test',
      parts: [],
      createdAt: new Date(),
    };

    expect(() => {
      updateMessageNode(root, message);
    }).toThrow('Message with id nonexistent not found in the tree');
  });

  it('should update existing message content while preserving child nodes', () => {
    const root = initMessageTree();
    const parent: UIMessage = {
      id: '1',
      role: 'user',
      content: 'Original',
      parts: [],
      createdAt: new Date(),
    };
    const child: UIMessage = {
      id: '2',
      role: 'assistant',
      content: 'Response',
      parts: [],
      createdAt: new Date(),
    };

    // 先创建初始树结构
    insertMessageNode(root, parent, null);
    insertMessageNode(root, child, '1');

    // 准备更新消息
    const updatedMessage: UIMessage = {
      id: '1',
      role: 'user',
      content: 'Updated',
      parts: [],
      createdAt: new Date(),
    };

    updateMessageNode(root, updatedMessage);

    // 验证节点内容已更新
    const updatedNode = findMessageNodeById('1', root);
    expect(updatedNode).toBeTruthy();
    expect(updatedNode?.content).toBe('Updated');

    // 验证子节点关系保持不变
    expect(updatedNode?.child.length).toBe(1);
    expect(updatedNode?.child[0].id).toBe('2');
    expect(updatedNode?.child[0].content).toBe('Response');
  });

  it('should preserve tree structure after update', () => {
    const root = initMessageTree();

    // 创建一个三层的树结构
    const msg1: UIMessage = {
      id: '1',
      role: 'user',
      content: 'First',
      parts: [],
      createdAt: new Date(),
    };
    const msg2: UIMessage = {
      id: '2',
      role: 'assistant',
      content: 'Second',
      parts: [],
      createdAt: new Date(),
    };
    const msg3: UIMessage = {
      id: '3',
      role: 'user',
      content: 'Third',
      parts: [],
      createdAt: new Date(),
    };

    insertMessageNode(root, msg1, null);
    insertMessageNode(root, msg2, '1');
    insertMessageNode(root, msg3, '2');

    // 更新中间节点
    const updatedMsg2: UIMessage = {
      id: '2',
      role: 'assistant',
      content: 'Updated Second',
      parts: [],
      createdAt: new Date(),
    };

    updateMessageNode(root, updatedMsg2);

    // 验证整个树结构保持完整
    expect(root.child.length).toBe(1);
    expect(root.child[0].id).toBe('1');
    expect(root.child[0].child.length).toBe(1);
    expect(root.child[0].child[0].id).toBe('2');
    expect(root.child[0].child[0].content).toBe('Updated Second');
    expect(root.child[0].child[0].child.length).toBe(1);
    expect(root.child[0].child[0].child[0].id).toBe('3');

    // 验证 infoMap 保持不变
    expect(root.infoMap.get('2')).toEqual({
      level: 2,
      parentMsgId: '1',
      childCount: 1,
    });
  });
});
describe('toLinearRecord', () => {
  it('should return empty array for empty tree', () => {
    const root = initMessageTree();
    const switchActions: SwitchAction[] = [];
    const result = toLinearRecord(root, switchActions);
    expect(result).toEqual([]);
  });

  it('should convert simple linear chain without switch actions', () => {
    const root = initMessageTree();
    const msg1: UIMessage = {
      id: '1',
      role: 'user',
      content: 'Hello',
      parts: [],
      createdAt: new Date(),
    };
    const msg2: UIMessage = {
      id: '2',
      role: 'assistant',
      content: 'Hi',
      parts: [],
      createdAt: new Date(),
    };

    insertMessageNode(root, msg1, null);
    insertMessageNode(root, msg2, '1');

    const result = toLinearRecord(root, []);
    expect(result).toHaveLength(2);
    expect(result[0].id).toBe('1');
    expect(result[1].id).toBe('2');
  });

  it('should handle branching with switch actions', () => {
    const root = initMessageTree();
    // 创建一个分支结构:
    //     1
    //    / \
    //   2   3
    //  /     \
    // 4       5
    const messages = [
      { id: '1', role: 'user', content: 'Root', parts: [], createdAt: new Date() },
      { id: '2', role: 'assistant', content: 'Branch 1', parts: [], createdAt: new Date() },
      { id: '3', role: 'assistant', content: 'Branch 2', parts: [], createdAt: new Date() },
      { id: '4', role: 'user', content: 'Leaf 1', parts: [], createdAt: new Date() },
      { id: '5', role: 'user', content: 'Leaf 2', parts: [], createdAt: new Date() },
    ] as UIMessage[];

    insertMessageNode(root, messages[0], null);
    insertMessageNode(root, messages[1], '1');
    insertMessageNode(root, messages[2], '1');
    insertMessageNode(root, messages[3], '2');
    insertMessageNode(root, messages[4], '3');

    // 测试默认路径（选择第一个分支）
    let result = toLinearRecord(root, []);
    expect(result.map((msg) => msg.id)).toEqual(['1', '2', '4']);

    // 测试切换到第二个分支
    const switchActions: SwitchAction[] = [{ messageId: '1', childIndex: 1 }];
    result = toLinearRecord(root, switchActions);
    expect(result.map((msg) => msg.id)).toEqual(['1', '3', '5']);
  });

  it('should handle multiple switch actions', () => {
    const root = initMessageTree();
    // 创建一个复杂的分支结构:
    //      1
    //     / \
    //    2   3
    //   / \   \
    //  4   5   6
    const messages = [
      { id: '1', role: 'user', content: 'Root', parts: [], createdAt: new Date() },
      { id: '2', role: 'assistant', content: 'Branch 1', parts: [], createdAt: new Date() },
      { id: '3', role: 'assistant', content: 'Branch 2', parts: [], createdAt: new Date() },
      { id: '4', role: 'user', content: 'Leaf 1', parts: [], createdAt: new Date() },
      { id: '5', role: 'user', content: 'Leaf 2', parts: [], createdAt: new Date() },
      { id: '6', role: 'user', content: 'Leaf 3', parts: [], createdAt: new Date() },
    ] as UIMessage[];

    insertMessageNode(root, messages[0], null);
    insertMessageNode(root, messages[1], '1');
    insertMessageNode(root, messages[2], '1');
    insertMessageNode(root, messages[3], '2');
    insertMessageNode(root, messages[4], '2');
    insertMessageNode(root, messages[5], '3');

    // 测试多个切换动作
    const switchActions: SwitchAction[] = [
      { messageId: '1', childIndex: 0 },
      { messageId: '2', childIndex: 1 },
    ];
    const result = toLinearRecord(root, switchActions);
    expect(result.map((msg) => msg.id)).toEqual(['1', '2', '5']);
  });

  it('should ignore invalid switch actions', () => {
    const root = initMessageTree();
    const msg1: UIMessage = {
      id: '1',
      role: 'user',
      content: 'Hello',
      parts: [],
      createdAt: new Date(),
    };
    const msg2: UIMessage = {
      id: '2',
      role: 'assistant',
      content: 'Hi',
      parts: [],
      createdAt: new Date(),
    };

    insertMessageNode(root, msg1, null);
    insertMessageNode(root, msg2, '1');

    // 测试无效的childIndex
    const invalidIndexAction: SwitchAction[] = [{ messageId: '1', childIndex: 999 }];
    const result1 = toLinearRecord(root, invalidIndexAction);
    expect(result1.map((msg) => msg.id)).toEqual(['1', '2']);

    // 测试无效的messageId
    const invalidIdAction: SwitchAction[] = [{ messageId: 'nonexistent', childIndex: 0 }];
    const result2 = toLinearRecord(root, invalidIdAction);
    expect(result2.map((msg) => msg.id)).toEqual(['1', '2']);
  });
});
