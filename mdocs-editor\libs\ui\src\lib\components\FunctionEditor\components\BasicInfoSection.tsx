import React from "react";
import { Label } from "../../ui/label";
import { Input } from "../../ui/input";
import { I18nInput } from "./ui/I18nInput";
import { useFunctionContent } from "../hooks/useFunctionContent";
import type { FunctionContent } from "../FunctionEditor";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../../ui/select";
import { Options } from "../views/FormView";

export interface BasicInfoSectionProps {
  /** 函数内容数据 */
  content: Partial<FunctionContent>;
  /** 内容更新回调函数 */
  onContentChange: (updates: Partial<FunctionContent>) => void;
  /** 是否处于加载状态 */
  isLoading?: boolean;
  /** 可选的版本列表 */
  availableVersions?: Options[];
  /** 函数名称 */
  functionName: string;
}

/**
 * 基本信息部分组件
 *
 * 负责编辑函数的基本信息，包括：
 * - 摘要（中英文）
 * - 引入版本
 */
export const BasicInfoSection: React.FC<BasicInfoSectionProps> = ({
  content,
  onContentChange,
  isLoading = false,
  availableVersions = [],
  functionName,
}) => {
  const { updateField } = useFunctionContent({
    content,
    onContentChange,
  });

  // 选择版本
  const selectVersion = (versionValue: string) => {
    updateField('since', versionValue);
  };

  return (
    <div className="space-y-6">
      {/* 函数名称和引入版本 - 一行显示 */}
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label className="text-base font-medium">函数名称</Label>
          <Input
            value={functionName}
            onChange={(e) => updateField('name', e.target.value)}
            placeholder="请输入函数名称"
            disabled={isLoading}
            className="text-sm"
          />
        </div>
        <div className="space-y-2">
          <Label className="text-base font-medium">
            引入版本<span className="text-red-500 ml-1">*</span>
          </Label>
          <Select
            value={content.since}
            disabled={isLoading}
            onValueChange={(value: string) => {
              selectVersion(value)
            }}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder={"选择版本"} >
              </SelectValue>
            </SelectTrigger>

            <SelectContent>
              {availableVersions.map((opt) => (
                <SelectItem key={opt.value} value={opt.value} className="py-3">
                  <div className="flex flex-col">
                    <span className="font-medium">{opt.label}</span>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <span className="text-xs text-gray-500">
          函数从哪一 Syslab 版本中引入
        </span>
      </div>
      {/* 函数摘要 */}
      <I18nInput
        label="函数摘要"
        value={content.summary}
        onChange={(value) => updateField('summary', value)}
        disabled={isLoading}
        type="textarea"
        rows={3}
        required={true}
        placeholderZh="函数的简短描述"
        placeholderEn="Brief function description"
        idPrefix="summary"
      />
    </div>
  );
};
