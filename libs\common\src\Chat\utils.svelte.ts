import { untrack } from 'svelte';
import { SvelteMap } from 'svelte/reactivity';

// 访问时若key不存在自动创建value对象的SvelteMap，需要包装在$state中使用
export class KeyedStore<T> extends SvelteMap<string, T> {
  #itemConstructor: new () => T;

  constructor(
    itemConstructor: new () => T,
    value?: Iterable<readonly [string, T]> | null | undefined,
  ) {
    super(value);
    this.#itemConstructor = itemConstructor;
  }

  get(key: string): T {
    const test =
      super.get(key) ??
      // ⚠️删除untrack会导致：Svelte error: state_unsafe_mutation Updating state inside a derived or a template expression is forbidden.
      // 因为Chat类会在$derive中使用，而$derive中不允许更新状态，在这里通过untrack来取消svelte对状态更新的观测。
      // ⚠️此外，虽然KeyedStore直接继承具有响应性的SvelteMap，但由于此处的untrack取消了状态更新的观测，从KeyedStore直接$derived实际上失去了响应性
      // 你需要通过$state<KeyedStore>()来包装使其恢复响应性。
      // Untrack here because this is technically a state mutation, meaning
      // deriveds downstream would fail. Because this is idempotent (even
      // though it's not pure), it's safe.
      untrack(() => this.set(key, new this.#itemConstructor())).get(key)!;

    return test;
  }
}
