{"[typescript]": {"editor.defaultFormatter": "biomejs.biome", "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.biome": "explicit", "source.organizeImports.biome": "explicit"}}, "[typescriptreact]": {"editor.defaultFormatter": "biomejs.biome", "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.biome": "explicit", "source.organizeImports.biome": "explicit"}}, "[javascript]": {"editor.defaultFormatter": "biomejs.biome", "editor.formatOnSave": true}, "[javascriptreact]": {"editor.defaultFormatter": "biomejs.biome", "editor.formatOnSave": true}, "[json]": {"editor.defaultFormatter": "vscode.json-language-features", "editor.formatOnSave": true, "editor.tabSize": 2}, "[jsonc]": {"editor.defaultFormatter": "vscode.json-language-features", "editor.formatOnSave": true, "editor.tabSize": 2}}