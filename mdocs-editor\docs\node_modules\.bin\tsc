#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/Desktop/web/mdocs-editor/node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/bin/node_modules:/mnt/c/Users/<USER>/Desktop/web/mdocs-editor/node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/node_modules:/mnt/c/Users/<USER>/Desktop/web/mdocs-editor/node_modules/.pnpm/typescript@5.8.3/node_modules:/mnt/c/Users/<USER>/Desktop/web/mdocs-editor/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/Desktop/web/mdocs-editor/node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/bin/node_modules:/mnt/c/Users/<USER>/Desktop/web/mdocs-editor/node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/node_modules:/mnt/c/Users/<USER>/Desktop/web/mdocs-editor/node_modules/.pnpm/typescript@5.8.3/node_modules:/mnt/c/Users/<USER>/Desktop/web/mdocs-editor/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/bin/tsc" "$@"
else
  exec node  "$basedir/../../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/bin/tsc" "$@"
fi
