<script lang="ts">
  import { Button } from '$lib/components/ui/button';
  import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
  } from '$lib/components/ui/dropdown-menu';
  import CircleCheck from '@lucide/svelte/icons/circle-check';
  import ChevronDown from '@lucide/svelte/icons/chevron-down';
  import { cn } from '$lib/utils';
  import type { ModelConfig } from '@askme/lib-common';
  import type { ClassValue } from 'svelte/elements';
  import { getUserPreferancesCookiesContext } from '@askme/lib-common';

  interface Props {
    class?: ClassValue;
    modelConfigs: ModelConfig[];
  }
  const { class: className, modelConfigs }: Props = $props();

  let open = $state(false);

  const userPreferances = getUserPreferancesCookiesContext();
  const selectedChatModel = $derived(userPreferances.value.selectedModel);
  const selectedChatModelDetails = $derived(
    modelConfigs.find((model) => model.id === selectedChatModel),
  );
</script>

<DropdownMenu {open} onOpenChange={(val) => (open = val)}>
  <DropdownMenuTrigger>
    {#snippet child({ props })}
      <Button
        {...props}
        variant="outline"
        class={cn(
          'data-[state=open]:bg-accent data-[state=open]:text-accent-foreground w-fit md:h-[34px] md:px-2',
          className,
        )}
      >
        {selectedChatModelDetails?.name}
        <ChevronDown />
      </Button>
    {/snippet}
  </DropdownMenuTrigger>
  <DropdownMenuContent align="start" class="min-w-[300px]">
    {#each modelConfigs as chatModel (chatModel.id)}
      <DropdownMenuItem
        onSelect={() => {
          open = false;
          userPreferances.value = { ...userPreferances.value, selectedModel: chatModel.id };
        }}
        class="group/item flex flex-row items-center justify-between gap-4"
        data-active={chatModel.id === selectedChatModel}
      >
        <div class="flex flex-col items-start gap-1">
          <div>{chatModel.name}</div>
          <div class="text-muted-foreground text-xs">
            {chatModel.description}
          </div>
        </div>

        <div
          class="text-foreground dark:text-foreground opacity-0 group-data-[active=true]/item:opacity-100"
        >
          <CircleCheck />
        </div>
      </DropdownMenuItem>
    {/each}
  </DropdownMenuContent>
</DropdownMenu>
