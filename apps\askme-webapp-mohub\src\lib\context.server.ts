import { PrismaClient } from '$lib/generated/prisma';
import { GitLab } from 'arctic';
import type { ServerResources } from './types';
import * as Minio from 'minio';
import { Langfuse } from 'langfuse';
import { config } from './config.server';

function buildServerResources(): ServerResources {
  const prisma = new PrismaClient({
    datasourceUrl: config.db.url,
  });

  let oauth = undefined;
  if (config.oauth.provider.toUpperCase() === 'GITLAB') {
    oauth = {
      // TODO：根据需要重写实现（换成自定义的api endpoint再构造OAuth2Client对象即可）
      // https://github.com/pilcrowonpaper/arctic/blob/main/src/providers/gitlab.ts
      gitlab: new GitLab(
        config.oauth.baseurl,
        config.oauth.application_id,
        config.oauth.application_secret,
        config.oauth.redirect_uri,
      ),
    };
  }
  const minio = new Minio.Client({
    endPoint: config.s3.hostname,
    port: config.s3.port,
    useSSL: config.s3.ssl,
    accessKey: config.s3.access_key,
    secretKey: config.s3.secret_key,
    pathStyle: config.s3.isPathStyle,
  });

  const langfuse = new Langfuse({
    secretKey: config.langfuse.secret_token,
    publicKey: config.langfuse.access_token,
    baseUrl: config.langfuse.baseurl,
  });

  return {
    prisma,
    oauth,
    s3: minio,
    langfuse,
  };
}

const serverCtx = buildServerResources();
export default serverCtx;
