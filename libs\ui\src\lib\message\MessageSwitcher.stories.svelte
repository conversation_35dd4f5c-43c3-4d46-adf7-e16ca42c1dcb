<script module>
  //    👆 notice the module context, defineMeta does not work in a regular <script> tag - instance
  import { defineMeta } from '@storybook/addon-svelte-csf';
  import MessageSwitcher from './MessageSwitcher.svelte';

  //      👇 Get the Story component from the return value
  const { Story } = defineMeta({
    title: 'UI/MessageSwitcher',
    component: MessageSwitcher,
    decorators: [
      /* ... */
    ],
    parameters: {
      /* ... */
    },
  });
</script>

<Story
  name="Default"
  args={{
    messageId: '2',
  }}
/>
