import type { PageServerLoad } from './$types';
import { getRandomElements } from '$lib/utils';
import type { SuggestedActionModel } from '@askme/lib-common';
import { hotReload } from '$lib/hotRelod';

export const load: PageServerLoad = async ({}) => {
  const suggestedActions = getRandomElements(
    hotReload.get_demo_questions(),
    2,
  ) as SuggestedActionModel[];

  return {
    suggestedActions,
  };
};
