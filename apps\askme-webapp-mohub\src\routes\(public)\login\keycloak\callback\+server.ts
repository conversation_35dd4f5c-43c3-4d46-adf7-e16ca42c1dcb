import {
  generateSessionToken,
  createSession,
  setSessionTokenCookie,
  createUserIfNotExists,
} from '$lib/baseAuth';
import { config } from '$lib/config.server';
import type { RequestEvent } from '@sveltejs/kit';
import * as LogFormat from '@askme/lib-common/logformat';
import { error } from '@sveltejs/kit';
import serverCtx from '$lib/context.server';
import { getUserInfoByOAuthProvider, buildOauthAuthorizationHeader } from '$lib/oauthUtils';

// 处理Keycloak OAuth2回调
export async function GET(event: RequestEvent): Promise<Response> {
  if (config.oauth.provider.toUpperCase() !== 'KEYCLOAK') {
    return new Response('Unauthorized', {
      status: 401,
    });
  }

  // 验证Oauth2 callback 参数
  const code = event.url.searchParams.get('code');
  if (!code) {
    console.debug(
      LogFormat.debug('Missing authorization code in OAuth2 callback query parameters.'),
    );
    return new Response('Unauthorized', {
      status: 401,
    });
  }

  // auth code换access token
  let tokenResponseJson: {
    access_token: string;
    expires_in: number;
    refresh_token: string;
    token_type: string;
    scope?: string;
  };

  try {
    const accessTokenResponse = await fetch(config.oauth.token_url, {
      method: 'POST',
      headers: {
        Authorization: buildOauthAuthorizationHeader(
          config.oauth.application_id,
          config.oauth.application_secret,
        ),
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        grant_type: 'authorization_code',
        code: code,
        redirect_uri: config.oauth.redirect_uri,
      }).toString(),
    });
    tokenResponseJson = await accessTokenResponse.json();
    if (!accessTokenResponse.ok) {
      throw new Error('Failed to validate authorization code.');
    }
  } catch {
    console.debug(LogFormat.debug('Failed to validate authorization code.'));
    // Invalid code or client credentials
    return new Response('Unauthorized', {
      status: 401,
    });
  }

  let userInfo;
  // 用token请求用户信息
  try {
    userInfo = await getUserInfoByOAuthProvider('KEYCLOAK', tokenResponseJson.access_token);
  } catch (error) {
    console.error(LogFormat.error('Failed to get user info.' + error + 'Provider:KEYCLOAK'));
    return new Response('Server Error', {
      status: 500,
    });
  }

  let user;
  try {
    user = await createUserIfNotExists(serverCtx.prisma, userInfo, 'KEYCLOAK');
  } catch (e) {
    console.error(LogFormat.error('Failed to create user.' + e));
    return error(500, 'Server Error');
  }

  const MS_IN_S = 1000;
  // 创建会话，设置cookie
  const sessionToken = generateSessionToken();
  const session = await createSession(
    serverCtx.prisma,
    sessionToken,
    tokenResponseJson.access_token,
    tokenResponseJson.refresh_token,
    new Date(Date.now() + tokenResponseJson.expires_in * MS_IN_S),
    user.id,
    false,
  );
  setSessionTokenCookie(event, sessionToken, session.expiresAt);

  return new Response(null, {
    status: 302,
    headers: {
      Location: '/',
    },
  });
}
