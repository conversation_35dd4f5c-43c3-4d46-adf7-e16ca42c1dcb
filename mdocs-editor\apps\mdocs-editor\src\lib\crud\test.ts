import { PrismaClient } from "../generated/prisma";
import * as util from "./index";

async function testCrud() {
  const prisma = new PrismaClient();
  try {
    console.log("Testing CRUD operations...");

    //测试创建
    // const library = await createEmptyLibraryHistory(prisma, "Test Library", {
    //   version: "1.0.10",
    //   creator: "admin",
    //   createdAt: new Date(),
    //   extra: `
    //   {
    //     "summary":
    //     {
    //       "zh": "图形",
    //       "en": "Graph"
    //     }
    //   }
    //   `,
    // });
    // const updatedLibrary = await setLatestInLibrary(prisma, "1.0.10", "Test Library");
    // console.log("Library history created:", library.history);
    // console.log("Library updated:", updatedLibrary);
    const res = await util.createEmptyLibraryHistoryWithLatest(prisma, "Test Library", {
      version: "1.0.20",
      creator: "admin",
      createdAt: new Date(),
      extra: `
      {
        "summary":
        {
          "zh": "图形",
          "en": "Graph"
        }
      }
      `,
    });
    console.log("Library history created with latest:", res);
  } catch (error) {
    console.error("Test failed:", error);
  } finally {
    await prisma.$disconnect();
  }
}

testCrud();
