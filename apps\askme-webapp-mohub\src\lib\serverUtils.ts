import { <PERSON>uff<PERSON> } from 'buffer';
import * as Stream from 'stream';
import { Readable } from 'stream';
import crypto from 'crypto';
import { PrismaClient, Prisma } from '$lib/generated/prisma';
import * as LogFormat from '@askme/lib-common/logformat';
import type { UIMessage, Attachment } from 'ai';
import type { RequestS3Config, ContentModerationResult, DBChatSearchResult } from '$lib/types';
import type { RAGSearchResponse, RAGSearchResult, ExternalLinkPart } from '@askme/lib-common';
import { getFileBlobformS3, buildMultiModalLLMRequestFileURL } from '$lib/s3utils';
import type { Role } from '$lib/generated/prisma';
import { config } from '$lib/config.server';

// 这个文件专门用来放服务端的工具函数
// ⚠️浏览器中有些node module是导入不了的

export function isUUID(uuid: string): boolean {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(uuid);
}

export function streamToBuffer(stream: Stream.Readable): Promise<Buffer> {
  return new Promise((resolve, reject) => {
    const chunks: Buffer[] = [];
    stream.on('data', (chunk) => chunks.push(Buffer.from(chunk)));
    stream.on('end', () => resolve(Buffer.concat(chunks)));
    stream.on('error', reject);
  });
}

export function createChunkedStream(
  webStream: ReadableStream<Uint8Array>,
  chunkSize = 512 * 1024,
): Readable {
  const reader = webStream.getReader();

  return new Readable({
    async read() {
      try {
        let totalRead = 0;

        while (totalRead < chunkSize) {
          const { done, value } = await reader.read();
          if (done) {
            this.push(null);
            break;
          }

          if (value) {
            this.push(value);
            totalRead += value.length;
          }
        }
      } catch (err) {
        this.destroy(err as Error);
      }
    },
  });
}

/**
 * 阿里云内容安全增强版签名
 * @param text 待签名字符串
 * @param secret 阿里云accessKeySecret密钥
 * @returns 签名
 */
function contentAliyunModerationPlusSign(text: string, secret: string): string {
  const hmac = crypto.createHmac('sha1', `${secret}&`);
  hmac.update(text, 'utf8');
  const signed = hmac.digest(); // 返回的是 Buffer（也就是 byte[]）
  return signed.toString('base64');
}

/**
 * 根据URLSearchParams构造待签名字符串， 用于计算签名, URLSearchParams的字段要预先字典排序
 * @param params 字典排序的URLSearchParams
 * @returns 待签名字符串
 */
function constructAliyunContentModerationPlusStringToSign(params: URLSearchParams): string {
  return `POST&${encodeURIComponent('/')}&${encodeURIComponent(params.toString())}`;
}

/**
 * 构造用户llm请求审查QueryParams
 * @param contentStr 待检测内容
 * @param accessKeyId 访问密钥ID
 * @param accessKeySecret 访问密钥
 * @returns 已加入签名，可直接发送到阿里云的URLSearchParams
 */
export function buildAliyunLLMQueryModerationParams(
  contentStr: string,
  accessKeyId: string,
  accessKeySecret: string,
): URLSearchParams {
  // ⚠️ 已字典排序
  const params = new URLSearchParams({
    AccessKeyId: accessKeyId,
    Action: 'TextModerationPlus',
    Format: 'JSON',
    Service: 'llm_query_moderation',
    ServiceParameters: JSON.stringify({
      content: contentStr,
    }),
    SignatureMethod: 'HMAC-SHA1',
    SignatureNonce: crypto.randomUUID(),
    SignatureVersion: '1.0',
    Timestamp: new Date().toISOString(),
    Version: '2022-03-02',
  });
  const stringToSign = constructAliyunContentModerationPlusStringToSign(params);
  const signedStr = contentAliyunModerationPlusSign(stringToSign, accessKeySecret);
  params.set('Signature', signedStr);
  return params;
}

/**
 * 检查数据库里有没有chat，如果没有，就创建一个新chat
 * @param prismaClient prisma client
 * @param chatId chat id
 * @param userId user id
 */
export async function createChatIfNotExists(
  prisma: PrismaClient,
  chatId: string,
  userId: number,
  title: string,
): Promise<void> {
  try {
    const chat = await prisma.chat.findUnique({
      where: {
        id: chatId,
        deletedAt: null,
      },
    });
    if (!chat) {
      await prisma.chat.create({
        data: {
          id: chatId,
          title,
          creatorId: userId,
        },
      });
    }
  } catch (error) {
    console.error(LogFormat.error('Failed to create a new chat: ' + error));
  }
}

/**
 * 检查数据库里有没有这个message，如果没有，就创建一个新message
 * @param prisma prisma client
 * @param lastMessage
 * @param lastBotMessage
 * @param chatId
 */
export async function createMessageIfNotExists(
  prisma: PrismaClient,
  lastMessage: UIMessage,
  preId: string | undefined,
  chatId: string,
  role: Role,
): Promise<void> {
  try {
    const message = await prisma.message.findFirst({
      where: {
        id: lastMessage.id,
      },
    });
    if (!message) {
      await prisma.message.create({
        data: {
          id: lastMessage.id,
          preId: preId ? preId : null, //如果是第一条用户消息，则preId为空，如果前面有bot消息，则preId为bot消息id
          parts: lastMessage!.parts as Prisma.JsonArray,
          attachments: JSON.stringify(lastMessage.experimental_attachments),
          annotations: lastMessage.annotations,
          role,
          chatId,
        },
      });
    }
  } catch (error) {
    console.error(LogFormat.error('Message failed to write to database: ' + error));
  }
}

/**
 * 构造请求LLM store的RAG接口的form data
 * @param s3Config s3配置
 * @param message 消息
 * @param storeName store名称
 * @param topK top k
 * @param attachments 附件
 */
export async function buildLLMStoreRAGFormData(
  s3Config: RequestS3Config,
  message: string,
  storeName: string,
  topK: number,
  attachments: Attachment[] | undefined,
): Promise<FormData> {
  const formData = new FormData();
  formData.append('message', message);
  formData.append('store_name', storeName);
  formData.append('top_k', topK.toString());
  if (attachments && attachments.length > 0) {
    for (const a of attachments) {
      try {
        const blob = await getFileBlobformS3(
          s3Config.minioClient,
          a,
          s3Config.userId,
          s3Config.bucketName,
          s3Config.prefix,
        );
        formData.append('files', blob, a.url);
      } catch (error) {
        console.error(LogFormat.error('Failed to get object from minio when rag: ' + error));
      }
    }
  }
  return formData;
}

/**
 * 向llm store的RAG接口请求结果
 * @param url llm store base url
 * @param accessToken 用于访问llm store的oauth access token
 * @param formData rag请求form data
 * @returns 搜索结果
 */
export async function fetchLLMStoreRAGResult(
  llmStoreBaseurl: string,
  accessToken: string,
  formData: FormData,
): Promise<RAGSearchResult[]> {
  const response = await fetch(`${llmStoreBaseurl}/mds/search`, {
    method: 'POST',
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
    body: formData,
  });

  if (!response.ok) {
    throw Error('RAG Request failed.');
  }
  const result: RAGSearchResponse = await response.json();
  return result.results;
}

/**
 * 从rag请求结果数组构造RAG提示词
 * @param ragResult 搜索结果
 * @returns RAG提示词
 */
export function buildRagPromptFromRagResult(ragResult: RAGSearchResult[]): string {
  return ragResult.reduce((acc, curr) => {
    return acc + `参考资料：\n${curr.content}\n\n`;
  }, '');
}

/**
 * 从rag请求结果数组构造溯源链接
 * @param ragResult 搜索结果
 * @param isPublicDeployment 是否公网部署
 * @param referenceSourceNumber 溯源链接数量
 * @returns 溯源链接数组
 */
export function buildSourceLinkPartsFromRagResult(
  ragResult: RAGSearchResult[],
  isPublicDeployment: boolean,
  referenceSourceNumber: number,
): ExternalLinkPart[] {
  const sourceLinkArray: ExternalLinkPart[] = [];
  // 过滤什么内容应该给溯源链接
  const validRagSourceLinkResult = ragResult.filter((item) => {
    const contentHasChinese = /[\u4e00-\u9fa5]/.test(item.content);
    const hasLink = item.metadata.external_link || item.metadata.internal_link;
    const isImage = item.metadata.image_url;
    const hasInternalLink = item.metadata.internal_link ? true : false;
    const hasInternalLinkWhenPublicDeployment = isPublicDeployment ? hasInternalLink : false;
    // 有链接，不是图片，且公网部署时没有内部链接，内容包含中文
    return hasLink && !isImage && !hasInternalLinkWhenPublicDeployment && contentHasChinese;
  });

  const seenTitles = new Set<string>();

  // 构造溯源链接列表
  for (const item of validRagSourceLinkResult) {
    if (sourceLinkArray.length >= referenceSourceNumber) break;

    const title = item.metadata.title ?? item.content.slice(0, 10) + '...'; // 没有标题的话截取正文生成一个默认标题
    if (seenTitles.has(title)) continue; // 避免重复标题

    seenTitles.add(title);
    sourceLinkArray.push({
      type: 'externalLink',
      url: item.metadata.external_link ?? item.metadata.internal_link ?? '', // 内部链接和外部链接总有一个，否则会被上面的逻辑过滤（所以理论上值不会为最后fallback的空字符串）
      title,
      chunkText: item.content,
    });
  }
  return sourceLinkArray;
}

/**
 * 根据部署环境处理附件，构造正确格式的附件字段，将UIMessage中的附件字段转换为LLM请求的实际文件url（base64编码或s3预签名url）
 *
 * @param s3Config s3配置
 * @param deploymentTarget 部署环境
 * @param message 用户消息
 * @returns
 */
export async function fillAttachmentByDeploymentTarget(
  s3Config: RequestS3Config,
  deploymentTarget: string,
  attachments: Attachment[],
): Promise<Attachment[]> {
  const newAttachments: Attachment[] = [];
  for (const a of attachments) {
    try {
      const fileurl = await buildMultiModalLLMRequestFileURL(s3Config, deploymentTarget, a);
      newAttachments.push({
        contentType: a.contentType,
        url: fileurl,
      } as Attachment);
    } catch (error) {
      console.error(LogFormat.error('Failed to covert attachment url: ' + error));
    }
  }
  return newAttachments;
}

/**
 * 向阿里云内容安全增强版发送请求，获取审查结果
 * @param baseurl 阿里云内容安全增强版baseurl
 * @param params URLSearchParams
 * @returns
 */
export async function fetchAliyunLLMQueryModerationResult(
  baseurl: string,
  params: URLSearchParams,
) {
  let result;
  try {
    const response = await fetch(`${baseurl}/?${params.toString()}`, {
      method: 'POST',
    });
    if (!response.ok) {
      throw new Error('Response not 2xx');
    }
    result = await response.json();
    // ⚠️注意阿里云的响应所有情况http请求均返回200，真实的错误信息在Code字段中
    if (result.Code !== 200) {
      throw new Error('Response not 2xx');
    }
  } catch (error) {
    console.error(
      LogFormat.error('Failed to get response from aliyun content moderation plus: ' + error),
    );
  }
  return result;
}

/**
 * 检查内容是否通过内容安全审查，并且为不同内容安全提供商返回规范化的审查结果，为空说明没有检查或检查失败
 * @param str 待审查内容
 * @returns 审查结果ContentModerationResult
 */
export async function fetchContentModerationResult(
  str: string,
): Promise<ContentModerationResult | null> {
  if (!config.content_moderation) {
    return null;
  }
  if (config.content_moderation.provider === 'ALIYUN') {
    const params = buildAliyunLLMQueryModerationParams(
      str,
      config.content_moderation.access_key_id,
      config.content_moderation.access_key_secret,
    );
    const result = await fetchAliyunLLMQueryModerationResult(
      config.content_moderation.baseurl,
      params,
    );
    if (result) {
      const data = result.Data;
      if (data.RiskLevel !== 'none') {
        return {
          isRiskQuery: true,
          riskQueryAnswer: data.Advice
            ? data.Advice[0].Answer
            : '对不起，我不能回复这个问题。如果您有其他和MWORKS产品相关的问题，我很乐意回答。',
        } as ContentModerationResult;
      }
      return {
        isRiskQuery: false,
        riskQueryAnswer: '',
      };
    } else {
      console.error(LogFormat.error('Failed to get result from aliyun content moderation plus'));
      return null;
    }
  } else {
    // 一般不会运行到这里
    console.error(LogFormat.error('Unsupported content moderation provider'));
    return null;
  }
}

/**
 * 从数据库中搜索聊天记录。
 * @param prisma prisma客户端
 * @param searchContent 搜索内容
 * @param userId 用户ID
 * @param page 页码
 * @param pageSize 每页数量
 * @returns
 */
export async function searchChatFromDB(
  prisma: PrismaClient,
  searchContent: string,
  userId: number,
  page: number = 1,
  pageSize: number = 10,
): Promise<DBChatSearchResult[]> {
  // 这个函数的目的是从用户的chat title或历史message文本中搜索匹配的字符串
  // DBChatSearchResult的matchedText会返回匹配完整的chat title或message文本

  // SQL逻辑：
  // 首先对Chat表和Message表的联表查询，取出符合如下条件的结果：
  // 1. Chat表的creatorId等于userId
  // 2. Message表的parts字段的json中，类型为text的对象内，有文本包含searchContent，或者Chat表的title字段文本包含searchContent
  // 接着对相同的chatId的结果进行去重
  // 然后对符合条件的结果，提取出匹配的字符串：text part的string或chat title二选一
  // 最后按时间顺序排序，并且取出指定分页范围的结果
  const offset = (page - 1) * pageSize;
  const searchTerm = '%' + searchContent + '%';

  return await prisma.$queryRaw`
    SELECT * FROM (
      SELECT DISTINCT ON (m."chatId")
        COALESCE(
          (
            SELECT elem->>'text'
            FROM jsonb_array_elements(m."parts") elem
            WHERE elem->>'type' = 'text'
              AND elem->>'text' ILIKE ${searchTerm}
            LIMIT 1
          ),
          c."title"
        ) as "matchedText", 
        m."chatId" as "chatId", 
        c."title" as "chatTitle",
        c."createdAt" as "createdAt"
      FROM "Message" m
      JOIN "Chat" c ON m."chatId" = c.id
      WHERE c."creatorId" = ${userId}
        AND (EXISTS (
          SELECT 1
          FROM jsonb_array_elements(m."parts") elem
          WHERE elem->>'type' = 'text'
            AND elem->>'text' ILIKE ${searchTerm}
        ) OR c."title" ILIKE ${searchTerm})
      ORDER BY m."chatId", c."createdAt" DESC
    ) AS subquery
    ORDER BY "createdAt" DESC
    LIMIT ${pageSize} OFFSET ${offset}
  `;
}
