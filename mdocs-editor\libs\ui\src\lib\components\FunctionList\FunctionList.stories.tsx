import type { Meta, StoryObj } from '@storybook/react-vite';
import { useState } from 'react';
import { FunctionList } from './FunctionList';
import type { FunctionItem } from './FunctionListContent';


const meta: Meta<typeof FunctionList> = {
  title: 'Components/FunctionList',
  component: FunctionList,
  parameters: {
    layout: 'fullscreen',
    docs: {
      description: {
        component: '函数列表组件，用于显示和管理函数库中的函数。支持搜索、选择、创建、导入、导出和删除功能。'
      }
    }
  },
  argTypes: {
    selectedFuncStr: {
      control: 'text',
      description: '当前选中的函数名'
    },
    libraryName: {
      control: 'text',
      description: '函数库名称'
    },
    className: {
      control: 'text',
      description: '自定义 CSS 类名'
    },
  },
  tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof FunctionList>;

// 交互式包装组件
function InteractiveFunctionList(props: React.ComponentProps<typeof FunctionList>) {
  const [selectedFuncStr, setSelectedFuncStr] = useState<string | null>(props.selectedFuncStr || null);

  return (
    <FunctionList
      {...props}
      selectedFuncStr={selectedFuncStr}
      onSelectFunction={(name) => {
        setSelectedFuncStr(name);
        console.log('选中函数:', name);
      }}
      onCreate={() => {
        console.log('创建新函数 - 通常会打开函数编辑器或模态框');
      }}
      onImport={(file) => {
        console.log('导入文件:', file.name, '大小:', file.size, 'bytes');
      }}
      onExport={(name) => {
        console.log('导出函数库 - 通常会下载包含所有函数的文件',name);
      }}
      onDelete={(name) => {
        console.log('删除函数:', name);
      }}
    />
  );
}

// 模拟函数数据
const mockFunctions: FunctionItem[] = [
  {
    name: 'add',
    summary: '计算两个数字的和，支持整数和浮点数运算',
    lastUpdatedBy: 'Alice',
    lastUpdatedAt: new Date('2024-01-15T10:30:00'),
    score: 4.8,
  },
  {
    name: 'multiply',
    summary: '计算两个数字的乘积',
    lastUpdatedBy: 'Bob',
    lastUpdatedAt: new Date('2024-01-14T15:45:00'),
    score: 4.5,
  },
  {
    name: 'calculate_mean',
    summary: '计算数组中所有数字的平均值，忽略非数字元素',
    lastUpdatedBy: 'Charlie',
    lastUpdatedAt: new Date('2024-01-13T09:20:00'),
    score: 4.9,
  },
  {
    name: 'find_max',
    summary: '在数组中查找最大的数字',
    lastUpdatedBy: 'David',
    lastUpdatedAt: new Date('2024-01-12T14:10:00'),
    score: 4.7,
    deprecated: true
  },
  {
    name: 'sort_array',
    summary: '对数组进行排序，支持升序和降序',
    lastUpdatedBy: 'Eve',
    lastUpdatedAt: new Date('2024-01-11T11:00:00'),
    score: 4.6,
  },
];

// 默认故事
export const Default: Story = {
  render: (args) => <InteractiveFunctionList {...args} />,
  args: {
    functions: mockFunctions,
    libraryName: 'math-utils',
    selectedFuncStr: 'add',
  },
};

// 空状态
export const Empty: Story = {
  render: (args) => <InteractiveFunctionList {...args} />,
  args: {
    functions: [],
    libraryName: 'empty-library',
    selectedFuncStr: null,
  },
};

// 大量函数
export const ManyFunctions: Story = {
  render: (args) => <InteractiveFunctionList {...args} />,
  args: {
    functions: Array.from({ length: 15 }, (_, i) => ({
      name: `function_${i + 1}`,
      summary: `这是第 ${i + 1} 个函数的描述`,
      lastUpdatedBy: ['Alice', 'Bob', 'Charlie'][i % 3],
      lastUpdatedAt: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000),
      score: Math.round((Math.random() * 2 + 3) * 10) / 10,
    })),
    libraryName: 'large-library',
    selectedFuncStr: 'function_1',
  },
};

// 长文本测试
export const LongContent: Story = {
  render: (args) => <InteractiveFunctionList {...args} />,
  args: {
    functions: [
      {
        name: 'very_long_function_name_that_might_overflow_and_need_tooltip',
        summary: '这是一个非常长的函数摘要，用于测试文本截断和 tooltip 功能。当文本过长时会自动截断并显示 tooltip。',
        lastUpdatedBy: 'LongUserName',
        lastUpdatedAt: new Date('2024-01-15T10:30:00'),
        score: 4.8,
      },
      {
        name: 'deprecated_function_example',
        summary: '这是一个已弃用的函数示例',
        lastUpdatedBy: 'Developer',
        lastUpdatedAt: new Date('2024-01-14T15:45:00'),
        score: 4.9,
        deprecated: true,
      },
      {
        name: 'normal_function',
        summary: '正常的函数',
        lastUpdatedBy: 'Dev',
        lastUpdatedAt: new Date('2024-01-13T12:00:00'),
        score: 5.0,
      },
    ],
    libraryName: 'tooltip-test-library_very_long_function_name',
    selectedFuncStr: 'very_long_function_name_that_might_overflow_and_need_tooltip',
  },
};
