import React from "react";
import { But<PERSON> } from "../../ui/button";
import { Input } from "../../ui/input";
import { Textarea } from "../../ui/textarea";
import { Label } from "../../ui/label";
import { Plus, X } from "lucide-react";
import { I18nInput } from "./ui/I18nInput";
import { ArrayManager } from "./ui/ArrayManager";
import { useFunctionContent } from "../hooks/useFunctionContent";
import { FunctionContent } from "../FunctionEditor";
/**
 * 可选字段部分组件的属性接口
 * @interface OptionalFieldsSectionProps
 * @description 定义了 OptionalFieldsSection 组件可接收的所有属性
 * @property content 函数内容数据
 * @property onContentChange 函数数据更新回调
 * @property isLoading 是否处于加载状态
 */
export interface OptionalFieldsSectionProps {
  /** 函数内容数据 */
  content: Partial<FunctionContent>;
  /** 内容更新回调函数 */
  onContentChange: (updates: Partial<FunctionContent>) => void;
  /** 是否处于加载状态 */
  isLoading?: boolean;
}

/**
 * 可选字段部分组件
 *
 * 负责管理函数的可选字段，包括：
 * - 脚注
 * - 另请参阅
 * - 变更记录
 */
export const OptionalFieldsSection: React.FC<OptionalFieldsSectionProps> = ({
  content,
  onContentChange,
  isLoading = false,
}) => {
  const { updateField } = useFunctionContent({
    content,
    onContentChange,
  });

  /**
   * 获取另请参阅数组
   */
  const getSeeAlsoArray = (): string[] => {
    return (content.seeAlso as unknown as string[]) || [];
  };

  /**
   * 获取变更记录对象
   */
  const getChangelogs = (): Record<string, string> => {
    return (content.changelogs as unknown as Record<string, string>) || {};
  };

  /**
   * 添加变更记录
   */
  const addChangelog = () => {
    const changelogs = getChangelogs();
    const newVersion = `v${Object.keys(changelogs).length + 1}.0.0`;
    updateField('changelogs', { ...changelogs, [newVersion]: '' });
  };

  /**
   * 删除变更记录
   * @param version 版本号
   */
  const removeChangelog = (version: string) => {
    const changelogs = getChangelogs();
    const newChangelogs = { ...changelogs };
    delete newChangelogs[version];
    updateField('changelogs', newChangelogs);
  };

  /**
   * 重命名变更记录版本
   * @param oldVersion 旧版本号
   * @param newVersion 新版本号
   */
  const renameChangelog = (oldVersion: string, newVersion: string) => {
    if (oldVersion === newVersion || !newVersion.trim()) return;

    const changelogs = getChangelogs();
    // if (changelogs[newVersion]) {
    //   alert('版本号已存在！');
    //   return;
    // }

    const changelog = changelogs[oldVersion];
    if (changelog !== undefined) {
      const newChangelogs = { ...changelogs };
      delete newChangelogs[oldVersion];
      newChangelogs[newVersion] = changelog;
      updateField('changelogs', newChangelogs);
    }
  };

  /**
   * 更新变更记录内容
   * @param version 版本号
   * @param value 变更内容
   */
  const updateChangelog = (version: string, value: string) => {
    const changelogs = getChangelogs();
    updateField('changelogs', { ...changelogs, [version]: value });
  };

  const changelogs = getChangelogs();

  return (
    <div className="space-y-6">
      {/* 脚注 */}
      <I18nInput
        label="脚注"
        value={content.footnote}
        onChange={(value) => updateField('footnote', value)}
        disabled={isLoading}
        type="textarea"
        rows={4}
        placeholderZh="函数的脚注信息，如参考文献等"
        placeholderEn="Function footnotes, such as references"
        idPrefix="footnote"

      />

      {/* 另请参阅 */}
      <ArrayManager
        label="另请参阅"
        value={getSeeAlsoArray()}
        onChange={(value) => updateField('seeAlso', value)}
        disabled={isLoading}
        placeholder="#/functions/functionName"
        addButtonText="添加函数引用"
        helpText="相关的其他函数引用"
        defaultNewValue="#/functions/"
      />

      {/* 变更记录 */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <Label className="text-base font-medium">变更记录</Label>
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={addChangelog}
            disabled={isLoading}
          >
            <Plus className="w-4 h-4 mr-1" />
            添加版本记录
          </Button>
        </div>

        <div className="space-y-3">
          {Object.entries(changelogs).map(([version, changelog]) => (
            <div key={version} className="border rounded-lg p-3 space-y-2">
              <div className="flex items-center gap-2">
                <Label className="text-sm font-medium">版本:</Label>
                <Input
                  defaultValue={version}
                  onBlur={(e) => {
                    const newVersion = e.target.value.trim();
                    if (newVersion && newVersion !== version) {
                      renameChangelog(version, newVersion);
                    }
                  }}
                  disabled={isLoading}
                  placeholder="版本号"
                  className="w-32"
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => removeChangelog(version)}
                  disabled={isLoading}
                >
                  <X className="w-4 h-4" />
                </Button>
              </div>
              <Textarea
                value={changelog}
                onChange={(e) => updateChangelog(version, e.target.value)}
                disabled={isLoading}
                placeholder="版本变更说明（支持 Markdown 格式）"
                rows={3}
              />
            </div>
          ))}

          {Object.keys(changelogs).length === 0 && (
            <div className="text-center py-4 text-gray-500 border-2 border-dashed border-gray-200 rounded-lg">
              暂无变更记录，点击上方按钮添加
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
