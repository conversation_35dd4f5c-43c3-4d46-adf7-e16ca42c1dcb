import type { LayoutServerLoad } from './$types';
import { redirect } from '@sveltejs/kit';
import type { UserPreferancesCookies } from '@askme/lib-common';
import { hotReload } from '$lib/hotRelod';

// 通过在路由组根布局添加未登陆跳转，保证路由组下所有路由均只能被登陆用户访问
// 设置用户偏好cookie响应式状态
export const load: LayoutServerLoad = async ({ locals, cookies }) => {
  if (!locals.user) {
    redirect(302, '/login');
  }
  // 中间件验证过了下面的cookies一定是合法的，所以不需要再验证
  return {
    cookiesData: JSON.parse(cookies.get('userPreferances')!) as UserPreferancesCookies,
    defaultTitle: hotReload.get_askme_config().appTitle,
    isSSO: locals.session.useCookieUserToken,
  };
};
