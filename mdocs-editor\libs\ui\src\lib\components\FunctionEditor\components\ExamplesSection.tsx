import React, { useCallback, useState } from "react";
import { Button } from "../../ui/button";
import { Input } from "../../ui/input";
import { Label } from "../../ui/label";
import { Plus, X } from "lucide-react";
import { I18nInput } from "./ui/I18nInput";
import { TagInput } from "./ui/TagInput";
import { useFunctionContent } from "../hooks/useFunctionContent";
import type { I18nText } from "./ui/I18nInput";
import { FunctionContent } from "../FunctionEditor";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "../../ui/dialog";
import { Options } from "../views/FormView";

export interface ExampleContent {
  /** 示例摘要 */
  summary: I18nText;
  /** 示例详细描述 */
  description: I18nText;
  /** 关联的语法引用列表 */
  syntaxes: string[];
}

export interface ExamplesSectionProps {
  /** 函数内容数据 */
  content: Partial<FunctionContent>;
  /** 内容更新回调函数 */
  onContentChange: (updates: Partial<FunctionContent>) => void;
  /** 是否处于加载状态 */
  isLoading?: boolean;
}

/**
 * 示例管理部分组件
 * 
 * 负责管理函数的示例，包括：
 * - 添加/删除示例
 * - 编辑示例名称
 * - 编辑示例摘要和描述（中英文）
 * - 管理关联的语法引用
 */
export const ExamplesSection: React.FC<ExamplesSectionProps> = ({
  content,
  onContentChange,
  isLoading = false,
}) => {
  const { updateField } = useFunctionContent({
    content,
    onContentChange,
  });

  // 生成可选语法标签的选项，仿照 SyntaxesSection 的 generateOptions
  const getSyntaxOptions: (currentSyntaxes: string[]) => Options[] = useCallback((
    currentSyntaxes: string[]
  ): Options[] => {
    const syntaxes = (content.syntaxes as Record<string, { description?: { zh?: string; en?: string } }>) || {};
    const options: { value: string; label: string; description?: string }[] = [];
    // 先添加所有有效语法
    Object.entries(syntaxes).forEach(([syntaxName, syntax]) => {
      const desc = syntax?.description?.zh || syntax?.description?.en || syntaxName;
      options.push({
        value: `#syntaxes/${syntaxName}`,
        label: syntaxName,
        description: desc,
      });
    });
    // 再补充已删除但被引用的语法
    (currentSyntaxes || []).forEach(syntaxRef => {
      if (syntaxRef.startsWith('#syntaxes/')) {
        const name = syntaxRef.replace('#syntaxes/', '');
        if (!syntaxes[name]) {
          options.push({
            value: syntaxRef,
            label: `${name} (已删除，建议移除)`,
            description: '此语法已被删除，建议移除',
          });
        }
      }
    });
    return options;
  },[content.syntaxes]);

  /**
   * 获取示例记录
   */
  const getExamples = (): Record<string, ExampleContent> => {
    return (content.examples as unknown as Record<string, ExampleContent>) || {};
  };

  /**
   * 添加新示例
   */
  const addExample = () => {
    const examples = getExamples();
    const newExampleName = `example${Object.keys(examples).length + 1}`;
    const newExample: ExampleContent = {
      summary: { zh: '', en: '' },
      description: { zh: '', en: '' },
      syntaxes: [],
    };
    // 新示例放在最前面，保持顺序
    updateField('examples', { [newExampleName]: newExample, ...examples });
  };

  /**
   * 删除示例
   * @param exampleName 示例名称
   */
  const removeExample = (exampleName: string) => {
    const examples = getExamples();
    const newExamples: Record<string, ExampleContent> = {};
    Object.entries(examples).forEach(([key, val]) => {
      if (key !== exampleName) {
        newExamples[key] = val;
      }
    });
    updateField('examples', newExamples);
  };

  /**
   * 重命名示例
   * @param oldName 旧名称
   * @param newName 新名称
   */
  const renameExample = (oldName: string, newName: string) => {
    if (oldName === newName || !newName.trim()) return;
    const examples = getExamples();
    // if (examples[newName]) {
    //   alert('示例名已存在！');
    //   return;
    // }
    const example = examples[oldName];
    if (example) {
      // 保持原有顺序，只更改示例名
      const newExamples: Record<string, ExampleContent> = {};
      Object.entries(examples).forEach(([key, val]) => {
        if (key === oldName) {
          newExamples[newName] = val;
        } else {
          newExamples[key] = val;
        }
      });
      updateField('examples', newExamples);
    }
  };

  /**
   * 更新示例字段
   * @param exampleName 示例名称
   * @param field 字段名
   * @param value 新值
   */
  const updateExample = (exampleName: string, field: keyof ExampleContent, value: ExampleContent[keyof ExampleContent]) => {
    const examples = getExamples();
    const example = examples[exampleName];
    if (example) {
      // 保持顺序
      const newExamples: Record<string, ExampleContent> = {};
      Object.entries(examples).forEach(([key, val]) => {
        if (key === exampleName) {
          newExamples[key] = { ...val, [field]: value };
        } else {
          newExamples[key] = val;
        }
      });
      updateField('examples', newExamples);
    }
  };

  const [dialogExampleName, setDialogExampleName] = useState<string | null>(null);
  const examples = getExamples();

  return (
    <div className="space-y-4">
      {/* 添加示例按钮 */}
      <div className="flex items-center justify-between">
        <span className="text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
          {Object.keys(examples).length} 个示例
        </span>
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={addExample}
          disabled={isLoading}
        >
          <Plus className="w-4 h-4 mr-1" />
          添加示例
        </Button>
      </div>

      {/* 示例列表 */}
      <div className="space-y-6">
        {Object.entries(examples).map(([exampleName, example]) => (
          <div key={exampleName} className="border rounded-lg p-4 space-y-4">
            {/* 示例名称和删除按钮 */}
            <div className="flex items-center justify-between gap-4">
              <div className="flex items-center gap-2 flex-1">
                <Label className="text-sm font-medium">示例ID</Label>
                <Input
                  defaultValue={exampleName}
                  onBlur={(e) => {
                    const newName = e.target.value.trim();
                    if (newName && newName !== exampleName) {
                      renameExample(exampleName, newName);
                    }
                  }}
                  disabled={isLoading}
                  placeholder="示例名称"
                  className="flex-1"
                />
              </div>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => setDialogExampleName(exampleName)}
                disabled={isLoading}
                title="删除示例"
              >
                <X className="w-4 h-4" />
              </Button>
              <Dialog open={dialogExampleName === exampleName} onOpenChange={(open) => {
                if (!open) setDialogExampleName(null);
              }}>
                <DialogContent className="sm:max-w-md">
                  <DialogHeader>
                    <DialogTitle>确认删除</DialogTitle>
                    <DialogDescription>
                      此操作无法撤销。您确定要删除示例 {exampleName} 吗？
                    </DialogDescription>
                  </DialogHeader>
                  <DialogFooter>
                    <Button onClick={() => setDialogExampleName(null)}>
                      取消
                    </Button>
                    <Button
                      onClick={() => {
                        removeExample(exampleName);
                        setDialogExampleName(null);
                      }}
                    >
                      确认删除
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </div>

            {/* 示例摘要 */}
            <I18nInput
              label="示例摘要"
              value={example.summary}
              onChange={(value) => updateExample(exampleName, 'summary', value)}
              disabled={isLoading}
              placeholderZh="示例的简短描述"
              placeholderEn="Brief example description"
              idPrefix={`example-summary-${exampleName}`}
              required={true}
            />

            {/* 示例详细描述 */}
            <I18nInput
              label="详细描述"
              value={example.description}
              onChange={(value) => updateExample(exampleName, 'description', value)}
              disabled={isLoading}
              type="textarea"
              rows={4}
              placeholderZh="示例的详细说明和代码"
              placeholderEn="Detailed example description and code"
              idPrefix={`example-desc-${exampleName}`}
              required={true}
            />

            {/* 关联语法 */}
            <TagInput
              label="关联语法"
              tags={example.syntaxes || []}
              availableTags={getSyntaxOptions(example.syntaxes || [])}
              onTagAdd={(syntaxRef: string) => {
                const current = example.syntaxes || [];
                if (!current.includes(syntaxRef)) {
                  updateExample(exampleName, 'syntaxes', [...current, syntaxRef]);
                }
              }}
              onTagRemove={(index: number) => {
                const current = example.syntaxes || [];
                const newArr = current.filter((_, i) => i !== index);
                updateExample(exampleName, 'syntaxes', newArr);
              }}
              disabled={isLoading}
              placeholder="选择语法"
              className="text-base"
            />
            <span className="text-xs text-gray-500">
              选择并关联已有语法。
            </span>
          </div>
        ))}

        {/* 空状态提示 */}
        {Object.keys(examples).length === 0 && (
          <div className="text-center py-1 text-gray-500">
            <span className="mb-2 text-sm">暂无示例</span>
            <span className="text-sm">点击上方按钮添加函数示例</span>
          </div>
        )}
      </div>
    </div>
  );
};
