import Cookies from 'js-cookie';

/**
 * 类型安全的Cookie钩子，可以响应式地读写某个Key对应的Cookie的内容。
 * ⚠️不适用于HTTP Only Cookie。
 * ⚠️类型T必须可JSON序列化。
 * ⚠️若value是一个对象，你必须将整个新对象写回value字段才能触发set方法的Cookies响应式更新（只有这种情况会调用我们的自定义setter）。
 */
export class UseCookies<T> {
  #key: string;
  #value = $state<T | null>(null);

  constructor(key: string, initValue: T, validator: (value: T) => boolean) {
    this.#key = key;
    const cookieString = Cookies.get(this.#key);
    if (cookieString) {
      try {
        const parsedValue = JSON.parse(cookieString);
        if (validator(parsedValue)) {
          this.#value = parsedValue;
        } else {
          this.#value = initValue;
        }
      } catch {
        this.#value = initValue;
      }
    } else {
      this.#value = initValue;
    }
  }

  get key() {
    return this.#key;
  }

  get value(): T {
    return this.#value!;
  }

  set value(v: T) {
    Cookies.set(this.#key, JSON.stringify(v));
    this.#value = v;
  }
}
