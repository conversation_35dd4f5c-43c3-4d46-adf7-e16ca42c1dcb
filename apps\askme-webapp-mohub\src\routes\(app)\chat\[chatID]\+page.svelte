<script lang="ts">
  import ChatPage from '$lib/components/ChatPage.svelte';
  import type { PageProps } from './$types';
  import { getVoteContext, buildMessageTree, getChatIdContext } from '@askme/lib-common';
  import { convertMsg } from '$lib/utils';

  const { data }: PageProps = $props();

  // 加载函数重新运行时，更新vote和chatId上下文
  $effect(() => {
    const voteMap = getVoteContext();
    data.messages.forEach((m) => {
      if (m.vote) {
        voteMap.set(m.id, m.vote.like);
      }
    });
    const chatIdContext = getChatIdContext();
    chatIdContext().chatId = data.chatId;
  });
</script>

<ChatPage
  chatId={data.chatId}
  messageTreeRoot={buildMessageTree(data.messages, convertMsg)}
  isSSO={data.isSSO}
  overviewTitle={data.defaultTitle}
  enableThemeSwitcher={data.enableThemeSwitcher}
/>
