import { useState } from "react";
import { SidebarLayout } from "./SidebarRoot";
import { IconBar } from "./IconBar";
import { LLMReviewPanel } from "./LLMReviewPanel";
import { StatisticsPanel } from "./StatisticsPanel";
import { ValidationPanel } from "./ValidationPanel";


/**
 * @summary 侧边栏组件的属性接口
 * @description 定义了 SideBar 组件可接收的所有属性
 * @property llmScore 大模型评分（可选）
 * @property llmComment 大模型评语（可选）
 * @property statisticsInfo 统计信息（可选）
 * @property validationInfo 校验信息（可选）
 * @property isSidebarOpen 侧边栏是否展开（可选）
 * @property onSidebarToggle 侧边栏展开/收起回调（可选）
 * @property activeTab 当前激活的标签页（可选）
 * @property onTabChange 标签页切换回调（可选）
 */
/**
 * 
 * @example
 * ```typescript
 * <SideBar
 *   llmScore={88}
 *   llmComment="模型评审通过，建议优化部分边界条件处理。"
 *   statisticsInfo={{
 *     functionCount: 12,
 *     parameterCount: 37,
 *     lastModified: "2024-06-10 15:23:45"
 *   }}
 *   validationInfo={{
 *     errors: [  
 *       "函数 validateUser 缺少返回类型声明",
 *       "函数 validateUser 缺少参数声明"
 *     ],
 *     warnings: [
 *       "函数 validateUser 缺少注释"
 *     ]
 *   }}
 *   isSidebarOpen={true}
 *   onSidebarToggle={() => console.log("Sidebar toggle")}
 *   activeTab="statistics"
 *   onTabChange={(tab) => console.log("Tab changed:", tab)}
 * />
 * ```
*/

export interface SideBarProps {
  llmScore?: number;
  llmComment?: string;
  statisticsInfo?: {
    functionCount: number;
    parameterCount: number;
    lastModified?: string;
  };
  validationInfo?: {
    errors: string[];
    warnings: string[];
  };
  isSidebarOpen?: boolean;
  onSidebarToggle?: () => void;
  activeTab?: "llm" | "statistics" | "validation";
  onTabChange?: (tab: "llm" | "statistics" | "validation") => void;
}

export function SideBar({
  llmScore,
  llmComment,
  statisticsInfo,
  validationInfo,
  isSidebarOpen: sidebarOpenProp,
  onSidebarToggle: onSidebarToggleProp,
  activeTab: activeTabProp,
  onTabChange,
}: SideBarProps) {
  const [isInternalSidebarOpen, setIsInternalSidebarOpen] = useState(true);
  const isSidebarOpen = sidebarOpenProp !== undefined ? sidebarOpenProp : isInternalSidebarOpen;
  const handleSidebarToggle = () => {
    if (onSidebarToggleProp) onSidebarToggleProp();
    else setIsInternalSidebarOpen(v => !v);
  };

  const [internalActiveTab, setInternalActiveTab] = useState<"llm" | "statistics" | "validation">("llm");
  const activeTab = activeTabProp !== undefined ? activeTabProp : internalActiveTab;
  const handleTabChange = (tab: string) => {
    if (onTabChange) onTabChange(tab as "llm" | "statistics" | "validation");
    else setInternalActiveTab(tab as "llm" | "statistics" | "validation");
  };

  const getTabTitle = () => {
    switch (activeTab) {
      case "llm": return "大模型评审";
      case "statistics": return "统计信息";
      case "validation": return "校验结果";
      default: return "大模型评审";
    }
  };

  return (
    <div className="h-full">
      <SidebarLayout
        isSidebarOpen={isSidebarOpen}
        onSidebarToggle={handleSidebarToggle}
        iconBar={
          <IconBar
            activeTab={activeTab}
            onTabChange={handleTabChange}
          />
        }
      >
        {isSidebarOpen && (
          <div className="flex flex-col h-full overflow-hidden">
            <div className="flex items-center justify-between p-4 border-b bg-white flex-shrink-0">
              <div className="text-lg font-semibold text-gray-800">
                {getTabTitle()}
              </div>
            </div>
            
            <div className="flex-1 min-h-0 overflow-hidden">
              {activeTab === "llm" && <LLMReviewPanel score={llmScore} comment={llmComment || ""} />}
              {activeTab === "statistics" && (
                <StatisticsPanel
                  statisticsInfo={
                    statisticsInfo ?? { functionCount: 0, parameterCount: 0, lastModified: "" }
                  }
                />
              )}
              {activeTab === "validation" && (
                <ValidationPanel
                  validationInfo={validationInfo}
                />
              )}
            </div>
          </div>
        )}
      </SidebarLayout>
    </div>
  );
}

export default SideBar;
