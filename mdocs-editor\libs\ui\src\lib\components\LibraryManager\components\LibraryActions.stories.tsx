import type { Meta, StoryObj } from '@storybook/react-vite';
import { LibraryActions } from './LibraryActions';
import { Library } from '../LibraryManager';

const meta: Meta<typeof LibraryActions> = {
  title: 'Components/LibraryManager/LibraryActions',
  component: LibraryActions,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'Library 操作下拉菜单组件，提供新建版本、导出和删除功能。',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    library: {
      description: 'Library 数据对象',
      control: 'object',
    },

    onExport: {
      description: '导出回调函数',
    },
    onDelete: {
      description: '删除回调函数',
    },
  },
};

export default meta;
type Story = StoryObj<typeof LibraryActions>;

// 模拟数据
const mockLibrary: Library = {
  name: 'UI Components',
  latestVersion: '2.0.0',
  lastUpdated: new Date('2024-01-15'),
  summary: '通用 UI 组件库，包含按钮、输入框、对话框等常用组件',
};

/**
 * 默认状态
 */
export const Default: Story = {
  args: {
    library: mockLibrary,
    onExport: (library: Library) => console.log('onExport', library),
    onDelete: (library: Library) => console.log('onDelete', library),
  },
};
