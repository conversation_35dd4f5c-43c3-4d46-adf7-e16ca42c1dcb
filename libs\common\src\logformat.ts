/**
 * Log helper to build structured log messages
 *
 * Levels:
 * - trace: Detailed information, typically of interest only when diagnosing problems.
 * - debug: Debug information. Typically status information, only useful for developers.
 * - info: Informational messages. Frequency of this level should be kept to < 30 per minute.
 * - warn: Warning messages. Something unexpected happened, but the app can recover.
 * - error: Error messages. The app can't recover from the error.
 * - fatal: Fatal messages. The app is about to crash.
 *
 * Usage:
 *
 * This works with vscode.OutputChannel
 *
 * ```typescript
 * import * as vscode from "vscode";
 *
 * const channel = vscode.OutputChannel("MyExtension");
 *
 * // Log a trace message
 * channel.appendLine(LogFormat.trace("This is a trace message"));
 *
 * // Log a trace message with additional context
 * channel.appendLine(LogFormat.trace({message: "This is a trace message", context: {foo: "bar"}}));
 * ```
 *
 * This also works with console.log
 *
 * ```typescript
 * // Log a trace message
 * console.log(LogFormat.trace("This is a trace message"));
 *
 * // Log a trace message with additional context
 * console.log(LogFormat.trace({message: "This is a trace message", context: {foo: "bar"}}));
 * ```
 *
 */
export type LogLevel = 'trace' | 'debug' | 'info' | 'warn' | 'error' | 'fatal';
export type LogFormatType = 'text' | 'json';
export type FormatData =
  | {
      message: string;
      context?: Record<string, unknown>;
    }
  | string;

// Private module state
let __LOG_FILTER_LEVEL: LogLevel = 'info';

// Public functions
export function set_log_level(level: LogLevel): void {
  __LOG_FILTER_LEVEL = level;
}

export function get_log_level(): LogLevel {
  return __LOG_FILTER_LEVEL;
}

export function trace(data: FormatData, format: LogFormatType = 'text'): string {
  return format_message('trace', data, format);
}

export function debug(data: FormatData, format: LogFormatType = 'text'): string {
  return format_message('debug', data, format);
}

export function info(data: FormatData, format: LogFormatType = 'text'): string {
  return format_message('info', data, format);
}

export function warn(data: FormatData, format: LogFormatType = 'text'): string {
  return format_message('warn', data, format);
}

export function error(data: FormatData, format: LogFormatType = 'text'): string {
  return format_message('error', data, format);
}

export function fatal(data: FormatData, format: LogFormatType = 'text'): string {
  return format_message('fatal', data, format);
}

export function format_message(level: LogLevel, data: FormatData, format: LogFormatType): string {
  if (!shouldLog(level)) {
    return '';
  }
  let message: string;
  let context: Record<string, unknown> | undefined;
  if (typeof data === 'string') {
    message = data;
  } else {
    message = data.message;
    context = data.context;
  }
  const now = new Date().toISOString();
  const log = {
    level,
    time: now,
    message,
    context,
  };
  if (format === 'json') {
    return JSON.stringify(log);
  } else {
    return formatText(level, now, message, context);
  }
}

// Private helper functions
function formatText(
  level: LogLevel,
  time: string,
  message: string,
  context?: Record<string, unknown>,
): string {
  if (context) {
    const contextStr = JSON.stringify(context);
    return `${time} - [${level.toUpperCase()}] - ${message} - ${contextStr}`;
  } else {
    return `${time} - [${level.toUpperCase()}] - ${message}`;
  }
}

function logLevelIndex(level: LogLevel): number {
  switch (level) {
    case 'trace':
      return 0;
    case 'debug':
      return 1;
    case 'info':
      return 2;
    case 'warn':
      return 3;
    case 'error':
      return 4;
    case 'fatal':
      return 5;
  }
}

function shouldLog(level: LogLevel): boolean {
  return logLevelIndex(level) >= logLevelIndex(__LOG_FILTER_LEVEL);
}

// Logger interface and helper function
export interface Logger {
  appendLine?: (message: string) => void;
  log?: (message: string) => void;
}

export function log(logger?: Logger, message?: string): void {
  if (!logger) {
    return;
  }

  if (message) {
    if (logger.appendLine) {
      logger.appendLine(message);
    } else if (logger.log) {
      logger.log(message);
    }
  }
}
