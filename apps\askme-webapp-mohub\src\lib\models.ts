import {
  customProvider,
  wrapLanguageModel,
  extractReasoningMiddleware,
  simulateReadableStream,
} from 'ai';
import { createOpenAICompatible } from '@ai-sdk/openai-compatible';
import { config } from '$lib/config.server';
import { MockLanguageModelV1 } from 'ai/test';
import { hotReload } from './hotRelod';

export function getOpenAIforUser(userAccessToken: string) {
  const openai = createOpenAICompatible({
    name: 'llm-store',
    apiKey: userAccessToken,
    baseURL: config.llm.openai_api_baseurl,
  });
  return customProvider({
    languageModels: {
      CHAT_MODEL: openai(hotReload.get_askme_config().llmModels.chatModel),
      REASON_MODAL: wrapLanguageModel({
        model: openai(hotReload.get_askme_config().llmModels.reasonModel),
        middleware: extractReasoningMiddleware({
          tagName: 'think',
        }),
      }),
      MULTI_MODAL_MODEL: openai(hotReload.get_askme_config().llmModels.multiModalModel),
    },
  });
}

/**
 * 获取一个用于响应用户风险查询的mock语言模型
 * @param riskQueryAnswer 风险查询回答
 * @returns
 */
export function getRiskResponseModel(riskQueryAnswer: string): MockLanguageModelV1 {
  return new MockLanguageModelV1({
    doStream: async () => ({
      stream: simulateReadableStream({
        chunks: [
          { type: 'text-delta', textDelta: riskQueryAnswer },
          {
            type: 'finish',
            finishReason: 'stop',
            logprobs: undefined,
            usage: { completionTokens: 0, promptTokens: 0 },
          },
        ],
      }),
      rawCall: { rawPrompt: null, rawSettings: {} },
    }),
  });
}
