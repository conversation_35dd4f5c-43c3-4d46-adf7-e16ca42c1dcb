<script lang="ts">
  import * as Sidebar from '$lib/components/ui/sidebar';
  import * as AlertDialog from '$lib/components/ui/alert-dialog';
  import type { ChatModel } from '@askme/lib-common';
  import SidebarItem from './SidebarItem.svelte';
  import MessageSquarePlus from '@lucide/svelte/icons/message-square-plus';
  import Search from '@lucide/svelte/icons/search';
  import { goto } from '$app/navigation';
  import { page } from '$app/state';
  import { toast } from 'svelte-sonner';
  import { getChatHistoryContext, getChatIdContext } from '@askme/lib-common';
  import Input from '$lib/components/ui/input/input.svelte';
  import type { Attachment } from 'svelte/attachments';
  import ChatSearchBar from '../search/ChatSearchBar.svelte';

  interface Props {
    /** @prop {string} sidebarTitle - sidebar的标题文本. */
    sidebarTitle: string;
    /** @prop {string} newChatButtonTitle - 新建对话的标题文本. */
    newChatButtonTitle: string;
    /** @prop {ChatModel[]} chat - sidebar的chat记录列表，按id/日期排序。*/
    chats: ChatModel[];
    /** @prop {() => void} onNewChat - 点击新建对话按钮后的回调函数*/
    onNewChat: () => void;
  }

  const { sidebarTitle, newChatButtonTitle, chats, onNewChat }: Props = $props();

  let chatHistory = getChatHistoryContext();
  let chatIdContext = getChatIdContext();

  let chatIdToDelete = $state<string | undefined>(undefined);
  let alertDialogOpen = $state(false);
  let nowReamingChatId: undefined | string = $state(undefined);
  let renameTileStr: undefined | string = $state(undefined);
  let isChatSearchBarOpen: boolean = $state(false);

  const ONE_DAY_IN_MILLISECONDS = 1000 * 3600 * 24;
  const dateLabel = ['今天', '昨天', '前7天', '前30天', '更早'];
  //根据chat的创建日期分类到chatGroup中
  const chatGroup = $derived.by(() => {
    if (chats && Array.isArray(chats) && chats.length > 0) {
      // 由于chats本身可能是个$state rune，无法在$derived中直接更改，因此在这里解构重新创造一个list以取消其反应性
      return [...chats]
        .sort((a, b) => {
          const dateA = typeof a.createdAt === 'string' ? new Date(a.createdAt) : a.createdAt;
          const dateB = typeof b.createdAt === 'string' ? new Date(b.createdAt) : b.createdAt;
          return dateB.getTime() - dateA.getTime();
        }) //后端会排序，但谨慎起见再排序一次(日期降序排序)
        .reduce(
          (acc, cur) => {
            // 确保总是取得props变化后，组件内响应更新时的最新时间
            const now = new Date();
            // 今天0点的时间
            const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
            const curChatDate =
              typeof cur.createdAt === 'string' ? new Date(cur.createdAt) : cur.createdAt;
            // 对话创建日期的0点时间
            const curChatDay = new Date(
              curChatDate.getFullYear(),
              curChatDate.getMonth(),
              curChatDate.getDate(),
            );
            const diffTime = today.getTime() - curChatDay.getTime();
            const diffDays = Math.floor(diffTime / ONE_DAY_IN_MILLISECONDS);
            if (diffDays === 0) {
              acc[0].push(cur);
            } else if (diffDays === 1) {
              acc[1].push(cur);
            } else if (diffDays <= 7) {
              acc[2].push(cur);
            } else if (diffDays <= 30) {
              acc[3].push(cur);
            } else {
              acc[4].push(cur);
            }
            return acc;
          },
          [[], [], [], [], []] as ChatModel[][],
        );
    } else {
      return [[], [], [], [], []] as ChatModel[][];
    }
  });

  function onDelete(id: string) {
    chatIdToDelete = id;
    alertDialogOpen = true;
  }

  async function handleDeleteChat() {
    const deletePromise = (async () => {
      const res = await fetch('/api/chat', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ id: chatIdToDelete }),
      });
      if (!res.ok) {
        throw new Error();
      }
    })();

    toast.promise(deletePromise, {
      loading: '删除对话中',
      success: () => {
        chatHistory.splice(
          chatHistory.findIndex((chat) => chat.id === chatIdToDelete),
          1,
        );
        return '聊天已删除';
      },
      error: '聊天删除失败',
    });

    alertDialogOpen = false;

    if (chatIdToDelete === page.params.chatID || page.url.pathname === '/chat') {
      goto('/chat', { invalidate: ['app:index'] });
    }
  }

  // ===========标题重命名逻辑========================
  function onRename(chatId: string, title: string) {
    nowReamingChatId = chatId;
    renameTileStr = title;
  }

  // 在重命名后乐观ui更新
  function updateUIAfterRenameTitle() {
    // 更新context
    const chatWillUpdate = chatHistory.find((chat) => chat.id === nowReamingChatId);
    if (chatWillUpdate && renameTileStr) {
      chatWillUpdate.title = renameTileStr;
    }
    // 重置状态
    resetRename();
  }

  // 还原标题重命名初始状态
  function resetRename() {
    nowReamingChatId = undefined;
    renameTileStr = undefined;
  }

  function submitRenamedTitle(chatId: string, title: string) {
    fetch('/api/chat', {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ id: chatId, title }),
    });
  }

  function checkAndRenameChatTitle() {
    if (chatHistory.find((chat) => chat.id === nowReamingChatId)?.title === renameTileStr) {
      // 发现没有重命名，取消网络请求
      resetRename();
    } else {
      // 确实重命名了，乐观更新ui+网络请求
      const submitTitle = renameTileStr;
      const submitId = nowReamingChatId;
      // 乐观更新ui
      updateUIAfterRenameTitle();
      // 网络请求
      submitRenamedTitle(submitId!, submitTitle!);
    }
  }

  const inputAttachment: Attachment = (element) => {
    const handleMouseDownOutside = (event: MouseEvent) => {
      if (
        nowReamingChatId &&
        element &&
        !element.contains(event.target as Node) &&
        renameTileStr &&
        renameTileStr !== ''
      ) {
        checkAndRenameChatTitle();
      }
    };
    (element as HTMLInputElement).focus();
    //给整个dom添加事件监听器
    document.addEventListener('mousedown', handleMouseDownOutside);
    return () => {
      // 移除不再需要的事件监听器
      document.removeEventListener('mousedown', handleMouseDownOutside);
    };
  };

  function handleRenameInputKeydown(event: KeyboardEvent) {
    // 键盘输入回车，尝试提交新标题
    if (event.key === 'Enter') {
      checkAndRenameChatTitle();
    } else if (event.key === 'Escape') {
      // 键盘输入esc，取消标题重命名
      resetRename();
    }
  }
  // ===========标题重命名逻辑结束========================
</script>

<!--
@component
ChatSidebar是用于显示LLM与用户对话历史的侧边栏。

- 用法:
  ``` svelte
  <Sidebar.Provider>
    <ChatSidebar sidebarTitle="MWORKS 智能问答助手" newChatButtonTitle="开启新对话" {chats} onNewChat={}/>
    <main>
      <Sidebar.Trigger />
    </main>
  </Sidebar.Provider>
  ```
  -->
<Sidebar.Root>
  <Sidebar.Header>
    <span class="text rounded-md px-2 text-lg font-semibold">
      {sidebarTitle}
    </span>
    <Sidebar.Menu>
      <Sidebar.MenuItem>
        <Sidebar.MenuButton isActive class="w-fit cursor-pointer" onclick={onNewChat}>
          {#snippet child({ props })}
            <span {...props}>
              <MessageSquarePlus />
              {newChatButtonTitle}
            </span>
          {/snippet}
        </Sidebar.MenuButton>
        <Sidebar.MenuButton class="cursor-pointer" onclick={() => (isChatSearchBarOpen = true)}>
          {#snippet child({ props })}
            <span {...props}>
              <Search />
              <span>搜索</span>
            </span>
          {/snippet}
        </Sidebar.MenuButton>
      </Sidebar.MenuItem>
    </Sidebar.Menu>
  </Sidebar.Header>
  <Sidebar.Content>
    {#each chatGroup as chatBlock, index (index)}
      {#if chatBlock.length}
        <Sidebar.Group>
          <Sidebar.GroupLabel>{dateLabel[index]}</Sidebar.GroupLabel>
          <Sidebar.GroupContent>
            <Sidebar.Menu>
              {#each chatBlock as chat (chat.id)}
                {#if nowReamingChatId === chat.id}
                  <Input
                    bind:value={renameTileStr}
                    {@attach inputAttachment}
                    onkeydown={(event) => handleRenameInputKeydown(event)}
                  />
                {:else}
                  <SidebarItem
                    isActive={chat.id.toString() === chatIdContext().chatId}
                    id={chat.id}
                    content={chat.title}
                    onRename={() => onRename(chat.id, chat.title)}
                    onDelete={() => onDelete(chat.id)}
                    itemURL={'/chat/' + chat.id}
                  />
                {/if}
              {/each}
            </Sidebar.Menu>
          </Sidebar.GroupContent>
        </Sidebar.Group>
      {/if}
    {/each}

    <AlertDialog.Root bind:open={alertDialogOpen}>
      <AlertDialog.Content>
        <AlertDialog.Header>
          <AlertDialog.Title>确定删除？</AlertDialog.Title>
          <AlertDialog.Description>此操作不可逆</AlertDialog.Description>
        </AlertDialog.Header>
        <AlertDialog.Footer>
          <AlertDialog.Cancel>取消</AlertDialog.Cancel>
          <AlertDialog.Action onclick={handleDeleteChat}>删除</AlertDialog.Action>
        </AlertDialog.Footer>
      </AlertDialog.Content>
    </AlertDialog.Root>
    <ChatSearchBar bind:isOpen={isChatSearchBarOpen} />
  </Sidebar.Content>
</Sidebar.Root>
