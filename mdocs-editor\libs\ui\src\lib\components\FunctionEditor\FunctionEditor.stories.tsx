import type { Meta, StoryObj } from '@storybook/react-vite';
import { useState } from 'react';
import { FunctionEditor, type FunctionContent } from './FunctionEditor';
import { Options } from './views/FormView';


const meta: Meta<typeof FunctionEditor> = {
  title: 'Components/FunctionEditor',
  component: FunctionEditor,
  parameters: {
    layout: 'fullscreen',
    tags: ['autodocs'],
    argTypes: {
      functionName: {
        control: 'text',
        description: '函数名称，显示在编辑器头部'
      },
      isLoading: {
        control: 'boolean',
        description: '是否处于加载状态，会禁用所有输入控件'
      },
      availableTags: {
        control: 'object',
        description: '可选的标签列表，用于标签选择器'
      },
      availableVersions: {
        control: 'object',
        description: '可选的版本列表，用于版本选择器'
      },
      availableTypes: {
        control: 'object',
        description: '可选的参数类型列表，用于参数类型选择器'
      }
    }
  }
};

export default meta;
type Story = StoryObj<typeof meta>;

// 包裹容器
const Wrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <div className="w-[1200px] h-[700px]">
    {children}
  </div>
);

// 示例标签选项
const sampleTagOptions: Options[] = [
  {
    value: "#/i18n/mathematics",
    label: "数学计算",
    description: "数学相关的计算函数，如代数运算、三角函数等"
  },
  {
    value: "#/i18n/plotting",
    label: "绘图可视化",
    description: "用于数据可视化和绘图的函数，支持2D/3D图表"
  },
  {
    value: "#/i18n/dataProcessing",
    label: "数据处理",
    description: "数据处理和分析相关函数，包括清洗、转换、聚合"
  },
  {
    value: "#/i18n/fileIO",
    label: "文件操作",
    description: "文件读写和I/O操作函数，支持多种格式"
  },
  {
    value: "#/i18n/statistics",
    label: "统计分析",
    description: "统计学相关的分析函数，如描述性统计、假设检验"
  },
  {
    value: "#/i18n/signalProcessing",
    label: "信号处理",
    description: "信号处理和滤波函数，用于时域和频域分析"
  },
  {
    value: "#/i18n/optimization",
    label: "优化算法",
    description: "优化和求解算法函数，包括线性和非线性优化"
  },
  {
    value: "#/i18n/experimental",
    label: "实验性",
    description: "实验性功能，API可能会发生变化，谨慎使用"
  },
  {
    value: "#/i18n/advanced",
    label: "高级功能",
    description: "高级用户使用的复杂功能，需要深入理解"
  },
];

// 示例版本选项
const sampleVersionOptions: Options[] = [
  {
    value: "2025a",
    label: "2025a",
    description: "2025年第一个版本"
  },
  {
    value: "2024b",
    label: "2024b",
    description: "2024年第二个版本"
  },
  {
    value: "2024a",
    label: "2024a",
    description: "2024年第一个版本"
  }
];

// 示例类型选项
const sampleTypeOptions: Options[] = [
  // 基础类型
  { value: "#/builtins/Scalar", label: "标量 (Scalar)", description: "标量类型" },
  { value: "#/builtins/Vector", label: "向量 (Vector)", description: "向量类型" },
  { value: "#/builtins/Matrix", label: "矩阵 (Matrix)", description: "矩阵类型" },
  { value: "#/builtins/String", label: "字符串 (String)", description: "字符串类型" },
  { value: "#/builtins/Boolean", label: "布尔值 (Boolean)", description: "布尔类型" },
  { value: "#/builtins/Any", label: "任意类型 (Any)", description: "任意类型" },

  // 数值类型
  { value: "#/builtins/Int32", label: "32位整数 (Int32)", description: "32位整数类型" },
  { value: "#/builtins/Int64", label: "64位整数 (Int64)", description: "64位整数类型" },
  { value: "#/builtins/Float32", label: "32位浮点数 (Float32)", description: "32位浮点数类型" },
  { value: "#/builtins/Float64", label: "64位浮点数 (Float64)", description: "64位浮点数类型" },

  // 视觉类型
  { value: "#/builtins/Color", label: "颜色 (Color)", description: "颜色类型" },
  { value: "#/builtins/RGB", label: "RGB颜色 (RGB)", description: "RGB颜色类型" },
  { value: "#/builtins/LineStyle", label: "线型 (LineStyle)", description: "线型类型" },
  { value: "#/builtins/Marker", label: "标记 (Marker)", description: "标记类型" },
];

// 完整的示例函数数据
const completeFunctionData: Partial<FunctionContent> = {
  summary: {
    zh: "绘制二维线图的函数，用于数据可视化",
    en: "Function for plotting 2-D line charts for data visualization"
  },
  since: "2025a",
  tags: ["#/i18n/tagGraphics", "#/i18n/tag2D3DPlots", "#/i18n/tagVisualization"],
  examples: {
    basicPlot: {
      summary: { zh: "基本线图", en: "Basic Line Plot" },
      description: {
        zh: "使用基本参数绘制简单的二维线图\nplot(x, y)",
        en: "Draw a simple 2-D line plot using basic parameters\nplot(x, y)"
      },
      syntaxes: ["#syntaxes/basic"]
    },
    styledPlot: {
      summary: { zh: "样式化线图", en: "Styled Line Plot" },
      description: {
        zh: "使用自定义样式绘制线图\nplot(x, y, 'LineStyle', '--', 'Color', 'red')",
        en: "Draw a line plot with custom styling\nplot(x, y, 'LineStyle', '--', 'Color', 'red')"
      },
      syntaxes: ["#syntaxes/styled"]
    }
  } as any,
  syntaxes: {
    basic: {
      description: { zh: "基本语法：绘制简单线图", en: "Basic syntax: draw simple line plot" },
      params: ["#params/x", "#params/y"],
      group: 1
    },
    styled: {
      description: { zh: "样式化语法：带样式参数的线图", en: "Styled syntax: line plot with style parameters" },
      params: ["#params/x", "#params/y", "#params/lineStyle", "#params/color"],
      group: 2,
      examples: ["#examples/styledPlot"]
    }
  } as any,
  params: {
    x: {
      summary: { zh: "X轴数据", en: "X-axis data" },
      description: { zh: "用于绘制的X轴坐标数据点", en: "Data points for X-axis coordinates" },
      kind: "posIn",
      type: "#/builtins/Array",
      examples: ["[1, 2, 3, 4, 5]"]
    },
    y: {
      summary: { zh: "Y轴数据", en: "Y-axis data" },
      description: { zh: "用于绘制的Y轴坐标数据点", en: "Data points for Y-axis coordinates" },
      kind: "posIn",
      type: "#/builtins/Array",
      examples: ["[2, 4, 6, 8, 10]"]
    },
    lineStyle: {
      summary: { zh: "线条样式", en: "Line style" },
      description: { zh: "指定线条的样式类型", en: "Specify the style type of the line" },
      kind: "keyword",
      type: "#/builtins/String",
      default: "'-'",
      examples: ["'-'", "'--'", "':'", "'-.'"]
    },
    color: {
      summary: { zh: "线条颜色", en: "Line color" },
      description: { zh: "指定线条的颜色", en: "Specify the color of the line" },
      kind: "keyword",
      type: ["#/builtins/String", "#/builtins/Array"],
      examples: ["'red'", "'blue'", "[1, 0, 0]"]
    }
  } as any,
  seeAlso: ["#/functions/scatter", "#/functions/bar", "#/functions/histogram"] as any,
  footnote: {
    zh: "此函数基于 MATLAB 的 plot 函数实现，支持多种绘图样式和参数配置。",
    en: "This function is based on MATLAB's plot function, supporting various plotting styles and parameter configurations."
  },
  changelogs: {
    "2025a": "初始版本发布",
    "2025b": "添加了更多样式选项和颜色支持"
  } as any
};

// 完整功能演示
export const CompleteExample: Story = {
  render: (args) => {
    const [functionData, setFunctionData] = useState<Partial<FunctionContent>>(completeFunctionData);

    return (
      <Wrapper>
        <FunctionEditor
          {...args}
          functionData={functionData}
          functionID='plot2d'
          availableTags={sampleTagOptions}
          availableVersions={sampleVersionOptions}
          availableTypes={sampleTypeOptions}
          onDataChange={(data) => {
            console.log('数据更新:', data);
            setFunctionData(data);
          }}
        />
      </Wrapper>
    );
  },
  args: {
    isLoading: false,
  },
};

// 空数据状态
export const EmptyData: Story = {
  render: (args) => {
    const [functionData, setFunctionData] = useState<Partial<FunctionContent>>({});

    return (
      <Wrapper>
        <FunctionEditor
          {...args}
          functionData={functionData}
          functionID="newFunction"
          availableTags={sampleTagOptions}
          availableVersions={sampleVersionOptions}
          availableTypes={sampleTypeOptions}
          onDataChange={(data) => {
            console.log('数据更新:', data);
            setFunctionData(data);
          }}
        />
      </Wrapper>
    );
  },
  args: {
    isLoading: false,
  },
};

// 基本数据示例
export const BasicExample: Story = {
  render: (args) => {
    const [functionData, setFunctionData] = useState<Partial<FunctionContent>>({
      summary: {
        zh: "示例函数",
        en: "Example Function"
      },
      since: "2025a",
      tags: ["#/i18n/mathematics", "#/i18n/plotting"]
    });

    return (
      <Wrapper>
        <FunctionEditor
          {...args}
          functionData={functionData}
          availableTags={sampleTagOptions}
          availableVersions={sampleVersionOptions}
          availableTypes={sampleTypeOptions}
          functionID="exampleFunc"
          onDataChange={(data) => {
            console.log('数据更新:', data);
            setFunctionData(data);
          }}
        />
      </Wrapper>
    );
  },
  args: {
    isLoading: false,
  },
};

// 加载状态
export const LoadingState: Story = {
  render: (args) => {
    const [functionData, setFunctionData] = useState<Partial<FunctionContent>>(completeFunctionData);

    return (
      <div className="w-[1200px] h-[700px] flex flex-col">
        <div className="flex-1">
          <FunctionEditor
            {...args}
            functionData={functionData}
            availableTags={sampleTagOptions}
            availableVersions={sampleVersionOptions}
            availableTypes={sampleTypeOptions}
            functionID="loadingFunction"
            onDataChange={(data) => {
              console.log('数据更新:', data);
              setFunctionData(data);
            }}
          />
        </div>
      </div>
    );
  },
  args: {
    isLoading: true,
  },
};

// 简单的外部控制演示
export const InteractiveDemo: Story = {
  render: () => {
    const [functionData, setFunctionData] = useState<Partial<FunctionContent>>({});

    // 演示外部控制按钮
    const ExternalControls = () => {
      const handleSetSample = () => {
        setFunctionData(completeFunctionData);
      };

      const handleReset = () => {
        setFunctionData({});
      };

      const handleGetData = () => {
        console.log('当前数据:', functionData);
        alert('数据已输出到控制台，请查看 Console');
      };

      const handleExportJson = () => {
        const json = JSON.stringify(functionData, null, 2);
        const blob = new Blob([json], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'function-data.json';
        a.click();
        URL.revokeObjectURL(url);
      };

      return (
        <div className="flex gap-2 p-4 bg-gray-100 border-b">
          <button
            onClick={handleSetSample}
            className="px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600 text-sm"
          >
            设置示例数据
          </button>
          <button
            onClick={handleReset}
            className="px-3 py-1 bg-red-500 text-white rounded hover:bg-red-600 text-sm"
          >
            重置数据
          </button>
          <button
            onClick={handleGetData}
            className="px-3 py-1 bg-green-500 text-white rounded hover:bg-green-600 text-sm"
          >
            获取数据
          </button>
          <button
            onClick={handleExportJson}
            className="px-3 py-1 bg-purple-500 text-white rounded hover:bg-purple-600 text-sm"
          >
            导出 JSON
          </button>
        </div>
      );
    };

    return (
      <div className=" flex flex-col">
        <ExternalControls />
        <div className="flex-1">
          <FunctionEditor
            functionData={functionData}
            availableTags={sampleTagOptions}
            availableVersions={sampleVersionOptions}
            availableTypes={sampleTypeOptions}
            functionID="demoFunction"
            onDataChange={(data) => {
              console.log('数据更新:', data);
              setFunctionData(data);
            }}
            isLoading={false}
          />
        </div>
      </div>
    );
  },
};

// 预览功能演示
export const PreviewDemo: Story = {
  render: (args) => {
    const [functionData, setFunctionData] = useState<Partial<FunctionContent>>(completeFunctionData);

    return (
      <div className=" flex flex-col">
        <div className="p-4 bg-blue-50 border-b">
          <h3 className="text-lg font-semibold text-blue-900 mb-2">预览功能演示</h3>
          <p className="text-blue-700 text-sm">
            这个演示展示了基于JSON数据生成的函数文档预览页面。点击"预览"标签查看效果。
          </p>
        </div>
        <div className="flex-1">
          <FunctionEditor
            {...args}
            functionData={functionData}
            functionID='plot'
            availableTags={sampleTagOptions}
            availableVersions={sampleVersionOptions}
            availableTypes={sampleTypeOptions}
            onDataChange={(data) => {
              console.log('数据更新:', data);
              setFunctionData(data);
            }}
          />
        </div>
      </div>
    );
  },
  args: {
    isLoading: false,
  },
};
