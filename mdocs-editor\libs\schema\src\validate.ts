import { checkParamsRefInSyntax } from "./rules";
import * as Types from "./types";
import { type Context, ValidationError } from "./utils";

/**
 * @summary 验证JSON字符串定义的MWORKS元数据（Library）的语法和语义
 * @param input JSON字符串，包含了MWORKS元数据的Library对象。
 * @returns 错误信息的列表
 * @example
 * ```typescript
 * const result = check_library('{"functions": {}}');
 * // result将含有语法错误信息
 * ```
 */
export function check_library(input: string): ValidationError[] {
  const object = JSON.parse(input) as Types.JlLibrary;
  return validate_library(object);
}

/**
 * @summary 验证param对象的语义，并更新上下文
 * @description 验证当前param对象是否符合约束，更新上下文中的params集合，结果保存在context.errors中
 * @param context 解析器的上下文对象
 * @param paramName 当前param的名字
 * @param _data 当前的param对象
 * @example
 * ```typescript
 * const context: Context = {
 *   currentFunctionName: "functionName",
 *   params: new Set([]),
 *   errors: []
 * };
 * validate_param(context, "X", {
 *   // param object
 * });
 * // 现在 context.params 中会包含 "functionName/params/X"
 * ```
 */
export function validate_param(context: Context, paramName: string, _data: Types.JlParameter): void {
  const currentFunctionName = context.currentFunctionName;
  context.params.add(`${currentFunctionName}/params/${paramName}`);
}
/**
 * @summary 验证function对象的语义
 * @description 验证当前function对象是否符合约束，例如检查参数和语法的引用是否存在。结果保存在context.errors中
 * @param context 解析器的上下文对象
 * @param data 当前的function对象
 */
export function validate_function(context: Context, data: Types.JlFunction): void {
  const paramKeys = Object.keys(data.params);
  context.scope.push("params");
  for (const key of paramKeys) {
    context.scope.push(key);
    validate_param(context, key, data.params[key]);
    context.scope.pop();
  }
  context.scope.pop();
  const syntaxKeys = Object.keys(data.syntaxes);
  context.scope.push("syntaxes");
  for (const key of syntaxKeys) {
    context.scope.push(key);
    validate_syntax(context, key, data.syntaxes[key]);
    context.scope.pop();
  }
  context.scope.pop();
}
/**
 * @summary 验证syntax对象的语义
 * @description 验证当前syntax对象是否符合被定义的一系列规则，结果保存在context.errors中
 * @param context 解析器的上下文对象
 * @param syntaxName 当前syntax的键名
 * @param syntax 当前syntax对象
 * @example
 * ```typescript
 * const context: Context = {
 *   currentFunctionName: "plot",
 *   params: new Set(["plot/params/Y"]),
 *   errors: []
 * };
 * const syntax = { params: ["#params/X", "#params/Y"] };
 * validate_syntax(context, 'X_Y', syntax);
 * // context.errors 会包含一个错误:
 * // [{
 * //   scope: "plot",
 * //   message: "被 syntax X_Y 引用的 Parameter X 不存在"
 * // }]
 * ```
 */
export function validate_syntax(context: Context, syntaxName: string, syntax: Types.JlSyntax): void {
  if (syntax.params) {
    checkParamsRefInSyntax(context, syntaxName, syntax);
  }
}
/**
 * @summary 验证Library对象的语法和语义
 * @description 如果data不符合Library的Schema，则直接返回语法错误列表；否则进入语义检查，并返回语义错误列表。
 * @param data 被假定为Library对象
 * @returns 错误列表
 * @example
 * ```typescript
 * const data: Types.JlLibrary = { functions: {} };
 * const errors = validate_library(data);
 * // 由于data不符合Library的Schema，errors中会包含语法错误信息
 * ```
 */
export function validate_library(data: Types.JlLibrary): ValidationError[] {
  const result = Types.JlLibrarySchema.safeParse(data);
  if (!result.success) {
    const newError = new ValidationError([], result.error.message);
    return [newError];
  }
  const errors: ValidationError[] = [];
  const functionKeys = Object.keys(data.functions);
  let currentFunctionName: string = "";
  const params: Set<string> = new Set();
  const scope: string[] = [];
  scope.push("functions");
  for (const key of functionKeys) {
    currentFunctionName = key;
    scope.push(currentFunctionName);
    validate_function({ scope, currentFunctionName, params, errors } satisfies Context, data.functions[key]);
    scope.pop();
    currentFunctionName = "";
  }
  scope.pop();
  return errors;
}
