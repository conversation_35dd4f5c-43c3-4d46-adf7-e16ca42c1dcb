<script lang="ts">
  import ArrowUp from '@lucide/svelte/icons/arrow-up';
  import Square from '@lucide/svelte/icons/square';
  import Paperclip from '@lucide/svelte/icons/paperclip';
  import Atom from '@lucide/svelte/icons/atom';
  import { Button } from '$lib/components/ui/button';
  import { Textarea } from '$lib/components/ui/textarea';
  import { Chat } from '@askme/lib-common/chat';
  import { getChatIdContext, getUserPreferancesCookiesContext } from '@askme/lib-common';
  import * as Tooltip from '$lib/components/ui/tooltip';
  import FileCard from './FileCard.svelte';
  import { toast } from 'svelte-sonner';
  import type { FileInfo, UIAttachment } from '@askme/lib-common';
  import { Toggle } from '$lib/components/ui/toggle/index.js';

  interface Props {
    /** @prop {Chat} chat - Chat实例. */
    chat: Chat;
    /** @prop {number} MAX_FILES - 最大上传文件数量.默认为5. */
    MAX_FILES?: number;
    /** @prop {number} MAX_FILE_SIZE_IN_MB - 最大上传文件大小（MB为单位）.默认为10 */
    MAX_FILE_SIZE_IN_MB?: number;
    /** @prop {number} MAX_INPUT_LENGTH - 最大输入长度.默认为2000. */
    MAX_INPUT_LENGTH?: number;
  }
  const {
    chat = $bindable(),
    MAX_FILES = 5,
    MAX_FILE_SIZE_IN_MB = 10,
    MAX_INPUT_LENGTH = 2000,
  }: Props = $props();

  const MAX_FILE_SIZE = MAX_FILE_SIZE_IN_MB * 1024 * 1024;

  let form: HTMLFormElement;
  const isTextInputed: boolean = $derived(chat.input.trim() !== '');
  const isLLMIdle = $derived(chat.status === 'ready');
  let isDragging = $state(false);
  const fileInfos: FileInfo[] = $state([]);
  const hasFiles = $derived(fileInfos.length > 0);
  const isAllFilesReady = $derived(
    fileInfos.length === 0 ? true : fileInfos.every((file) => file.uploadStatus === 'uploaded'),
  );
  const isUserInput = $derived(
    (isTextInputed && fileInfos.length === 0) || // 只输入文字
      (!isTextInputed && isAllFilesReady && fileInfos.length > 0) || // 只上传文件
      (isTextInputed && isAllFilesReady), // 同时上传图片和文字
  );
  // ⚠️ 必要性
  // 由于每个子元素都会触发dragEnter和dragLeave事件
  // 你无法光靠dragLeave来判断抓取动作是否离开浏览器
  // 当enter时dragCounter+1,leave时dragCounter-1
  // 当dragCounter为0时，你可以安全的判断抓取动作已经离开窗口
  let dragCounter = $state(0);
  let buttonStatus: 'CanSend' | 'Disabled' | 'LetStop' = $derived.by(() => {
    if (isLLMIdle && isUserInput) {
      return 'CanSend';
    } else if (isLLMIdle && !isUserInput) {
      return 'Disabled';
    } else {
      return 'LetStop';
    }
  });

  const chatIdContext = getChatIdContext();
  const userPreferances = getUserPreferancesCookiesContext();

  const isReasoningEnabled = $derived(userPreferances.value.isReasoningEnabled);

  function handleTextareaInput(event: Event) {
    // ⚠️ 延迟更新textarea高度至浏览器重绘前，避免中文输入最后一行时向上滚动
    requestAnimationFrame(() => {
      const textareaDom = event.currentTarget as HTMLTextAreaElement;
      if (!textareaDom) return;
      textareaDom.style.height = 'auto';
      // ⚠️textarea的高度最大限制为视口高度的1/3
      // 这个最大高度限制必须小于max-h-[40vh]动态高度限制
      // 原因是底部toolbar存在一个固定高度，如果也设置为40%会把toolbar顶出视口
      const MAX_HEIGHT_PERCENTAGE = 0.33;
      // ⚠️添加一个额外的小高度
      // 如果不加的话，输入超过一行就会出现滚动条
      const EXTRA_HEIGHT = 2;
      const newHeight = Math.min(
        textareaDom.scrollHeight + EXTRA_HEIGHT,
        window.innerHeight * MAX_HEIGHT_PERCENTAGE,
      );
      textareaDom.style.height = `${newHeight}px`;
    });
  }

  function handleTextareaKeyDown(event: KeyboardEvent) {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault(); // 阻止换行
      if (isUserInput) {
        form?.requestSubmit(); // 触发表单提交
      }
    }
  }

  function handleFormSubmit(event: SubmitEvent) {
    // 新建对话时，需要更新chatIdContext
    if (!chatIdContext().chatId) {
      chatIdContext().chatId = chat.id;
    }
    chat.handleSubmit(event, {
      attachments: fileInfos.map(
        (fi) =>
          ({
            name: fi.file.name,
            url: `${fi.uuid}.${fi.file.name.split('.').pop()?.toLowerCase()}`,
            uuid: fi.uuid,
            fileSize: fi.file.size,
            contentType: fi.file.type,
          }) as UIAttachment,
      ),
      allowEmptySubmit: true,
    });
    fileInfos.length = 0;
  }

  function onFileCardClose(fi: FileInfo) {
    fileInfos.splice(fileInfos.indexOf(fi), 1);
  }

  // ==========文件上传逻辑=====================
  // 处理单击按钮上传文件
  function onAttachmentClick() {
    // 创建隐藏的输入框
    const input = document.createElement('input');
    // 设置文件类型并允许多选
    input.type = 'file';
    input.accept = 'image/*';
    input.multiple = true;
    // 当选择改变时
    input.onchange = async () => {
      // 没有选择文件直接返回
      if (!input.files?.length) return;
      // ⚠️FileList本身不是一个数组，需要Array.from转成数组才能使用数组方法
      const fileArray = Array.from(input.files);
      validAndUpload(fileArray, fileInfos.length);
    };
    input.click();
  }

  // 处理粘贴板上传文件
  function handlePaste(event: ClipboardEvent) {
    // 检查剪贴板中是否有文件
    const items = event.clipboardData?.items;
    if (!items) return;
    // 如果剪贴板内容是纯文本，则直接返回
    if (items.length > 0 && items[0].kind === 'string') {
      return;
    }
    // 过滤掉非文件
    // ⚠️DataTransferItemList本身不是一个数组，需要Array.from转成数组才能使用数组方法
    const fileArray = Array.from(items).reduce((fileArray, item) => {
      if (item.kind === 'file') {
        fileArray.push(item.getAsFile()!);
      }
      return fileArray;
    }, [] as File[]);
    validAndUpload(fileArray, fileInfos.length);
  }

  // --------------拖拽处理---------------
  // 处理文件拖拽进入窗口
  function handleWindowDragEnter(event: DragEvent) {
    // ⚠️必须屏蔽浏览器的默认Drag行为，不然默认会使用浏览器打开你拖拽进浏览器窗口的文件
    // 而不是使用我们的自定义拖拽处理，下面的event.preventDefault()同理
    event.preventDefault();
    dragCounter++;

    // 检查是否有文件被拖拽
    if (event.dataTransfer?.types.includes('Files')) {
      isDragging = true;
    }
  }

  // 处理文件拖拽离开窗口
  function handleWindowDragLeave() {
    dragCounter--;
    if (dragCounter === 0) {
      isDragging = false;
    }
  }

  // 处理拖拽悬浮
  function handleWindowDragOver(event: DragEvent) {
    event.preventDefault();
  }

  // 处理拖拽文件释放，上传文件
  function handleWindowDrop(event: DragEvent) {
    event.preventDefault();
    // 还原拖拽状态
    dragCounter = 0;
    isDragging = false;
    // 获取拖拽的文件
    const droppedFiles = event.dataTransfer?.files;
    if (!droppedFiles?.length) return;
    // ⚠️DataTransferItemList本身不是一个数组，需要Array.from转成数组才能使用数组方法
    const fileArray = Array.from(droppedFiles);
    validAndUpload(fileArray, fileInfos.length);
  }
  // ----------拖拽处理结束-----------------------

  // 校验文件合法性并上传文件
  function validAndUpload(filesToUpload: File[], currentFileCount: number) {
    // 过滤掉不支持的格式
    const validFormatFiles = filesToUpload.filter((file) => file.type.startsWith('image/'));
    if (validFormatFiles.length < filesToUpload.length) {
      toast.error('上传的文件包含未知类型，现仅支持图片附件');
    }
    // 过滤太大的文件
    const validSizeFiles = validFormatFiles.filter((file) => file.size <= MAX_FILE_SIZE);
    if (validSizeFiles.length < validFormatFiles.length) {
      toast.error(`有文件超过${MAX_FILE_SIZE_IN_MB}MB限制`);
    }
    // 过滤掉同名同大小文件(⚠️校验哈希值更准确，但需要await额外计算时间，为了性能，只要是文件名和大小相同，就认为是相同文件)
    const validFiles = validSizeFiles.filter((file) => {
      const isDuplicate = fileInfos.some(
        (f) => f.file.name === file.name && f.file.size === file.size,
      );
      if (isDuplicate) {
        toast.error(`文件${file.name}已存在，未添加`);
      }
      return !isDuplicate;
    });

    // 没有合法文件，终止流程
    if (validFiles.length === 0) return;
    // 检查是否超过总文件数限制，若超过，终止流程
    if (currentFileCount + validFiles.length > MAX_FILES) {
      toast.error(`最多只能上传${MAX_FILES}个文件`);
      return;
    }
    // 取消深度思考
    setReasoningTogglePressed(false);
    // 此次文件添加已校验完成，遍历并上传每个合法文件
    for (const file of validFiles) {
      uploadFile(file);
    }
  }
  // ⚠️单独上传每个文件，而不是单独一个http请求上传所有文件，这样最容易追踪文件上传状态（deepseek也是此类实现）
  async function uploadFile(file: File) {
    // 初始化上传状态
    fileInfos.push({ file, uuid: undefined, uploadStatus: 'notStarted' });
    // formdata对象用于上传数据
    const formData = new FormData();
    formData.append('file', file);
    // 取得上传文件信息引用
    const currentFileInfo = fileInfos.find((fi) => fi.file === file)!;
    try {
      currentFileInfo.uploadStatus = 'uploading';
      // 网络请求
      const response = await fetch('/api/files/upload', {
        method: 'POST',
        body: formData,
      });
      if (!response.ok) throw new Error('Upload file failed');
      const result = await response.json();
      currentFileInfo.uuid = result.uuid;
      currentFileInfo.uploadStatus = 'uploaded';
    } catch (error) {
      currentFileInfo.uploadStatus = 'uploadFailed';
      console.error('文件上传错误:', error);
    }
  }
  // ==========文件上传逻辑结束=====================

  // =========深度思考控制逻辑开始=====================
  function getReasoningTogglePressed() {
    return isReasoningEnabled ?? false;
  }
  function setReasoningTogglePressed(newPressed: boolean) {
    userPreferances.value = { ...userPreferances.value, isReasoningEnabled: newPressed };
  }
  // =========深度思考控制逻辑结束=====================
</script>

<!--
@component
UserInput用于接受用户输入。它带有自带样式。

- 用法:
  ``` svelte
  <UserInput {chat}
  {MAX_FILES}
  {MAX_FILE_SIZE_IN_MB}
  />
  ```
-->

<svelte:window
  ondragenter={handleWindowDragEnter}
  ondragleave={handleWindowDragLeave}
  ondragover={handleWindowDragOver}
  ondrop={handleWindowDrop}
/>

<form bind:this={form} onsubmit={handleFormSubmit}>
  <div
    class="bg-background @container overflow-hidden rounded-2xl border dark:border-zinc-700"
    style="container-type: inline-size;"
  >
    {#if fileInfos.length > 0}
      <div
        class="bg-background grid grid-cols-2 gap-2 p-2 @md:grid-cols-3 @xl:grid-cols-4 @3xl:grid-cols-5"
      >
        {#each fileInfos as fi (`${fi.file.name}-${fi.file.size}`)}
          <FileCard
            fileName={fi.file.name}
            fileSize={fi.file.size}
            uuid={fi.uuid}
            status={fi.uploadStatus}
            onClose={() => {
              onFileCardClose(fi);
            }}
            imgFile={fi.file.type.startsWith('image/') ? fi.file : undefined}
          />
        {/each}
      </div>
    {/if}

    <Textarea
      onkeydown={handleTextareaKeyDown}
      bind:value={chat.input}
      class="bg-muted max-h-[40vh] resize-none overflow-y-auto rounded-t-2xl rounded-b-none border-b-0 text-base! shadow-none focus-visible:border-inherit focus-visible:ring-0 focus-visible:ring-transparent dark:border-zinc-700"
      placeholder="发送一条消息，shift+回车换行"
      maxlength={MAX_INPUT_LENGTH}
      onpaste={handlePaste}
      oninput={handleTextareaInput}
    />
    <div class="bg-muted flex items-center justify-between rounded-b-2xl p-2">
      <!-- 底部工具栏左侧 -->
      <div class="flex items-center gap-2">
        <Tooltip.Provider delayDuration={1}>
          <Tooltip.Root>
            <Tooltip.Trigger>
              {#snippet child({ props })}
                <div class={[hasFiles && 'cursor-not-allowed']} {...props}>
                  <Toggle
                    aria-label="Toggle Reasoning"
                    class={[
                      hasFiles && 'cursor-not-allowed',
                      'data-[state=on]:bg-toggle data-[state=on]:hover:bg-toggle-hover  hover:bg-input bg-background data-[state=on]:text-toggle-text rounded-2xl',
                    ]}
                    bind:pressed={getReasoningTogglePressed, setReasoningTogglePressed}
                    variant="outline"
                    disabled={hasFiles}
                  >
                    <Atom class="size-4" />
                    深度思考
                  </Toggle>
                </div>
              {/snippet}
            </Tooltip.Trigger>
            <Tooltip.Content>
              {#if hasFiles}
                <p>请先删除附件</p>
              {:else}
                <p>先思考后回答,解决推理问题</p>
              {/if}
            </Tooltip.Content>
          </Tooltip.Root>
        </Tooltip.Provider>
      </div>
      <!-- 底部工具栏右侧 -->
      <div class="flex items-center gap-2">
        <Tooltip.Provider delayDuration={1}>
          <Tooltip.Root>
            <Tooltip.Trigger>
              {#snippet child({ props })}
                <Button
                  {...props}
                  variant="ghost"
                  class="mr-2 h-fit p-1.5 dark:border-zinc-600"
                  onclick={onAttachmentClick}
                >
                  <Paperclip />
                  <span class="sr-only"
                    >上传附件，仅支持图片。最多{MAX_FILES}个，每个{MAX_FILE_SIZE_IN_MB}MB。</span
                  >
                </Button>
              {/snippet}
            </Tooltip.Trigger>
            <Tooltip.Content>
              <p class="text-sm font-semibold">上传附件，仅支持图片</p>
              <p>最多{MAX_FILES}个，每个{MAX_FILE_SIZE_IN_MB}MB。</p>
            </Tooltip.Content>
          </Tooltip.Root>
        </Tooltip.Provider>
        {#if buttonStatus === 'CanSend' || buttonStatus === 'Disabled'}
          <Tooltip.Provider delayDuration={1}>
            <Tooltip.Root>
              <Tooltip.Trigger>
                {#snippet child({ props })}
                  <div class={[!isUserInput && 'cursor-not-allowed']} {...props}>
                    <Button
                      type="submit"
                      class="h-fit rounded-full border p-1.5 dark:border-zinc-600"
                      disabled={!isUserInput && isLLMIdle}
                    >
                      <ArrowUp />
                    </Button>
                  </div>
                {/snippet}
              </Tooltip.Trigger>
              <Tooltip.Content>
                {#if isUserInput}
                  发送
                {:else if !isAllFilesReady}
                  等待附件上传完成，或删除异常附件
                {:else}
                  请输入问题
                {/if}
              </Tooltip.Content>
            </Tooltip.Root>
          </Tooltip.Provider>
        {:else if buttonStatus === 'LetStop'}
          <Button class="h-fit rounded-full border p-1.5 dark:border-zinc-600" onclick={chat.stop}>
            <Square />
          </Button>
        {/if}
      </div>
    </div>
  </div>
</form>

{#if isDragging}
  <div class="fixed inset-0 z-50 flex items-center justify-center bg-black/30">
    <div class="border-primary bg-background rounded-lg border-2 border-dashed p-8 text-center">
      <Paperclip class="text-primary mx-auto mb-4 h-12 w-12" />
      <p class="text-lg font-medium">释放鼠标上传图片</p>
      <p class="text-muted-foreground mt-2 text-sm">
        最多{MAX_FILES}个，每个{MAX_FILE_SIZE_IN_MB}MB
      </p>
    </div>
  </div>
{/if}
