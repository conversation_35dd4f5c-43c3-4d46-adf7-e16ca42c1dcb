<script lang="ts">
  import { Button } from '$lib/components/ui/button';
  import Copy from '@lucide/svelte/icons/copy';
  import { toast } from 'svelte-sonner';
  import type { CodeSnippet } from '@askme/lib-common';
  import * as Tooltip from '$lib/components/ui/tooltip';

  interface Props {
    /** @prop {CodeSnippet} codeSnippet - ChatBot的消息. */
    codeSnippet: CodeSnippet;
  }
  const { codeSnippet }: Props = $props();
</script>

<!-- Tab bar with language and copy button -->

<div class="text-xs font-medium text-zinc-600 dark:text-zinc-400">
  {codeSnippet.language}
</div>
<Tooltip.Provider>
  <Tooltip.Root>
    <Tooltip.Trigger>
      {#snippet child({ props })}
        <Button
          {...props}
          variant="ghost"
          size="sm"
          class="h-7 px-2 text-zinc-500 hover:text-zinc-900 dark:text-zinc-400 dark:hover:text-zinc-50"
          onclick={() => {
            navigator.clipboard.writeText(codeSnippet.code).then(() => {
              toast.success('已复制');
            });
          }}
        >
          <Copy size={14} />
          <span class="sr-only">复制代码</span>
        </Button>
      {/snippet}
    </Tooltip.Trigger>
    <Tooltip.Content>
      <p>复制代码</p>
    </Tooltip.Content>
  </Tooltip.Root>
</Tooltip.Provider>
