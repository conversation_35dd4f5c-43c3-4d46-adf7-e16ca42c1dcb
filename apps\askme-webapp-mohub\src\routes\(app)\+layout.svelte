<script lang="ts">
  import * as Sidebar from '@askme/lib-ui/shadcn-sidebar';
  import { Toaster } from '@askme/lib-ui/shadcn-sonner';
  import { ChatSidebar } from '@askme/lib-ui';
  import type { LayoutProps } from './$types';
  import { goto } from '$app/navigation';
  import {
    setChatHistoryContext,
    getChatHistoryContext,
    setVoteContext,
    hasChatHistoryContext,
    hasVoteContext,
    hasChatIdContext,
    setChatIdContext,
    KeyedChatStore,
    setChatContext,
    hasChatContext,
    hasUserPreferancesCookiesContext,
    setUserPreferancesCookiesContext,
    UseCookies,
  } from '@askme/lib-common';
  import type { ChatModel } from '@askme/lib-common';
  import { SvelteMap } from 'svelte/reactivity';
  import { DEFAULT_CHAT_TITLE, userPreferancesCookiesValidator } from '$lib/config';
  import { onMount } from 'svelte';

  const { data, children }: LayoutProps = $props();

  // 初始化侧边栏聊天历史上下文
  if (!hasChatHistoryContext()) {
    const chats = $state([] as ChatModel[]);
    setChatHistoryContext(chats);
  }
  const chats = getChatHistoryContext();

  // 初始化投票上下文
  if (!hasVoteContext()) {
    const voteMap = new SvelteMap<string, boolean>();
    setVoteContext(voteMap);
  }

  // ⚠️： 由于sveltekit存在一个已知的设计问题或bug，replacestate()前端路由不会响应式地更新page state
  // 因此从url拿取chatId并不是一个稳定的方法（尤其是新建chat的时候），如无特殊情况尽量使用context去更新和拿取当前对话的chatId
  // 初始化chatId上下文
  if (!hasChatIdContext()) {
    const chatId = $state({ chatId: undefined });
    setChatIdContext(() => chatId);
  }

  // 初始化Chat上下文
  if (!hasChatContext()) {
    const keyedChatStore = $state<KeyedChatStore>(new KeyedChatStore());
    setChatContext(keyedChatStore);
  }

  // 初始化用户偏好cookie上下文
  if (!hasUserPreferancesCookiesContext()) {
    const userPreferancesCookies = $state(
      new UseCookies('userPreferances', data.cookiesData, userPreferancesCookiesValidator),
    );
    setUserPreferancesCookiesContext(userPreferancesCookies);
  }

  // TODO: UseLocalStorage用法
  // if (!hasUserPreferancesContext()) {
  //   setUserPreferancesContext(
  //     useLocalStorage('userPreferances', DEFAULT_USER_PREFERANCES, userPreferancesValidator),
  //   );
  // }

  chats.push(
    ...data.chats.map((c) => ({
      ...c,
      createdAt: new Date(c.createdAt), // 在前端转为用户的当地时间（⚠️：在服务器转换会引发时区不一致）
    })),
  );

  // 组件挂载时检查没有生成标题的消息，若有遍历并且重新生成标题
  // ⚠️务必在onMount中执行避免影响首屏渲染速度
  onMount(() => {
    chats.forEach(async (c) => {
      if (c.title === DEFAULT_CHAT_TITLE) {
        const response = await fetch('/api/summary', {
          method: 'POST',
          body: JSON.stringify({
            chatID: c.id,
          }),
          headers: {
            'Content-Type': 'application/json',
          },
        });
        const result = await response.json();
        // 前端更换标题
        if (response.ok) {
          // 前端sidebar更换标题;
          c.title = result.text;
        }
      }
    });
  });
</script>

<Toaster position="top-center" theme="light" richColors />
<Sidebar.Provider>
  <ChatSidebar
    sidebarTitle={data.defaultTitle}
    newChatButtonTitle="开启新对话"
    {chats}
    onNewChat={() => goto('/chat', { invalidate: ['app:index'] })}
  />
  <main class="h-full w-full min-w-0 overflow-hidden">
    {@render children()}
  </main>
</Sidebar.Provider>
