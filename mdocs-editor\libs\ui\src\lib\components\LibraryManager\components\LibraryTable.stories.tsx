import type { Meta, StoryObj } from '@storybook/react-vite';
import { LibraryTable } from './LibraryTable';
import { Library } from '../LibraryManager';

const meta: Meta<typeof LibraryTable> = {
  title: 'Components/LibraryManager/LibraryTable',
  component: LibraryTable,
  parameters: {
    layout: 'fullscreen',
    docs: {
      description: {
        component: 'Library 表格组件，支持分页、响应式设计和操作菜单。',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    libraries: {
      description: 'Library 列表数据',
      control: 'object',
    },
    loading: {
      description: '加载状态',
      control: 'boolean',
    },
    currentPage: {
      description: '当前页码',
      control: 'number',
    },
    pageSize: {
      description: '每页显示数量',
      control: 'number',
    },
    total: {
      description: '总数据量',
      control: 'number',
    },
    onLibraryClick: {
      description: 'Library 点击回调',
    },

    onExport: {
      description: '导出回调',
    },
    onDelete: {
      description: '删除回调',
    },
    onPageChange: {
      description: '页码变更回调',
    },
  },
};

export default meta;
type Story = StoryObj<typeof LibraryTable>;

// 模拟数据
const generateMockLibraries = (count: number): Library[] => {
  return Array.from({ length: count }, (_, index) => ({
    id: `lib-${String(index + 1).padStart(3, '0')}`,
    name: `Library ${index + 1}`,
    latestVersion: `v2.0.${index}`,
    lastUpdated: new Date(2024, 0, 15 + index),
    summary: `这是第 ${index + 1} 个 Library 的摘要信息，包含了相关的功能描述`,
  }));
};

const mockLibraries = generateMockLibraries(25);

// 通用回调函数
const createCallbacks = () => ({
  onLibraryClick: (library: Library) => console.log('onLibraryClick', library),
  onExport: (library: Library) => console.log('onExport', library),
  onDelete: (library: Library) => console.log('onDelete', library),
  onPageChange: (page: number) => console.log('onPageChange', page),
});

/**
 * 默认状态
 */
export const Default: Story = {
  args: {
    libraries: mockLibraries.slice(0, 10),
    loading: false,
    currentPage: 1,
    pageSize: 10,
    total: 10,
    ...createCallbacks(),
  },
};

/**
 * 空数据状态
 */
export const Empty: Story = {
  args: {
    libraries: [],
    loading: false,
    currentPage: 1,
    pageSize: 10,
    total: 0,
    ...createCallbacks(),
  },
};


/**
 * 分页数据
 */
export const WithPagination: Story = {
  args: {
    libraries: mockLibraries.slice(0, 25),
    loading: false,
    currentPage: 1,
    pageSize: 10,
    total: 25,
    ...createCallbacks(),
  },
};
