import React from "react";
import { ChevronDown, ChevronRight } from "lucide-react";

/**
 * 可折叠部分组件的属性接口
 * @interface CollapsibleSectionProps
 * @description 用于可折叠部分的属性类型定义。
 * @property title 部分标题
 * @property sectionKey 部分的唯一标识符，用于状态管理
 * @property children 子内容
 * @property defaultExpanded 是否默认展开
 * @property collapsedSections 折叠状态映射
 */
export interface CollapsibleSectionProps {
  /** 部分标题 */
  title: string;
  /** 部分的唯一标识符，用于状态管理 */
  sectionKey: string;
  /** 子内容 */
  children: React.ReactNode;
  /** 是否默认展开 */
  defaultExpanded?: boolean;
  /** 折叠状态映射 */
  collapsedSections: Record<string, boolean>;
  /** 切换折叠状态的回调函数 */
  onToggleSection: (sectionKey: string) => void;
}

/**
 * 可折叠部分组件
 * 
 * 提供一个可以展开/折叠的内容区域，带有标题和状态指示器
 */
export const CollapsibleSection: React.FC<CollapsibleSectionProps> = ({
  title,
  sectionKey,
  children,
  defaultExpanded = true,
  collapsedSections,
  onToggleSection,
}) => {
  const isCollapsed = collapsedSections[sectionKey] ?? !defaultExpanded;

  return (
    <div className="space-y-4">
      {/* 标题栏 */}
      <div
        className="flex items-center gap-2 cursor-pointer hover:bg-gray-50 p-2 rounded-md -m-2 transition-colors"
        onClick={() => onToggleSection(sectionKey)}
        role="button"
        tabIndex={0}
        onKeyDown={(e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            onToggleSection(sectionKey);
          }
        }}
      >
        {/* 折叠状态图标 */}
        {isCollapsed ? (
          <ChevronRight className="w-4 h-4 text-gray-500" />
        ) : (
          <ChevronDown className="w-4 h-4 text-gray-500" />
        )}
        
        {/* 标题 */}
        <h3 className="text-lg font-semibold text-gray-900 select-none">
          {title.includes('*') ? (
            <>
              {title.replace(' *', '')}
              <span className="text-red-500 ml-1">*</span>
            </>
          ) : (
            title
          )}
        </h3>
      </div>

      {/* 内容区域 */}
      {!isCollapsed && (
        <div className="ml-6 space-y-4 animate-in slide-in-from-top-2 duration-200">
          {children}
        </div>
      )}
    </div>
  );
};
