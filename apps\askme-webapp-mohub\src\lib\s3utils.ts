import type { Attachment } from 'ai';
import * as Minio from 'minio';
import { streamToBuffer } from '$lib/serverUtils';
import type { RequestS3Config } from '$lib/types';

/** Build S3 full keypath with the existence of prefix */
export function buildFullPath(parts: string[], prefix?: string) {
  if (prefix?.trim() === '') {
    return parts.join('/');
  } else {
    return `${prefix?.trim()}/${parts.join('/')}`;
  }
}

/**
 * 从s3获取文件base64
 * @param minioClient minio客户端
 * @param bucket 存储桶名
 * @param objectName 对象名
 * @returns 文件base64
 */
export async function getFileBase64(
  minioClient: Minio.Client,
  bucket: string,
  objectName: string,
): Promise<string> {
  const stream = await minioClient.getObject(bucket, objectName);
  const chunks: Buffer[] = [];

  return new Promise((resolve, reject) => {
    stream.on('data', (chunk) => {
      chunks.push(chunk);
    });

    stream.on('end', () => {
      const buffer = Buffer.concat(chunks);
      const base64 = buffer.toString('base64');
      resolve(base64);
    });

    stream.on('error', (err) => {
      reject(err);
    });
  });
}

/**
 * 根据用户id，attachment对象，存储桶名，存储桶前缀从s3获取文件blob
 * @param minioClient minio客户端
 * @param attachment attachment对象
 * @param userId 用户id
 * @param bucketName 存储桶名
 * @param prefix 存储桶前缀
 * @returns 文件blob
 */
export async function getFileBlobformS3(
  minioClient: Minio.Client,
  attachment: Attachment,
  userId: number,
  bucketName: string,
  prefix?: string,
): Promise<Blob> {
  // ⚠️下面的的a.url实际是部分url(文件名)，需要根据用户id拼接完整url
  const objectStream = await minioClient.getObject(
    bucketName,
    buildFullPath([`${userId}`, attachment.url], prefix),
  );
  const buffer = await streamToBuffer(objectStream);
  const blob = new Blob([buffer], { type: attachment.contentType });
  return blob;
}

/**
 * 根据部署环境构造多模态LLM请求文件url
 * @param s3Config s3配置
 * @param deployment_target
 * @param attachments
 */
export async function buildMultiModalLLMRequestFileURL(
  s3Config: RequestS3Config,
  deploymentTarget: string,
  attachment: Attachment,
): Promise<string> {
  const fileKey = buildFullPath([`${s3Config.userId}`, attachment.url], s3Config.prefix);
  let fileurl;
  if (deploymentTarget === 'PRIVATE') {
    // 内网部署，将文件base64编码(Data URL)给LLM厂商
    const fileBase64 = await getFileBase64(s3Config.minioClient, s3Config.bucketName, fileKey);
    fileurl = `data:${attachment.contentType};base64,${fileBase64}`;
  } else if (deploymentTarget === 'PUBLIC') {
    // 公网部署，提供预签名url给LLM厂商
    const EXPIRE_IN_SECONDS = 5 * 60;
    fileurl = await s3Config.minioClient.presignedGetObject(
      s3Config.bucketName,
      fileKey,
      EXPIRE_IN_SECONDS,
    );
  } else {
    // 一般不会运行到这里，其他选项在构造config时会报错并停止服务端程序
    throw new Error('Deployment target not supported');
  }
  return fileurl;
}
