<script module lang="ts">
  //    👆 notice the module context, defineMeta does not work in a regular <script> tag - instance
  import { defineMeta } from '@storybook/addon-svelte-csf';

  import SuggestedActions from './SuggestedActions.svelte';
  import { Chat } from '@askme/lib-common/chat';
  import type { SuggestedActionModel } from '@askme/lib-common';

  //      👇 Get the Story component from the return value
  const { Story } = defineMeta({
    title: 'UI/SuggestedActions',
    component: SuggestedActions,
    decorators: [
      /* ... */
    ],
    parameters: {
      /* ... */
    },
  });
  const suggestedActions = [
    {
      title: 'What are the advantages',
      label: 'of using SvelteKit?',
      action: 'What are the advantages of using SvelteKit?',
    },
    {
      title: 'Write code to',
      label: `demonstrate d<PERSON><PERSON><PERSON>'s algorithm`,
      action: `Write code to demonstrate d<PERSON><PERSON><PERSON>'s algorithm`,
    },
    {
      title: 'Help me write an essay',
      label: `about silicon valley`,
      action: `Help me write an essay about silicon valley`,
    },
    {
      title: 'What is the weather like',
      label: 'in San Francisco?',
      action: 'What is the weather like in San Francisco?',
    },
  ] as SuggestedActionModel[];
</script>

<Story
  name="Default"
  args={{
    suggestedActions,
    chatClient: new Chat(),
  }}
/>
