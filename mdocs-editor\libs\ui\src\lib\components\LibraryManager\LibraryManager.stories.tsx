import type { Meta, StoryObj } from '@storybook/react-vite';
import { LibraryManager } from './LibraryManager';
import type { Library } from './LibraryManager';

const meta: Meta<typeof LibraryManager> = {
  title: 'Components/LibraryManager',
  component: LibraryManager,
  parameters: {
    layout: 'padded',
    docs: {
      description: {
        component: `
Library 管理组件，提供完整的 Library 管理功能
        `,
      },
    },
  },
  tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof LibraryManager>;

// 模拟数据
const mockLibraries: Library[] = [
  {
    name: 'React Utils',
    latestVersion: '2.0.0',
    lastUpdated: new Date('2024-01-15'),
    summary: '常用的 React 工具函数库，包含各种实用的 Hook 和工具函数常用的 React 工具函数库，包含各种实用的 Hook 和工具函数',
  },
  {
    name: 'UI Components',
    latestVersion: '0.2.0',
    lastUpdated: new Date('2024-01-10'),
    summary: '可复用的 UI 组件集合',
  },
  {
    name: 'Data Processing',
    latestVersion: '4.0.0',
    lastUpdated: new Date('2024-01-20'),
    summary: '数据处理和转换工具',
  },
  {
    name: 'Authentication',
    latestVersion: '2.0.0',
    lastUpdated: new Date('2024-01-12'),
    summary: '用户认证和授权相关功能',
  },
  {
    name: 'API Client',
    latestVersion: '2.1.0',
    lastUpdated: new Date('2024-01-18'),
    summary: 'HTTP 客户端封装库',
  },
];

export const Default: Story = {
  args: {
    libraries: mockLibraries,
    loading: false,
    onLibraryClick: (library: Library) => console.log('Library clicked:', library),
    onImportLibrary: (file: File) => console.log('Import library:', file.name),
    onExportLibrary: (library: Library) => console.log('Export library:', library),
    onExportAllLibraries: () => console.log('Export all libraries'),
    onDeleteLibrary: (libraryId: string) => console.log('Delete library:', libraryId),
  },
}

export const Empty: Story = {
  args: {
    libraries: [],
    loading: false,
    onLibraryClick: (library: Library) => console.log('Library clicked:', library),
    onImportLibrary: (file: File) => console.log('Import library:', file.name),
    onExportLibrary: (library: Library) => console.log('Export library:', library),
    onExportAllLibraries: () => console.log('Export all libraries'),
    onDeleteLibrary: (libraryId: string) => console.log('Delete library:', libraryId),
  },
};



export const ManyLibraries: Story = {
  args: {
    libraries: Array.from({ length: 25 }, (_, i) => ({
      name: `Library ${i + 1}`,
      latestVersion: `${Math.floor(Math.random() * 3) + 1}.${Math.floor(Math.random() * 5)}.${Math.floor(Math.random() * 10)}`,
      lastUpdated: new Date(2024, 0, Math.floor(Math.random() * 30) + 1),
      summary: `这是第 ${i + 1} 个 Library 的描述信息`,
    })),
    loading: false,
    onLibraryClick: (library: Library) => console.log('Library clicked:', library),
    onImportLibrary: (file: File) => console.log('Import library:', file.name),
    onExportLibrary: (library: Library) => console.log('Export library:', library),
    onExportAllLibraries: () => console.log('Export all libraries'),
    onDeleteLibrary: (libraryId: string) => console.log('Delete library:', libraryId),
  },
};
