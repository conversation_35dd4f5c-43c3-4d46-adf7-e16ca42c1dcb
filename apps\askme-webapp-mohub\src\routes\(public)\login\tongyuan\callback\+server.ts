import {
  generateSessionToken,
  createSession,
  setSessionTokenCookie,
  createUserIfNotExists,
} from '$lib/baseAuth';
import { config } from '$lib/config.server';
import type { RequestEvent } from '@sveltejs/kit';
import * as LogFormat from '@askme/lib-common/logformat';
import { error } from '@sveltejs/kit';
import serverCtx from '$lib/context.server';
import { getUserInfoByOAuthProvider, buildOauthAuthorizationHeader } from '$lib/oauthUtils';

// 处理tongyuan OAuth2回调
export async function GET(event: RequestEvent): Promise<Response> {
  if (config.oauth.provider.toUpperCase() !== 'TONGYUAN') {
    return new Response('Unauthorized', {
      status: 401,
    });
  }

  // 验证Oauth2 callback 参数
  const code = event.url.searchParams.get('code');
  if (!code) {
    console.debug(
      LogFormat.debug('Missing authorization code in OAuth2 callback query parameters.'),
    );
  }
  // auth code换access token⬇️

  let tokenResponseJson: {
    code: number;
    data: {
      access_token: string;
      expires_in: number;
      refresh_token: string;
      id_token: string;
      scope: string;
      token_type: string;
    };
    message: string;
    time: number;
  };
  try {
    const accessTokenResponse = await fetch(config.oauth.token_url, {
      method: 'POST',
      headers: {
        Authorization: buildOauthAuthorizationHeader(
          config.oauth.application_id,
          config.oauth.application_secret,
        ),
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        grant_type: 'authorization_code',
        code: code!,
        redirect_uri: config.oauth.redirect_uri,
      }).toString(),
    });
    tokenResponseJson = await accessTokenResponse.json();
  } catch {
    console.debug(LogFormat.debug('Failed to validate authorization code.'));
    // Invalid code or client credentials
    return new Response('Unauthorized', {
      status: 401,
    });
  }

  let userInfo;
  // 用token请求用户信息
  try {
    userInfo = await getUserInfoByOAuthProvider('TONGYUAN', tokenResponseJson.data.access_token);
  } catch (e) {
    console.error(LogFormat.error('Failed to get user info.' + e + 'Provider:TONGYUAN'));
    return new Response('Server Error', {
      status: 500,
    });
  }

  let user;
  try {
    user = await createUserIfNotExists(serverCtx.prisma, userInfo, 'TONGYUAN');
  } catch (e) {
    console.error(LogFormat.error('Failed to create user.' + e));
    return error(500, 'Server Error');
  }

  const MS_IN_S = 1000;
  // 创建会话，设置cookie
  const sessionToken = generateSessionToken();
  const session = await createSession(
    serverCtx.prisma,
    sessionToken,
    tokenResponseJson.data.access_token,
    tokenResponseJson.data.refresh_token,
    new Date(Date.now() + tokenResponseJson.data.expires_in * MS_IN_S),
    user.id,
    false,
  );
  setSessionTokenCookie(event, sessionToken, session.expiresAt);

  return new Response(null, {
    status: 302,
    headers: {
      Location: '/',
    },
  });
}
