<script lang="ts">
  import type { LayoutProps } from './$types';
  import '../app.css';
  import { ModeWatcher } from 'mode-watcher';
  import { onMount } from 'svelte';

  const { children, data }: LayoutProps = $props();

  onMount(() => {
    if (!data.enableThemeSwitcher) {
      if (localStorage.getItem('mode-watcher-mode') != data.mode) {
        localStorage.setItem('mode-watcher-mode', data.mode);
      }
      if (localStorage.getItem('mode-watcher-theme') != data.theme) {
        localStorage.setItem('mode-watcher-theme', data.theme);
      }
    }
  });
</script>

<!-- ⚠️如果localstorage里已经存储了theme和mode,默认读取localstorage的值.而非这里的值 -->
<ModeWatcher />
{@render children()}
