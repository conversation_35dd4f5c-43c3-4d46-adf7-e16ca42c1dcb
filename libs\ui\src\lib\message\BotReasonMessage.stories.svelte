<script module>
  //    👆 notice the module context, defineMeta does not work in a regular <script> tag - instance
  import { defineMeta } from '@storybook/addon-svelte-csf';

  import BotReasonMessage from './BotReasonMessage.svelte';

  //      👇 Get the Story component from the return value
  const { Story } = defineMeta({
    title: 'UI/BotReasonMessage',
    component: BotReasonMessage,
    decorators: [
      /* ... */
    ],
    parameters: {
      /* ... */
    },
  });
</script>

<Story
  name="Default"
  args={{
    isExpanded: true,
    isReasoning: false,
    message: 'Hello, I am a chatbot, now I reasoning.',
    reasoningTitle: '深度思考中',
    reasonFinishedTitle: '已深度思考',
  }}
/>
