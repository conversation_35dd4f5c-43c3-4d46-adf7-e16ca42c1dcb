import React, { useMemo, useCallback } from "react";
import Editor from "@monaco-editor/react";
import type { FunctionContent } from "../FunctionEditor";

/**
 * JSON视图组件的属性接口
 */
export interface JsonViewProps {
  /** 函数内容数据 */
  functionData: Partial<FunctionContent>;
  /** 函数数据更新回调 */
  onDataChange: (data: Partial<FunctionContent>) => void;
  /** 是否处于加载状态 */
  isLoading?: boolean;
}

/**
 * JSON视图组件
 *
 * 负责渲染函数编辑器的JSON视图，提供：
 * - JSON格式的数据编辑
 * - 语法高亮
 * - 格式化
 * - 错误处理
 */
export const JsonView: React.FC<JsonViewProps> = ({
  functionData,
  onDataChange,
  isLoading = false,
}) => {
  // JSON 字符串状态
  const jsonString = useMemo(() => {
    return JSON.stringify(functionData, null, 2);
  }, [functionData]);

  /**
   * JSON 编辑器变化处理
   */
  const handleJsonChange = useCallback((value: string | undefined) => {
    if (!value) return;

    try {
      const parsed = JSON.parse(value);
      onDataChange(parsed);
    } catch (error) {
      console.warn('JSON 解析错误:', error);
    }
  }, [onDataChange]);

  return (
    <div className="h-full w-full">
      <Editor
        height="100%"
        defaultLanguage="json"
        value={jsonString}
        onChange={handleJsonChange}
        options={{
          readOnly: isLoading,
          minimap: { enabled: false },
          scrollBeyondLastLine: false,
          fontSize: 14,
          lineNumbers: 'on',
          wordWrap: 'on',
          automaticLayout: true,
          formatOnPaste: true,
          formatOnType: true,
        }}
        theme="vs"
      />
    </div>
  );
};