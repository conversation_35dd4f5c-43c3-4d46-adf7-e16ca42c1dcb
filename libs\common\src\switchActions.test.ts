import { describe, it, expect } from 'vitest';
import { SwitchAction, insertAction } from './switchActions';
import type { TreeNodeInfo } from './messageTree';

describe('insertAction', () => {
  it('should throw error when message ID not found in info map', () => {
    const actions: SwitchAction[] = [];
    const newAction: SwitchAction = { messageId: 'nonexistent', childIndex: 0 };
    const infoMap = new Map<string | null, TreeNodeInfo>();

    expect(() => {
      insertAction(actions, newAction, infoMap);
    }).toThrow('Message ID not found in info map');
  });

  it('should insert action at beginning of empty list', () => {
    const actions: SwitchAction[] = [];
    const newAction: SwitchAction = { messageId: 'msg1', childIndex: 0 };
    const infoMap = new Map<string | null, TreeNodeInfo>([
      ['msg1', { level: 1, parentMsgId: null, childCount: 0 }],
    ]);

    const result = insertAction(actions, newAction, infoMap);
    expect(result).toEqual([newAction]);
  });

  it('should insert action in correct position based on level', () => {
    const existingAction: SwitchAction = { messageId: 'msg1', childIndex: 0 };
    const actions: SwitchAction[] = [existingAction];

    const infoMap = new Map<string | null, TreeNodeInfo>([
      ['msg1', { level: 1, parentMsgId: null, childCount: 1 }],
      ['msg2', { level: 2, parentMsgId: 'msg1', childCount: 0 }],
      ['msg3', { level: 0, parentMsgId: null, childCount: 0 }],
    ]);

    // Insert at end (higher level)
    const higherAction: SwitchAction = { messageId: 'msg2', childIndex: 1 };
    let result = insertAction(actions, higherAction, infoMap);
    expect(result).toEqual([existingAction, higherAction]);

    // Insert at beginning (lower level)
    const lowerAction: SwitchAction = { messageId: 'msg3', childIndex: 0 };
    result = insertAction(actions, lowerAction, infoMap);
    expect(result).toEqual([lowerAction]);
  });

  it('should handle null messageId', () => {
    const actions: SwitchAction[] = [];
    const newAction: SwitchAction = { messageId: null, childIndex: 0 };
    const infoMap = new Map<string | null, TreeNodeInfo>([
      [null, { level: 0, parentMsgId: null, childCount: 1 }],
    ]);

    const result = insertAction(actions, newAction, infoMap);
    expect(result).toEqual([newAction]);
  });

  it('should maintain order for same level actions', () => {
    const existingAction: SwitchAction = { messageId: 'msg1', childIndex: 0 };
    const actions: SwitchAction[] = [existingAction];

    const infoMap = new Map<string | null, TreeNodeInfo>([
      ['msg1', { level: 1, parentMsgId: null, childCount: 1 }],
      ['msg2', { level: 1, parentMsgId: null, childCount: 0 }],
    ]);

    const sameLevel: SwitchAction = { messageId: 'msg2', childIndex: 1 };
    const result = insertAction(actions, sameLevel, infoMap);
    expect(result).toEqual([sameLevel]);
  });
});
