import { check_library } from "@mdocs/schema";
import type { PrismaClient } from "../generated/prisma";

function validateExtraJsonSchema(data: string): boolean {
  const jsonObject = JSON.parse(data);
  /**
   * 插入一个空的functions成员，这样可以在假设functions部分合法的情况下检查extra部分
   */
  jsonObject.functions = {};
  const modifiedJsonString = JSON.stringify(jsonObject);
  const errors = check_library(modifiedJsonString);
  return errors.length === 0;
}
/**
 * @summary 创建一个空的library历史记录(不包含functions), 如果libraryName对应的library记录不存在则先创建一个library记录
 * @param prisma prisma client
 * @param libraryName library名称
 * @param historyData 历史记录的必要成员
 * @returns Promise<{curLibrary: JlLibrary, history: JlLibraryHistory}> 返回创建或更新的library记录和新创建的历史记录
 * @example
 * ```typescript
 * const prisma = new PrismaClient();
 * const { updatedLibrary, history } = await createEmptyLibraryHistory(prisma, "Test Library", {
 *   version: "1.0.0",
 *   creator: "admin",
 *   createdAt: new Date(),
 *   extra: `
 *   {
 *     "summary":
 *     {
 *       "zh": "图形",
 *       "en": "Graph"
 *     }
 *   }
 *   `,
 * });
 * ```
 */
async function createEmptyLibraryHistory(
  prisma: PrismaClient,
  libraryName: string,
  historyData: {
    version: string;
    creator: string;
    extra: string;
  },
) {
  // Validate JSON schema
  if (!validateExtraJsonSchema(historyData.extra)) {
    throw new Error("Invalid JSON schema for extra field");
  }
  return await prisma.$transaction(async (tx) => {
    const curLibrary = await tx.jlLibrary.upsert({
      where: { name: libraryName },
      create: { name: libraryName },
      update: {}, // 如果已存在，不需要更新内容
    });

    const history = await tx.jlLibraryHistory.create({
      data: {
        version: historyData.version,
        creator: historyData.creator,
        extra: historyData.extra,
        libraryId: curLibrary.id,
      },
    });
    return { curLibrary, history };
  });
}
/**
 * @summary 设置library记录的最新历史指向何处
 * @param prisma prisma client
 * @param latestVersion 版本号
 * @param libraryName library名称
 * @returns Promise<JlLibrary> 返回更新后的library记录，其latestHistoryId字段已更新为指定版本的历史记录ID
 * @example
 * ```typescript
 * const updatedLibrary = await setLatestInLibrary(prisma, "1.0.10", "Test Library");
 * ```
 */
async function setLatestInLibrary(prisma: PrismaClient, latestVersion: string, libraryName: string) {
  return await prisma.$transaction(async (tx) => {
    const library = await tx.jlLibrary.findUnique({
      where: { name: libraryName },
      include: {
        history: {
          where: { version: latestVersion },
        },
      },
    });
    if (!library) {
      throw new Error("Library not found");
    }
    if (library.history.length > 1 || library.history.length === 0) {
      throw new Error("Invalid history state");
    }
    const updatedLibrary = await tx.jlLibrary.update({
      where: { id: library.id },
      data: { latestHistoryId: library.history[0].id },
    });
    return updatedLibrary;
  });
}
/**
 * @summary 创建一个空的library历史记录并设置其为最新版本
 * @param prisma prisma client
 * @param libraryName library名称
 * @param historyData 历史记录的必要成员
 * @returns Promise<{updatedLibrary: JlLibrary, history: JlLibraryHistory}> 返回更新后的library记录（已设置最新历史ID）和新创建的历史记录
 * @example
 * ```
 * const prisma = new PrismaClient();
 * const { updatedLibrary, history } = await createEmptyLibraryHistoryWithLatest(prisma, "Test Library", {
 *   version: "1.0.0",
 *   creator: "admin",
 *   createdAt: new Date(),
 *   extra: `
 *   {
 *     "summary":
 *     {
 *       "zh": "图形",
 *       "en": "Graph"
 *     }
 *   }
 *   `,
 * });
 * ```
 */
async function createEmptyLibraryHistoryWithLatest(
  prisma: PrismaClient,
  libraryName: string,
  historyData: {
    version: string;
    creator: string;
    createdAt: Date;
    extra: string;
  },
) {
  const res = await createEmptyLibraryHistory(prisma, libraryName, historyData);
  const updatedLibrary = await setLatestInLibrary(prisma, historyData.version, libraryName);
  return { updatedLibrary, history: res.history };
}
export { createEmptyLibraryHistory, setLatestInLibrary, createEmptyLibraryHistoryWithLatest };
