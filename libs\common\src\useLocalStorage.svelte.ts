import { onMount } from 'svelte';

// TODO：若有需要考虑加入自定义的序列化/反序列化逻辑

/** 类型安全的LocalStorage钩子，可以响应式地读写某个Key对应的LocalStorage的内容。
 * ⚠️类型T必须可JSON序列化。
 * ⚠️useLocalStorage总是在客户端运行。
 * ⚠️若value是一个对象，你必须将整个新对象写回value字段才能触发set方法的localstorage响应式更新（只有这种情况会调用我们的自定义setter）。
 * ⚠️任何localstorge的值都会被用户修改，如果需要将这些值作为参数传递给后端，必须要有合法性验证。
 * ⚠️localStorage里，**最好不要**存放影响首屏渲染结果的用户设置，如默认模型，黑/白模式，i18n等等。这些设置需要存在cookie中。
 * 原因是服务端渲染阶段实际上没有办法拿到浏览器里的localstorage的数据，localstorage的数据需要待前端水合完成，并且挂载组件后才开始同步到响应式状态中。
 * 水合和挂载存在时延，导致首屏渲染状态与实际用户设置期望状态存在一个突然的切换，影响用户体验。
 * @param key LocalStorage的key值
 * @param initialValue 初始化值
 * @param validator 用于验证初始化时localstorage数据否合法的函数,返回true:数据合法，false:数据非法，若非法说明被修改，此时使用initialValue初始化
 */
export const useLocalStorage = <T>(
  key: string,
  initialValue: T,
  validator: (value: T) => boolean,
) => {
  let value = $state<T>(initialValue);

  // 初始化时，localStorage存在对应key值，就从localStorage中读取数据
  // 不存在对应key值，用initialValue初始化localStorage
  onMount(() => {
    const currentValue = localStorage.getItem(key);
    if (currentValue) {
      try {
        const parsedValue = JSON.parse(currentValue);
        // 使用validator验证数据合法性
        if (validator(parsedValue)) {
          value = parsedValue;
        } else {
          // 若数据非法,使用initialValue初始化localStorage
          save();
        }
      } catch {
        // JSON解析失败，说明数据无法被序列化，使用initialValue初始化localStorage
        save();
      }
    } else {
      // 不存在对应key值，使用initialValue初始化localStorage
      save();
    }
  });

  const save = () => {
    if (value) {
      localStorage.setItem(key, JSON.stringify(value));
    }
  };

  return {
    get value() {
      return value;
    },
    // set value时更新LocalStorage
    set value(v: T) {
      value = v;
      save();
    },
  } as LocalStorageHook<T>;
};

export interface LocalStorageHook<T> {
  value: T;
}
