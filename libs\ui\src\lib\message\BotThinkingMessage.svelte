<script lang="ts">
  import * as Avatar from '$lib/components/ui/avatar/index.js';
  import type { ClassValue } from 'svelte/elements';
  import { cn } from '$lib/utils';
  import Loader from '@lucide/svelte/icons/loader';

  interface Props {
    /** @prop {string} thinkingMessage - ChatBot思考中显示的消息. */
    thinkingMessage: string;
    /** @prop {string} avatarSrc - ChatBot头像src */
    avatarSrc: string;
    /** @prop {ClassValue} class - 自定义样式. */
    class?: ClassValue;
  }
  const { thinkingMessage, avatarSrc, class: className }: Props = $props();
</script>

<!--
  @component
  BotThinkingMessage用于显示bot思考中显示的消息。

  - 用法:
    ``` svelte
      <BotThinkingMessage
        {avatarSrc}
        {thinkingMessage}
      />
    ```
-->
<div class={cn(' flex w-full items-center gap-4', className)}>
  <Avatar.Root>
    <Avatar.Image src={avatarSrc} alt="bot avatar" />
  </Avatar.Root>
  <span class="text-foreground">
    {thinkingMessage}
  </span>
  <Loader class="animate-spin" />
</div>
