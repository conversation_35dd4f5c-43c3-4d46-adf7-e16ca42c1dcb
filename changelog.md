# dev

- (feature) 增加latex数学公式支持
- (feature) 增加对话搜索功能

# v1.1.0

- (feature) 增加主题切换功能，可以通过.env文件配置默认主题和是否开启主题切换器
- (bugfix) 对于乱码内容不生成链接
- (bugfix) 线上华为云obs上传失败
- (bugfix) 修复配置文件ENABLE_THEME_SWITCHER从true切换为false时，主题不生效的bug

# v1.0.0

- (feature) 添加可选的项目配置文件 askme_config.json，可以通过该文件热更新应用标题和LLM模型配置等信息

# v1.0.0-beta1

- (feature) 提供 /api/health 查询服务状态
- (enhancement) 支持 DeepSeek R1 模型
- (enhancement) 升级至性能更好的高级模型
- (feature) 增加可选功能：用户输入内容安全审查
- (enhancement) 用户输入框增加字数上限
- (bugfix) 修复中文输入法触发TextArea意外滚动
- (bugfix) 修复未开启sso时删除cookie导致的异常
- (bugfix) 修改模型选择逻辑
- (bugfix) 删除无用的Access-Control中间件

# v1.0.0-alpha1

- (Feature) 支持思考模型、图文模型
- (Feature) 支持图片附件问答
- (Feature) 支持 OAuth 授权
- (Feature) 支持历史聊天存储
- (Feature) 支持 langfuse 日志接入
- (Feature) 支持 MWORKS 文档搜索并在回答中列出相关文档

<!-- feature, bugfix, enhancement, breaking change -->
