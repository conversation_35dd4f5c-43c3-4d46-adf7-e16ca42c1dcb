import {
  validateSessionToken,
  setSessionTokenCookie,
  deleteSessionTokenCookie,
  hashSessionToken,
  getSessionWithUserBySessionId,
  ssoLogin,
  validateSharedAccessToken,
} from '$lib/baseAuth';
import type { SessionWithUser, SsoLoginResult } from '$lib/baseAuth';
import serverCtx from '$lib/context.server';
import { env } from '$env/dynamic/private';
import * as LogFormat from '@askme/lib-common/logformat';
import type { Handle, ServerInit } from '@sveltejs/kit';
import { sequence } from '@sveltejs/kit/hooks';
import { config } from '$lib/config.server';
import { normalizeUserPreferancesCookies } from '$lib/utils';
import { validateOAuthAccessToken } from '$lib/oauthUtils';

// 服务器启动检查
export const init: ServerInit = async () => {
  // 1. 设置日志级别
  LogFormat.set_log_level(config.log.level as LogFormat.LogLevel);

  // 3. 数据库检查
  try {
    await serverCtx.prisma.$queryRaw`SELECT 1`;
    console.log(LogFormat.info('Database connected.'));
  } catch (error) {
    console.error(LogFormat.fatal(`Database unavailable: ${error}`));
    process.exit(1);
  }

  // 4. OAuth 服务器检查
  try {
    const response = await fetch(config.oauth.baseurl);
    if (!response.ok) {
      console.error(LogFormat.fatal(`OAUTH_BASE_URL not available: ${response.statusText}`));
      process.exit(1);
    }
    console.log(LogFormat.info(`OAUTH_BASE_URL (${config.oauth.baseurl}) is available.`));
  } catch (error) {
    console.error(LogFormat.fatal(`OAUTH_BASE_URL not available: ${error}`));
    process.exit(1);
  }

  // 5. MinIO 存储检查
  if (config.s3.provider === 'MINIO') {
    try {
      const response = await fetch(
        new URL(
          `minio/health/live`,
          `${config.s3.ssl ? 'https' : 'http'}://${config.s3.hostname}:${config.s3.port}`,
        ),
      );
      if (!response.ok) {
        console.error(LogFormat.fatal(`S3_BASE_URL not available: ${response.statusText}`));
        process.exit(1);
      }
      console.log(LogFormat.info(`S3_BASE_URL (${env.S3_BASE_URL}) is available.`));
    } catch (error) {
      console.error(LogFormat.fatal(`S3_BASE_URL not available: ${error}`));
      process.exit(1);
    }
  } else if (config.s3.provider === 'HUAWEI_OBS') {
    try {
      const response = await fetch(`https://${config.s3.hostname}`);
      // 华为obs没有实现健康检查api，因此简单fetch一下看服务有没有在线
      // 403是正常的，因为没有权限访问
      if (!response.ok && response.status === 403) {
        console.log(LogFormat.info(`S3_BASE_URL (${env.S3_BASE_URL}) is available.`));
      } else {
        console.error(
          LogFormat.fatal(
            `S3_BASE_URL not available, please check Huawei OBS config, or the OBS service is offline.`,
          ),
        );
      }
    } catch (error) {
      console.error(LogFormat.fatal(`S3_BASE_URL not available: ${error}`));
      process.exit(1);
    }
  } else {
    // 一般不会执行到这里，config构造时已经检查过了
    throw new Error('S3_PROVIDER not available: ' + config.s3.provider);
  }

  // 6. Langfuse 检查
  try {
    const response = await fetch(new URL('api/public/health', config.langfuse.baseurl));
    if (!response.ok) {
      console.error(LogFormat.fatal(`LANGFUSE_BASEURL not available: ${response.statusText}`));
      process.exit(1);
    }
    console.log(LogFormat.info(`LANGFUSE_BASEURL (${config.langfuse.baseurl}) is available.`));
  } catch (error) {
    console.error(LogFormat.fatal(`LANGFUSE_BASEURL not available: ${error}`));
    process.exit(1);
  }

  // 7. OPENAI API 健康检查
  try {
    const response = await fetch(new URL('/api/health', config.llm.openai_api_baseurl));
    if (!response.ok) {
      console.error(
        LogFormat.fatal(
          `OPENAI_API_BASE_URL not available: ${response.statusText}, if the LLM service is provided by LLM Store, perhaps the LLM Store is offline.`,
        ),
      );
    }
    console.log(
      LogFormat.info(`OPENAI_API_BASE_URL (${config.llm.openai_api_baseurl}) is available.`),
    );
  } catch (error) {
    console.error(
      LogFormat.fatal(
        `OPENAI_API_BASE_URL not available: ${error}, if the LLM service is provided by LLM Store, perhaps the LLM Store is offline.`,
      ),
    );
  }

  // 8. 阿里云内容安全检查
  if (config.content_moderation) {
    if (config.content_moderation.provider === 'ALIYUN') {
      try {
        const response = await fetch(new URL(config.content_moderation.baseurl));
        // 简单fetch一下看服务有没有在线, 404是正常的，因为没有权限访问
        if (!response.ok && response.status === 404) {
          console.log(
            LogFormat.info(
              `ALI_CLOUD_CONTENT_MODERATION_PLUS_BASEURL (${config.content_moderation.baseurl}) is available.`,
            ),
          );
        }
      } catch (error) {
        console.error(
          LogFormat.fatal(`ALI_CLOUD_CONTENT_MODERATION_PLUS_BASEURL not available: ${error}`),
        );
        process.exit(1);
      }
    } else {
      // 一般不会执行到这里，config构造时已经检查过provider
      throw new Error('CONTENT_MODERATION_PROVIDER not support.');
    }
  }
};

// 用户会话认证中间件

// authHandler的主线逻辑是:
// 获取cookie中的sessionToken(用于维护askme登陆会话)和sharedAccessToken(sso场景下用于维护sso主站登陆会话)
// 根据这两个token值的具体情况, 构造出正确的session和user对象, 附加到event.locals, 以供后续请求处理器使用
// session和user对象在authHandler中被表示为sessionWithUser对象, 一开始为空
// 当authHandler执行到结束时:
// 若正确构造出sessionWithUser对象, 表示经过授权的合法请求, 将sessionWithUser附加到event.locals
// sessionWithUser对象置空, 表示未经授权或发生错误的请求, 则将event.locals设置为空

// 步骤:
// 首先获取cookie中的sessionToken和sharedAccessToken,创建空的sessionWithUser
// if cookie中存在sessionToken
//    |- 从数据库中获取session和user
//    |- if 数据库里有session
//      |- if cookie中有sharedAccessToken,此时检查sharedAccessToken有没有更换,若更换校验新token
//      |- else cookie中没有sharedAccessToken
//          |- if SSO场景,主站退出了.那么应该登出askme
//          |- else OAUTH场景, 此时应该正常校验/续期oauth access token
//    |- sessionToken验证/续期
// else cookie中不存在sessionToken
//    |- if cookie中有sharedAccessToken, 自动sso登陆/注册
// 根据填充的sessionWithUser, 设置event.locals
export const authHandler: Handle = async ({ event, resolve }) => {
  // 获取cookie中的会话令牌
  let sessionToken = event.cookies.get('session') ?? null;

  // 获取sso主站cookie共享来的access token
  let sharedAccessToken = null;
  if (config.common.sso_enabled) {
    sharedAccessToken = event.cookies.get(config.common.sso_cookie_name) ?? null;
  }

  let sessionWithUser: SessionWithUser | null = null;

  // 有sessionToken
  if (sessionToken) {
    // 数据库中的会话ID
    const sessionId = hashSessionToken(sessionToken);
    // 从数据库中获取会话和用户信息
    try {
      sessionWithUser = await getSessionWithUserBySessionId(serverCtx.prisma, sessionId);
      if (!sessionWithUser) {
        throw new Error('Session not found');
      }
    } catch (e) {
      console.error(LogFormat.error('Failed to get session with user:' + e));
      sessionWithUser = null;
    }
    // 数据库里没读到session就不继续了
    if (sessionWithUser) {
      // 有sessionToken, 且有sharedAccessToken,此时检查sharedAccessToken有没有更换,并且校验新token
      if (sharedAccessToken) {
        const isShardAccessTokenChanged = sessionWithUser.accessToken !== sharedAccessToken;
        if (isShardAccessTokenChanged) {
          sessionWithUser = (await validateSharedAccessToken(
            serverCtx.prisma,
            sharedAccessToken,
            sessionWithUser,
          )) as SessionWithUser | null;
        }
      } else {
        // 有sessionToken, 没有sharedAccessToken
        // 可能情况:
        // 1. OAUTH场景, 此时应该正常校验sessionToken和oauth access token
        // 2. SSO场景,主站退出了.那么应该登出askme
        if (sessionWithUser.useCookieUserToken) {
          sessionWithUser = null;
        } else {
          try {
            sessionWithUser = (await validateOAuthAccessToken(
              serverCtx.prisma,
              sessionWithUser,
              config.oauth.provider,
            )) as SessionWithUser | null;
          } catch (error) {
            console.error(LogFormat.error('Failed to validate oauth access token:' + error));
          }
        }
      }
      // sessionToken验证/续期
      try {
        sessionWithUser = (await validateSessionToken(
          serverCtx.prisma,
          sessionWithUser,
        )) as SessionWithUser | null;
      } catch (e) {
        console.error(LogFormat.error('Failed to validate session token:' + e));
        sessionWithUser = null;
      }
    }
  } else {
    // 没有sessionToken, 但是有sharedAccessToken
    // 说明用户主站登录了，但是askme没登陆, 因此单点登陆askme
    let ssoLoginResult: SsoLoginResult | null = null;
    if (sharedAccessToken) {
      try {
        ssoLoginResult = await ssoLogin(serverCtx.prisma, sharedAccessToken);
        if (ssoLoginResult) {
          sessionToken = ssoLoginResult.sessionToken;
          sessionWithUser = ssoLoginResult.sessionWithUser;
        } else {
          throw new Error('Failed to SSOLogin');
        }
      } catch (e) {
        console.error(LogFormat.error('Failed to validate shared access token:' + e));
        sessionWithUser = null;
      }
    }
  }

  // 如果最后sessionWithUser不为空，说明认证成功，设置cookie
  if (sessionWithUser && sessionToken) {
    setSessionTokenCookie(event, sessionToken, sessionWithUser.expiresAt);
    const { user, ...session } = sessionWithUser;
    event.locals.session = session;
    event.locals.user = user;
  } else {
    // 认证失败，删除cookie
    deleteSessionTokenCookie(event.cookies);
    event.locals.user = null;
    event.locals.session = null;
  }

  return resolve(event);
};

// 开发者工具请求屏蔽
export const devToolHandler: Handle = async ({ event, resolve }) => {
  if (event.url.pathname.startsWith('/.well-known/appspecific/com.chrome.devtools')) {
    return new Response(null, { status: 204 });
  }
  return resolve(event);
};

// cookie处理中间件, 目前用于验证用户偏好cookie
export const cookieHandler: Handle = async ({ event, resolve }) => {
  const userPreferancesCookiesValue = event.cookies.get('userPreferances') ?? null;

  const finalValue = normalizeUserPreferancesCookies(userPreferancesCookiesValue);

  const EXPIRE_TIME_30DAYS_IN_MS = 1000 * 60 * 60 * 24 * 30;
  event.cookies.set('userPreferances', finalValue, {
    path: '/',
    sameSite: 'lax',
    expires: new Date(Date.now() + EXPIRE_TIME_30DAYS_IN_MS),
    httpOnly: false,
  });
  return resolve(event);
};

// 合并所有 handle 中间件
export const handle = sequence(devToolHandler, authHandler, cookieHandler);
