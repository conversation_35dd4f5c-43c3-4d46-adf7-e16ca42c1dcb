<script module lang="ts">
  //    👆 notice the module context, defineMeta does not work in a regular <script> tag - instance
  import { defineMeta } from '@storybook/addon-svelte-csf';
  import { Toaster } from '$lib/components/ui/sonner/index.js';
  import BotMessage from './BotMessage.svelte';
  import type { JSONValue, TextPart, UIMessage } from 'ai';
  import type { ExternalLinkPart } from '@askme/lib-common';

  //      👇 Get the Story component from the return value
  const { Story } = defineMeta({
    title: 'UI/BotMessage',
    component: BotMessage,
    decorators: [
      /* ... */
    ],
    parameters: {
      /* ... */
    },
  });
  const externalLinks = [
    {
      type: 'externalLink',
      url: 'https://www.mohub.com',
      title: 'MoHub',
      chunkText:
        'MoHub是苏州同元软控公司基于独立自主的系统建模仿真求解内核打造的建模仿真云平台，支持各行业工业知识、经验、数据的模型化、软件化、平台化，实现工业知识的众创、 ...',
    },
    {
      type: 'externalLink',
      url: 'https://www.tongyuan.cc/',
      title: '同元官网',
      chunkText:
        '苏州同元软控信息技术有限公司成立于2008年，是专业从事新一代系统级设计与仿真工业软件产品研发、工程服务及系统工程解决方案的高科技企业。同元软控旨在为各行业提供 ...',
    },
  ] as ExternalLinkPart[];

  const messageParts1 = [
    {
      type: 'text',
      text: '这是一个完整的回复消息示例。我可以帮助回答你的问题，提供代码建议，或者讨论技术话题。',
    },
  ] as TextPart[];

  const messageParts2 = [
    {
      type: 'reasoning',
      reasoning: '这是一个推理消息示例。我可以帮助回答你的问题，提供代码建议，或者讨论技术话题。',
    },
    {
      type: 'text',
      text: 'Bot消息回复',
    },
  ] as TextPart[];
  const avatarSrc =
    'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiIGNsYXNzPSJsdWNpZGUgbHVjaWRlLWJvdCI+PHBhdGggZD0iTTEyIDhWNEg4Ii8+PHJlY3Qgd2lkdGg9IjE2IiBoZWlnaHQ9IjEyIiB4PSI0IiB5PSI4IiByeD0iMiIvPjxwYXRoIGQ9Ik0yIDE0aDIiLz48cGF0aCBkPSJNMjAgMTRoMiIvPjxwYXRoIGQ9Ik0xNSAxM3YyIi8+PHBhdGggZD0iTTkgMTN2MiIvPjwvc3ZnPg==';

  const message1: UIMessage = {
    id: '1',
    parts: messageParts1,
    role: 'assistant',
    content: '',
  };

  const message2: UIMessage = {
    id: '1',
    parts: messageParts2,
    role: 'assistant',
    content: '',
    annotations: externalLinks as unknown as JSONValue[],
  };
</script>

<Toaster />

<Story
  name="Default"
  args={{
    message: message1,
    avatarSrc: avatarSrc,
    showActions: true,
    class: 'mb-10',
    reload: () => console.log('Reload!'),
  }}
/>

<Story
  name="Responding"
  args={{
    message: message1,
    avatarSrc: avatarSrc,
    showActions: false,
    class: 'mb-10',
    reload: () => console.log('Reload!'),
  }}
/>

<Story
  name="Reason"
  args={{
    message: message2,
    avatarSrc: avatarSrc,
    showActions: false,
    class: 'mb-10',
    reload: () => console.log('Reload!'),
  }}
/>
