{"name": "@mdocs/schema", "private": true, "version": "0.0.0", "type": "module", "module": "dist/index.js", "main": "dist/index.cjs", "types": "dist/types/index.d.ts", "files": ["dist"], "scripts": {"build": "vite build", "watch": "vite build --watch", "test": "vitest"}, "dependencies": {"zod": "^4.0.5"}, "devDependencies": {"typescript": "~5.8.3", "vite": "^7.0.4", "vite-plugin-dts": "^4.5.4", "vitest": "^3.2.4"}}