import { encodeBase32LowerCaseNoPadding, encodeHexLowerCase } from '@oslojs/encoding';
import { sha256 } from '@oslojs/crypto/sha2';
import type { RequestEvent, Cookies } from '@sveltejs/kit';
import type { User, Session, Provider } from '$lib/generated/prisma';
import * as LogFormat from '@askme/lib-common/logformat';
import { config } from '$lib/config.server';
import type { UserInfo } from './types';
import { PrismaClient } from '$lib/generated/prisma';
import { getUserInfoByOAuthProvider } from '$lib/oauthUtils';

/**
 * 生成会话令牌（随机字符串编码成base32）
 */
export function generateSessionToken(): string {
  const bytes = new Uint8Array(20);
  crypto.getRandomValues(bytes);
  const token = encodeBase32LowerCaseNoPadding(bytes);
  return token;
}

/**
 * 创建会话，对会话令牌sha256单向哈希，存储到数据库
 */
export async function createSession(
  prisma: PrismaClient,
  token: string,
  access_token: string,
  refresh_token: string | null,
  expires_at: Date | null,
  userId: number,
  useCookieUserToken: boolean,
): Promise<Session> {
  /** cookie过期时间，30天的毫秒数。30天的cookie有效期*/
  const ExpireTimeInMs = 1000 * 60 * 60 * 24 * 30;
  const sessionId = hashSessionToken(token);
  const session: Session = {
    id: sessionId,
    userId,
    expiresAt: new Date(Date.now() + ExpireTimeInMs),
    accessToken: access_token,
    refreshToken: refresh_token,
    oauthExpiresAt: expires_at,
    useCookieUserToken,
  };
  await prisma.session.create({
    data: session,
  });
  return session;
}

/**
 * 验证会话令牌，如有需要返回续期后的session对象.任何错误都返回null
 * @param prisma prisma客户端
 * @param session prisma session对象
 * @returns prisma session对象
 */
export async function validateSessionToken(
  prisma: PrismaClient,
  session: Session | null,
): Promise<Session | null> {
  if (!session) {
    return null;
  }
  /** cookie续期时间，15天的毫秒数。15天内cookie自动续期 */
  const RenewTimeInMs = 1000 * 60 * 60 * 24 * 15;
  /** cookie过期时间，30天的毫秒数。30天的cookie有效期*/
  const ExpireTimeInMs = 1000 * 60 * 60 * 24 * 30;

  // 用户session过期
  if (Date.now() >= session.expiresAt.getTime()) {
    invalidateSession(prisma, session.id);
    console.debug(LogFormat.debug('Session expired, sessionId:' + session.id));
    return null;
  }

  // 用户session续期
  if (Date.now() >= session.expiresAt.getTime() - RenewTimeInMs) {
    try {
      const newExpireTime = new Date(Date.now() + ExpireTimeInMs);
      renewSession(prisma, session.id, newExpireTime);
      session.expiresAt = newExpireTime;
      console.debug(
        LogFormat.debug(
          'Session renewed, sessionId:' + session.id + ', now expiresAt:' + session.expiresAt,
        ),
      );
    } catch (error) {
      // 续期写入数据库失败，log报错，不影响登陆（下次请求再续就是了）
      console.error(
        LogFormat.error('Failed to update session:' + error + '. sessionId:' + session.id),
      );
    }
  }

  return session;
}

/**
 * 使会话失效(删除数据库中的会话)
 */
export async function invalidateSession(prisma: PrismaClient, sessionId: string): Promise<void> {
  try {
    await prisma.session.delete({ where: { id: sessionId } });
    console.debug(LogFormat.debug('Session deleted, sessionId:' + sessionId));
  } catch (error) {
    console.error(
      LogFormat.error('Failed to delete session:' + error + '. sessionId:' + sessionId),
    );
  }
}

/**
 * 使用户的所有会话失效(删除数据库中的所有会话)
 */
export async function invalidateAllSessions(prisma: PrismaClient, userId: number): Promise<void> {
  try {
    await prisma.session.deleteMany({
      where: {
        userId: userId,
      },
    });
    console.debug(LogFormat.debug('Session deleted, userId:' + userId));
  } catch (error) {
    console.error(LogFormat.error('Failed to delete all session:' + error + '.  userId:' + userId));
  }
}

export type SessionWithUser = Session & { user: User };

/**
 * 设置浏览器会话cookie
 */
export function setSessionTokenCookie(event: RequestEvent, token: string, expiresAt: Date): void {
  event.cookies.set('session', token, {
    httpOnly: true,
    sameSite: 'lax',
    expires: expiresAt,
    path: '/',
  });
}

/**
 * 删除浏览器会话cookie
 */
export function deleteSessionTokenCookie(cookies: Cookies): void {
  cookies.delete('session', {
    path: '/',
  });
  if (config.common.sso_enabled) {
    cookies.delete(config.common.sso_cookie_name, {
      path: '/',
    });
  }
}

/**
 * 将cookies中的会话令牌转为sha256单向哈希
 * @param token cookies中的会话令牌
 * @returns 数据库中sha256单向哈希后的会话令牌
 */
export function hashSessionToken(token: string): string {
  return encodeHexLowerCase(sha256(new TextEncoder().encode(token)));
}

/**
 * 在数据库中续期会话
 * @param prisma prisma客户端
 * @param sessionId 会话ID
 * @param expiresAt 会话的新过期时间
 */
async function renewSession(prisma: PrismaClient, sessionId: string, expiresAt: Date) {
  await prisma.session.update({
    where: {
      id: sessionId,
    },
    data: {
      expiresAt: expiresAt,
    },
  });
}

/**
 * 根据会话ID从数据库中获取会话和用户信息
 * @param prisma prisma客户端
 * @param sessionId 会话ID
 * @returns 会话和用户信息
 */
export async function getSessionWithUserBySessionId(
  prisma: PrismaClient,
  sessionId: string,
): Promise<SessionWithUser | null> {
  return await prisma.session.findUnique({
    where: {
      id: sessionId,
    },
    include: {
      user: true,
    },
  });
}

/**
 * 若用户已存在则返回prisma用户对象
 * 若用户不存在则创建用户再返回prisma用户对象
 * @param userInfo 用户信息
 * @param provider OAuth提供者
 * @returns prisma User对象
 */
export async function createUserIfNotExists(
  prisma: PrismaClient,
  userInfo: UserInfo,
  provider: Provider,
): Promise<User> {
  let user = await prisma.user.findFirst({
    where: {
      UID: userInfo.UID,
    },
  });

  // 数据库里没有用户就注册
  if (!user) {
    user = await prisma.user.create({
      data: {
        UID: userInfo.UID,
        username: userInfo.username,
        email: userInfo.email,
        provider,
      },
    });
  }

  return user;
}

/** 验证单点登陆主站cookie中的sharedAccessToken
 * 如果验证成功, 返回更新后的session对象
 * 如果验证失败, 返回null
 * @param prisma prisma客户端
 * @param sharedAccessToken 主站共享的access token
 * @param session askme的session
 * @returns sessionWithUser
 */
export async function validateSharedAccessToken(
  prisma: PrismaClient,
  sharedAccessToken: string,
  session: Session | null,
): Promise<Session | null> {
  if (!session) {
    return null;
  }
  let userInfo;
  // 主站更换了access token,此时校验一下oauth userInfo
  try {
    userInfo = await getUserInfoByOAuthProvider(config.oauth.provider, sharedAccessToken);
    if (!userInfo) {
      throw new Error('UserInfo is null');
    }
  } catch (e) {
    // 验证access token失败, 登出askme的单点登陆
    console.error(
      LogFormat.error('Failed to get user info.' + e + 'Provider:' + config.oauth.provider),
    );
    return null;
  }
  // 验证新sharedAccessToken成功,此时已取得userinfo, 更新数据库中的access token
  if (userInfo) {
    try {
      await prisma.session.update({
        where: {
          id: session.id,
        },
        data: {
          accessToken: sharedAccessToken,
        },
      });
      session.accessToken = sharedAccessToken;
    } catch (e) {
      console.error(LogFormat.error('Failed to update session:' + e));
      return null;
    }
  }
  // 返回更新后的session对象
  return session;
}

/** 单点登陆方法,使用cookie里的sharedAccessToken向OAuth请求用户信息
 * 如果验证成功, 返回更新后的SessionWithUser对象
 * 如果验证失败, 返回null
 * @param prisma prisma客户端
 * @param sharedAccessToken 主站共享的access token
 */
export async function ssoLogin(
  prisma: PrismaClient,
  sharedAccessToken: string,
): Promise<SsoLoginResult | null> {
  let userInfo;
  let user;
  // 用共享的access token向oauth请求用户信息
  try {
    userInfo = await getUserInfoByOAuthProvider(config.oauth.provider, sharedAccessToken);
  } catch (e) {
    console.error(
      LogFormat.error('Failed to get user info.' + e + 'Provider:' + config.oauth.provider),
    );
    return null;
  }
  // 验证access token成功
  if (userInfo) {
    // 有用户信息就获取用户,没用户信息就创建用户
    user = await createUserIfNotExists(prisma, userInfo, config.oauth.provider);
    //创建完用户创建session
    const sessionToken = generateSessionToken();
    const session = await createSession(
      prisma,
      sessionToken,
      sharedAccessToken,
      null,
      null,
      user.id,
      true,
    );
    const sessionWithUser = { ...session, user };
    return { sessionWithUser, sessionToken };
  } else {
    return null;
  }
}

export interface SsoLoginResult {
  sessionWithUser: SessionWithUser;
  sessionToken: string;
}
