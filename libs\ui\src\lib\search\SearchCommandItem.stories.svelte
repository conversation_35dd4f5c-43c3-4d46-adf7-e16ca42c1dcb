<script module lang="ts">
  //    👆 notice the module context, define<PERSON><PERSON> does not work in a regular <script> tag - instance
  import { defineMeta } from '@storybook/addon-svelte-csf';
  import type { ChatSearchResult } from '@askme/lib-common';

  import SearchCommandItem from './SearchCommandItem.svelte';

  //      👇 Get the Story component from the return value
  const { Story } = defineMeta({
    title: 'UI/SearchCommandItem',
    component: SearchCommandItem,
    decorators: [
      /* ... */
    ],
    parameters: {
      /* ... */
    },
  });
  const searchResult = {
    title: 'title',
    detail: 'detail',
    dateStr: 'dateStr',
    chatId: 'chatId',
  } as ChatSearchResult;

  const searchResultLongContent = {
    title:
      'titletitletitletitletitletitletitletitletitletitletitletitletitletitletitletitletitletitletitletitletitletitletitletitletitletitletitletitletitletitletitletitletitletitletitle',
    detail:
      'detaildetaildetaildetaildetaildetaildetaildetaildetaildetaildetaildetaildetaildetaildetaildetaildetaildetaildetaildetaildetaildetaildetaildetaildetaildetaildetaildetaildetaildetaildetail',
    dateStr: 'dateStr',
    chatId: 'chatId',
  } as ChatSearchResult;
</script>

<Story
  name="Default"
  args={{
    searchResult,
  }}
/>

<Story
  name="Long Title"
  args={{
    searchResult: searchResultLongContent,
  }}
/>
