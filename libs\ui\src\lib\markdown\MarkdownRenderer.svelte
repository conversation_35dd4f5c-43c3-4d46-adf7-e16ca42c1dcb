<script lang="ts">
  import type {
    HTMLAttributes,
    HTMLOlAttributes,
    HTMLLiAttributes,
    HTMLAnchorAttributes,
  } from 'svelte/elements';
  import { cn } from '$lib/utils';
  import { gfmPlugin } from 'svelte-exmarkdown/gfm';
  import type { Plugin } from 'svelte-exmarkdown';
  import Markdown from 'svelte-exmarkdown';
  import 'highlight.js/styles/github.css';
  import modelica from './modelica';
  import julia from 'highlight.js/lib/languages/julia';
  import juliaRepl from 'highlight.js/lib/languages/julia-repl';
  import python from 'highlight.js/lib/languages/python';
  import matlab from 'highlight.js/lib/languages/matlab';
  import bash from 'highlight.js/lib/languages/bash';
  import shell from 'highlight.js/lib/languages/shell';
  import rehypeHighlight from 'rehype-highlight';
  import CodeBar from './CodeBar.svelte';
  import type { CodeSnippet } from '@askme/lib-common';
  import 'katex/dist/katex.min.css';
  import rehypeKatex from 'rehype-katex';
  import remarkMath from 'remark-math';

  interface Props {
    /** @prop {string} markdown - markdown字符串. */
    markdown: string;
  }
  let { markdown = '' }: Props = $props();

  let codeBlocks: CodeSnippet[] = $derived.by(() => {
    const codeBlockRegex = /```\s*([\w#+-]+)?\s*\n([\s\S]*?)(?:\n?\s*```|$)/g;
    const matches: CodeSnippet[] = [];
    let match;

    while ((match = codeBlockRegex.exec(markdown)) !== null) {
      matches.push({
        language: match[1] || 'text', // 可能没有指定语言
        code: match[2].trim(),
      } as CodeSnippet);
    }

    return matches;
  });

  // svelte-ignore non_reactive_update
  let currentPreIndex = 0;

  const plugins: Plugin[] = [
    {
      remarkPlugin: [remarkMath],
      rehypePlugin: [
        rehypeKatex,
        rehypeHighlight,
        {
          ignoreMissing: true,
          languages: { julia, juliaRepl, modelica, python, matlab, bash, shell },
        },
      ],
    },
    gfmPlugin(),
  ];
</script>

<Markdown md={markdown} {plugins}>
  <!-- ⚠️svelte 5.30后snippet必须放到父标签里，不然会报类型错误，原因未知 -->
  {#snippet h1(props: HTMLAttributes<HTMLHeadingElement>)}
    {@const { children, class: className, ...rest } = props}
    <h1 class={cn('mt-6 mb-2 text-3xl font-semibold', className)} {...rest}>
      {@render children?.()}
    </h1>
  {/snippet}
  {#snippet h2(props: HTMLAttributes<HTMLHeadingElement>)}
    {@const { children, class: className, ...rest } = props}
    <h2 class={cn('mt-6 mb-2 text-2xl font-semibold', className)} {...rest}>
      {@render children?.()}
    </h2>
  {/snippet}

  {#snippet h3(props: HTMLAttributes<HTMLHeadingElement>)}
    {@const { children, class: className, ...rest } = props}
    <h3 class={cn('mt-6 mb-2 text-xl font-semibold', className)} {...rest}>
      {@render children?.()}
    </h3>
  {/snippet}

  {#snippet h4(props: HTMLAttributes<HTMLHeadingElement>)}
    {@const { children, class: className, ...rest } = props}
    <h4 class={cn('mt-6 mb-2 text-lg font-semibold', className)} {...rest}>
      {@render children?.()}
    </h4>
  {/snippet}

  {#snippet h5(props: HTMLAttributes<HTMLHeadingElement>)}
    {@const { children, class: className, ...rest } = props}
    <h5 class={cn('mt-6 mb-2 text-base font-semibold', className)} {...rest}>
      {@render children?.()}
    </h5>
  {/snippet}

  {#snippet h6(props: HTMLAttributes<HTMLHeadingElement>)}
    {@const { children, class: className, ...rest } = props}
    <h6 class={cn('mt-6 mb-2 text-sm font-semibold', className)} {...rest}>
      {@render children?.()}
    </h6>
  {/snippet}

  {#snippet ol(props: HTMLOlAttributes)}
    {@const { children, class: className, ...rest } = props}
    <ol {...rest} class={cn('ml-4 list-outside list-decimal', className)}>
      {@render children?.()}
    </ol>
  {/snippet}

  {#snippet ul(props: HTMLAttributes<HTMLUListElement>)}
    {@const { children, class: className, ...rest } = props}
    <ul {...rest} class={cn('ml-4 list-outside list-disc', className)}>
      {@render children?.()}
    </ul>
  {/snippet}

  {#snippet li(props: HTMLLiAttributes)}
    {@const { children, class: className, ...rest } = props}
    <li {...rest} class={cn(className)}>
      {@render children?.()}
    </li>
  {/snippet}

  {#snippet strong(props: HTMLAttributes<HTMLElement>)}
    {@const { children, class: className, ...rest } = props}
    <span {...rest} class={cn('font-semibold', className)}>
      {@render children?.()}
    </span>
  {/snippet}

  {#snippet a(props: HTMLAnchorAttributes)}
    {@const { children, class: className, ...rest } = props}
    <a
      {...rest}
      class={cn('text-blue-500 hover:underline', className)}
      target="_blank"
      rel="noopener noreferrer"
    >
      {@render children?.()}
    </a>
  {/snippet}

  {#snippet code(props: HTMLAttributes<HTMLElement>)}
    {@const { children, class: className, ...rest } = props}
    <code
      {...rest}
      class={cn(
        // 如果<code>在<pre>中，则不应用背景色
        'rounded-md py-0.5 text-sm wrap-break-word [&:not(pre_&)]:bg-zinc-100 [&:not(pre_&)]:dark:bg-zinc-800',
        className,
      )}>{@render children?.()}</code
    >
  {/snippet}

  {#snippet pre(props: HTMLAttributes<HTMLPreElement>)}
    {@const { children, class: className, ...rest } = props}
    {@const index = currentPreIndex++}
    <div class="my-4 overflow-hidden rounded-xl border border-zinc-200 dark:border-zinc-700">
      <div
        class="flex items-center justify-between border-b border-zinc-200 bg-zinc-100 px-4 py-2 dark:border-zinc-700 dark:bg-zinc-800"
      >
        {#if codeBlocks[index]}
          <CodeBar codeSnippet={codeBlocks[index]} />
        {/if}
      </div>
      <!-- Code content -->
      <pre
        {...rest}
        class={cn(
          'block overflow-x-auto p-4 text-sm whitespace-pre text-zinc-900 dark:bg-zinc-900 dark:text-zinc-50',
          className,
        )}>{@render children?.()}</pre>
    </div>
  {/snippet}

  {#snippet table(props: HTMLAttributes<HTMLTableElement>)}
    {@const { children, class: className, ...rest } = props}
    <div class="my-4 overflow-x-auto">
      <table {...rest} class={cn('w-full border-collapse text-sm', className)}>
        {@render children?.()}
      </table>
    </div>
  {/snippet}

  {#snippet thead(props: HTMLAttributes<HTMLTableSectionElement>)}
    {@const { children, class: className, ...rest } = props}
    <thead
      {...rest}
      class={cn(
        'border-b border-zinc-200 bg-zinc-50 dark:border-zinc-700 dark:bg-zinc-800',
        className,
      )}>{@render children?.()}</thead
    >
  {/snippet}

  {#snippet tbody(props: HTMLAttributes<HTMLTableSectionElement>)}
    {@const { children, class: className, ...rest } = props}
    <tbody {...rest} class={cn('divide-y divide-zinc-200 dark:divide-zinc-700', className)}
      >{@render children?.()}</tbody
    >
  {/snippet}

  {#snippet tr(props: HTMLAttributes<HTMLTableRowElement>)}
    {@const { children, class: className, ...rest } = props}
    <tr
      {...rest}
      class={cn('transition-colors hover:bg-zinc-100 dark:hover:bg-zinc-800/50', className)}
      >{@render children?.()}</tr
    >
  {/snippet}

  {#snippet th(props: HTMLAttributes<HTMLTableCellElement>)}
    {@const { children, class: className, ...rest } = props}
    <th
      {...rest}
      class={cn('px-4 py-3 text-left font-medium text-zinc-900 dark:text-zinc-50', className)}
      >{@render children?.()}</th
    >
  {/snippet}

  {#snippet td(props: HTMLAttributes<HTMLTableCellElement>)}
    {@const { children, class: className, ...rest } = props}
    <td {...rest} class={cn('px-4 py-3 text-zinc-700 dark:text-zinc-300', className)}
      >{@render children?.()}</td
    >
  {/snippet}
</Markdown>
