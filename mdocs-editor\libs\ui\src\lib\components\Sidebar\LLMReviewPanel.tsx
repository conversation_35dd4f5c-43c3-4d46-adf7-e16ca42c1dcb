import { ScrollArea } from "../ui/scroll-area";
import { Brain } from "lucide-react";

/**
 * @interface LLMReviewPanelProps
 * @description 用于大模型评审面板的属性类型定义。
 * @property score 评审分数
 * @property comment 评审意见
 */
export interface LLMReviewPanelProps {
  score?: number;
  comment: string;
}

/**
 * 大模型评审面板组件
 * @param props LLMReviewPanelProps
 */
export function LLMReviewPanel({
  score,
  comment,
}: LLMReviewPanelProps) {
  return (
    <ScrollArea className="h-full">
      <div className="p-4 space-y-4">
        {(score === undefined && (!comment || comment.trim() === "")) ? (
          <div className="text-center text-gray-500 py-12">
            <Brain className="w-12 h-12 mx-auto mb-3 text-gray-300" />
            <span className="text-sm">暂无评审内容</span>
            <br></br>
            <span className="text-xs text-gray-400 mt-1">等待大模型分析...</span>
          </div>
        ) : (
          <>
            {score !== undefined && (
              <div className={`p-3 rounded-md border ${score >= 90 ? "text-green-500 bg-green-50" : score >= 70 ? "text-yellow-500 bg-yellow-50" : "text-red-500 bg-red-50"}`}>
                <div className="flex items-center gap-1 mb-1">
                  <span className="font-medium text-sm">评审分数</span>
                </div>
                <div className="text-xl font-semibold">{score}</div>
              </div>
            )}
            {comment && comment.trim() !== "" && (
              <div className="space-y-2">
                <h4 className="font-medium text-gray-700 flex items-center gap-2">
                  评审意见
                </h4>
                <div className="bg-gray-50 p-3 rounded-lg border-l-4 border-blue-400">
                  <span className="text-sm text-gray-700 leading-relaxed whitespace-pre-wrap break-words">
                    {comment}
                  </span>
                </div>
              </div>
            )}
          </>
        )}
      </div>
    </ScrollArea>
  );
}
