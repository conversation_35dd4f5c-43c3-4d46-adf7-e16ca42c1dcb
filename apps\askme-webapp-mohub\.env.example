# 若未说明，注释掉的为可选字段，且内容为默认值

# ########## Common Server Config ###########

# # 服务监听地址 (svelte)
# HOST="127.0.0.1"

# # 服务监听端口 (svelte)
# PORT="5000"

# # 部署环境: 公网/内网
# # 当前行为差异：
# # 1. 公网使用华为云，内网使用minio
# # 2. 公网环境下，不给出内部知识的溯源链接
# # Options: 'PUBLIC' | 'PRIVATE' |
# DEPLOYMENT_TARGET=PUBLIC

# # SSO cookie名称
# # 默认为空, 为空时禁用SSO.
# # 设置为具体字符串时会从对应cookie name取值 
# # 示例: '' | 'askme_token'
# SSO_COOKIE_NAME=

# # Log Level
# # Options: 'trace' | 'debug' | 'info' | 'warn' | 'error' | 'fatal'
# LOG_LEVEL=info

# ########## LLM API ################
# 注：LLM 服务与 askme 服务需要指向同一个 OAuth 提供商

# LLM API endpoint
LLM_STORE_API_BASE_URL=https://copilot.tongyuan.cc/api/

# # OpenAI API endpoint
# # 若未提供，则为 LLM_STORE_API_BASE_URL
# OPENAI_API_BASE_URL=<LLM_STORE_API_BASE_URL>

# ########## OAuth ##################

# OAuth 供应商，目前支持 GITLAB、TONGYUAN、KEYCLOAK 三种
OAUTH_PROVIDER=GITLAB

# OAuth provider base url
OAUTH_BASE_URL=https://login.tongyuan.cc

# OAuth Application secret
OAUTH_APPLICATION_SECRET=xxx

# OAuth Application ID
OAUTH_APPLICATION_ID=xxx

# # OAuth authorize request scopes
# OAUTH_SCOPES="openid email"

# # OAuth provider userinfo endpoint URL
# # 若未提供，将基于 provider 和 baseurl 来自动推断
# OAUTH_USERINFO_URL=https://login.tongyuan.cc/authentication/userinfo

# # OAuth provider authorize endpoint URL
# # 若未提供，将基于 provider 和 baseurl 来自动推断
# OAUTH_AUTHORIZATION_URL=https://login.tongyuan.cc/authentication/authorize

# # OAuth provider token endpoint URL
# # 若未提供，将基于 provider 和 baseurl 来自动推断
# OAUTH_TOKEN_URL=https://login.tongyuan.cc/authentication/oauth2/token

# # OAuth Reduirect URI
OAUTH_REDIRECT_URI=https://askme.mohub.net/login/gitlab/callback

# ########## S3 Object storage ##################

# S3 backend provider
# options: "MINIO" | "HUAWEI_OBS"
S3_PROVIDER="MINIO"

# S3 provider base url
S3_BASE_URL="https://oss-dev.tongyuan.cc"

# S3 bucket name
S3_BUCKET_NAME="askme"

# S3 bucket path prefix
# 若为空，则实际存储路径为 <bucket>/
# 若非空，则实际存储路径为 <bucket>/<prefix>/
S3_BUCKET_PREFIX="xxx"

# S3 Secret key
S3_SECRET_KEY="xxx"

# S3 Access Key
S3_ACCESS_KEY=xxx

# ########### Database ##################

# database provider
# options: "postgres"
# DB_PROVIDER="postgres"

# database connection url
# <AUTHOR> <EMAIL>:<port>
# example: postgres://postgres:postgres@localhost:5432
DB_URL=postgres://postgres@localhost:5432

# ############# Langfuse LLM log service ##########

# langfuse baseurl
LANGFUSE_BASEURL=http://localhost:3000

# langfuse access token
LANGFUSE_PUBLIC_KEY=xxx

# langfuse secret token
LANGFUSE_SECRET_KEY=xxx

# ############# Hot reload config file paths ##########
# 热更新配置文件路径

# 示例问题配置文件路径
# DEMO_QUESTIONS_PATHS=./demo_questions.json

# AskMe配置文件路径
# ASKME_CONFIG_PATHS=./askme_config.json

# ############### 内容安全审查 ###############

# 内容安全审查提供商,不填写默认关闭
# options: "ALIYUN"
# CONTENT_MODERATION_PROVIDER=

# 内容安全审查baseurl
# 示例：https://green-cip.cn-shanghai.aliyuncs.com
# CONTENT_MODERATION_BASE_URL=

# 内容安全审查AccessKeyID
# CONTENT_MODERATION_ACCESS_KEY_ID=xxx

# 内容安全审查AccessKeySecret
# CONTENT_MODERATION_ACCESS_KEY_SECRET=xxx

# ############## 外观主题控制 ###############

# 是否打开主题切换器
# ENABLE_THEME_SWITCHER=false

# 默认主题 (橙色主题适配MoHub)
# options: "ORANGE" | "DARK" | "LIGHT"
# DEFAULT_THEME=ORANGE