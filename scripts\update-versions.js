#!/usr/bin/env node

const fs = require("fs");
const path = require("path");
const { execSync } = require("child_process");

// Read the version from VERSION file
function readVersion() {
  try {
    const versionContent = fs.readFileSync("VERSION", "utf8").trim();
    return versionContent;
  } catch (error) {
    console.error("Error reading VERSION file:", error.message);
    process.exit(1);
  }
}

// Find all package.json files in the workspace
function findPackageJsonFiles() {
  const workspaceDir = process.cwd();
  const packageJsonFiles = [];

  // Read pnpm-workspace.yaml to get package paths
  try {
    const workspaceYaml = fs.readFileSync("pnpm-workspace.yaml", "utf8");
    const packageGlobs = workspaceYaml
      .split("\n")
      .filter((line) => line.trim().startsWith("-"))
      .map((line) => line.trim().replace(/^- ["']?(.+?)["']?$/, "$1"));

    // For each glob pattern, find directories
    for (const glob of packageGlobs) {
      // Simple glob expansion for patterns like "apps/*" or "libs/*"
      if (glob.endsWith("/*")) {
        const baseDir = glob.slice(0, -2);
        if (fs.existsSync(baseDir)) {
          const subDirs = fs
            .readdirSync(baseDir, { withFileTypes: true })
            .filter((dirent) => dirent.isDirectory())
            .map((dirent) => path.join(baseDir, dirent.name));

          for (const dir of subDirs) {
            const packageJsonPath = path.join(dir, "package.json");
            if (fs.existsSync(packageJsonPath)) {
              packageJsonFiles.push(packageJsonPath);
            }
          }
        }
      }
    }

    // Also add the root package.json if it exists
    const rootPackageJson = path.join(workspaceDir, "package.json");
    if (fs.existsSync(rootPackageJson)) {
      packageJsonFiles.push(rootPackageJson);
    }
  } catch (error) {
    console.error("Error finding package.json files:", error.message);
    process.exit(1);
  }

  return packageJsonFiles;
}

// Update version in package.json file
function updatePackageVersion(packageJsonPath, version) {
  try {
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, "utf8"));

    if (packageJson.version !== undefined) {
      packageJson.version = version;
      fs.writeFileSync(
        packageJsonPath,
        JSON.stringify(packageJson, null, 2) + "\n"
      );
      return true;
    }
    return false;
  } catch (error) {
    console.error(`Error updating ${packageJsonPath}:`, error.message);
    return false;
  }
}

// Main function
function main() {
  const isCheckMode = process.argv.includes("--check");

  const version = readVersion();
  console.log(`Read version: ${version}`);

  const packageJsonFiles = findPackageJsonFiles();

  if (isCheckMode) {
    checkVersions(packageJsonFiles, version);
  } else {
    updateVersions(packageJsonFiles, version);
  }
}

function checkVersions(packageJsonFiles, version) {
  console.log("\nChecking package versions...");
  const mismatches = [];

  for (const packageJsonPath of packageJsonFiles) {
    try {
      const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, "utf8"));
      if (packageJson.version && packageJson.version !== version) {
        mismatches.push({
          path: packageJsonPath,
          expected: version,
          actual: packageJson.version,
        });
      }
    } catch (error) {
      console.error(`Error reading ${packageJsonPath}: ${error.message}`);
      // Treat read error as a mismatch
      mismatches.push({ path: packageJsonPath, error: "Read error" });
    }
  }

  if (mismatches.length > 0) {
    console.error("\nVersion check failed. Mismatches found:");
    mismatches.forEach((m) => {
      if (m.error) {
        console.error(` - ${m.path}: Could not be read.`);
      } else {
        console.error(
          ` - ${m.path}: Expected version ${m.expected}, but found ${m.actual}`
        );
      }
    });
    console.error("\nPlease run 'pnpm update-versions' to fix the versions.");
    process.exit(1);
  } else {
    console.log("\nAll package.json versions are up to date.");
  }
}

function updateVersions(packageJsonFiles, version) {
  console.log(`\nFound ${packageJsonFiles.length} package.json files:`);
  packageJsonFiles.forEach((file) => console.log(` - ${file}`));
  console.log();

  let updatedCount = 0;
  for (const packageJsonPath of packageJsonFiles) {
    const wasUpdated = updatePackageVersion(packageJsonPath, version);
    if (wasUpdated) {
      console.log(`Updated version in ${packageJsonPath}`);
      updatedCount++;
    } else {
      console.log(`No version field found in ${packageJsonPath}, skipping.`);
    }
  }

  console.log(
    `\nTotal: ${updatedCount} package.json files updated with version ${version}`
  );
}

main();
