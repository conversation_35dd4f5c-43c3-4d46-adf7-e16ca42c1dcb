import { useState, useMemo } from 'react';
import { cn } from '../../utils';
import { FunctionListHeader } from './FunctionListHeader';
import { FunctionListToolbar } from './FunctionListToolbar';
import { FunctionListSearchbar } from './FunctionListSearchbar';
import { FunctionListContent, type FunctionItem } from './FunctionListContent';


interface FunctionListProps {
  /** 函数列表 */
  functions: FunctionItem[];
  /** 函数库名称 */
  libraryName: string;
  /** 当前选中的函数名 */
  selectedFuncStr?: string | null;
  /** 选择函数时的回调 */
  onSelectFunction?: (functionName: string) => void;
  /** 导入函数回调 */
  onImport?: (file: File) => void;
  /** 新建函数回调 */
  onCreate?: () => void;
  /** 导出函数库回调 */
  onExport?: (functionName: string) => void;
  /** 删除函数回调 */
  onDelete?: (functionName: string) => void;
  /** 自定义 CSS 类*/
  className?: string;
}

/**
 * FunctionList 主组件
 * 
 * 功能：
 * - 展示函数列表
 * - 支持搜索、选择、创建、导入、导出和删除功能
 * 
 * 包含四个子组件：Header、Toolbar、Searchbar、Content。
 */
export function FunctionList({
  functions,
  libraryName,
  selectedFuncStr,
  onSelectFunction,
  onImport,
  onCreate,
  onExport,
  onDelete,
  className
}: FunctionListProps) {
  // 搜索状态
  const [searchTerm, setSearchTerm] = useState('');

  // 过滤后的函数列表
  const filteredFunctions = useMemo(() => {
    if (!searchTerm.trim()) {
      return functions;
    }

    const term = searchTerm.toLowerCase();
    return functions.filter(func => 
      func.name.toLowerCase().includes(term) ||
      (func.summary && func.summary.toLowerCase().includes(term))
    );
  }, [functions, searchTerm]);

  return (
    <div className={cn(
      "h-screen flex flex-col bg-white border-r border-gray-200 overflow-hidden w-80",
      className
    )}>
        {/* 头部：库名称和统计 */}
        <FunctionListHeader
          libraryName={libraryName}
          functionCount={functions.length}
        />

        {/* 搜索栏和工具栏在一行 */}
        <div className="px-4 py-3 bg-white border-b border-gray-100">
          <div className="flex items-center gap-3">
            {/* 左侧：搜索栏 */}
            <FunctionListSearchbar
              searchTerm={searchTerm}
              onSearchChange={setSearchTerm}
              placeholder="搜索函数..."
            />

            {/* 右侧：工具栏按钮 */}
            <div className="flex items-center gap-1">
              <FunctionListToolbar
                onCreate={onCreate}
                onImport={onImport}
              />
            </div>
          </div>

          {/* 搜索结果提示 */}
          {searchTerm && (
            <div className="mt-2 text-xs text-gray-500">
              搜索 "{searchTerm}"
            </div>
          )}
        </div>

      {/* 内容区域：函数列表 */}
      <FunctionListContent
        functions={filteredFunctions}
        selectedFuncStr={selectedFuncStr}
        onSelectFunction={onSelectFunction}
        onDelete={onDelete}
        onExport={onExport}
        showEmptyState={true}
        emptyStateText={
          searchTerm
            ? `未找到包含 "${searchTerm}" 的函数`
            : "暂无函数，点击上方 + 按钮创建新函数"
        }
      />
    </div>
  );
}
