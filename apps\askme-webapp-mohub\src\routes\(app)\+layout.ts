import { error } from '@sveltejs/kit';
import * as LogFormat from '@askme/lib-common/logformat';
import type { LayoutLoad } from './$types';

// TODO: 支持Chat的分段加载
// 首次渲染（服务器SSR阶段），只加载最近一定数量的Chat
// 之后更早的Chat在用户滚动到侧边栏底部时加载（前端调用API获取更早的chat）
export const load: LayoutLoad = async ({ data, fetch }) => {
  try {
    const response = await fetch('/api/chat');
    const jsonData: { chats: { id: string; createdAt: string; title: string }[] } =
      await response.json();
    return {
      chats: jsonData.chats,
      ...data,
    };
  } catch (e) {
    console.error(LogFormat.error('Failed to load chats from server: ' + e));
    error(500, 'Internal Server Error');
  }
};
