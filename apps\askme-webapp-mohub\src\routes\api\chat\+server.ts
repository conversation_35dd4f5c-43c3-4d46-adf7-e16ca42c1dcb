import { json, error } from '@sveltejs/kit';
import type { ChatModel } from '@askme/lib-common';
import type { RequestHandler } from './$types';
import serverCtx from '$lib/context.server';
import * as LogFormat from '@askme/lib-common/logformat';
import type { Chat } from '$lib/generated/prisma';

// 取得chat列表
export const GET = (async ({ locals }) => {
  if (!locals.user) {
    console.debug(LogFormat.debug('Unauthorized in /api/chat GET'));
    error(401, 'Unauthorized');
  }

  let chats: Chat[] = [];
  try {
    chats = await serverCtx.prisma.chat.findMany({
      where: {
        creatorId: locals.user.id,
        deletedAt: null,
      },
      orderBy: {
        createdAt: 'desc', // 新聊天放前面
      },
    });
  } catch (e) {
    console.error(LogFormat.error('Failed to find chats: ' + e));
    error(500, 'Internal Server Error');
  }

  return json(
    {
      chats: chats.map((chat) => ({
        id: chat.id,
        createdAt: chat.createdAt,
        title: chat.title,
      })) as ChatModel[],
    },
    { status: 200 },
  );
}) satisfies RequestHandler;

// 删除指定chat
export const DELETE = (async ({ locals, request }) => {
  if (!locals.user) {
    console.debug(LogFormat.debug('Unauthorized in /api/chat DELETE'));
    error(401, 'Unauthorized');
  }

  const { id }: { id: string } = await request.json();

  try {
    await serverCtx.prisma.chat.update({
      where: {
        id,
      },
      data: {
        deletedAt: new Date(),
      },
    });
  } catch (e) {
    console.error(LogFormat.error('Failed to delete chat: ' + e));
    error(500, 'Internal Server Error');
  }
  return new Response('Chat deleted', { status: 200 });
}) satisfies RequestHandler;

// 更新chat(目前只有标题)
export const PATCH = (async ({ locals, request }) => {
  if (!locals.user) {
    console.debug(LogFormat.debug('Unauthorized in /api/chat PATCH'));
    error(401, 'Unauthorized');
  }

  const { id, title }: { id: string; title: string } = await request.json();

  try {
    await serverCtx.prisma.chat.update({
      where: {
        id,
      },
      data: {
        title,
      },
    });
  } catch (e) {
    console.error(LogFormat.error('Failed to update chat: ' + e));
    error(500, 'Internal Server Error');
  }
  return new Response('Chat updated', { status: 200 });
}) satisfies RequestHandler;
