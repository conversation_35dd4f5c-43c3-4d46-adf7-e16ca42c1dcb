import { redirect } from '@sveltejs/kit';
import serverCtx from '$lib/context.server';
import { isUUID } from '$lib/serverUtils';
import type { PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ params, locals }) => {
  if (!isUUID(params.chatID)) {
    return redirect(302, '/chat');
  }
  //从数据库中查找chatID对应的chat
  const chat = await serverCtx.prisma.chat.findUnique({
    where: {
      id: params.chatID,
      deletedAt: null,
    },
    include: {
      messages: {
        orderBy: [
          {
            createdAt: 'asc',
          },
        ],
        include: {
          vote: true,
        },
      },
    },
  });

  if (!chat || chat?.creatorId != locals.user.id) {
    return redirect(302, '/chat');
  }

  return {
    chatId: chat.id,
    messages: chat.messages,
  };
};
