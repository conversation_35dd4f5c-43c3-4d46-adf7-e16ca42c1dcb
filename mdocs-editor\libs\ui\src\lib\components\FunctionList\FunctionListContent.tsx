import { MoreHorizontal, Download, Trash2, User, Clock } from 'lucide-react';
import { Button } from '../ui/button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '../ui/dropdown-menu';



/**
 * 函数项数据结构
 */
export interface FunctionItem {
  /** 函数名称 */
  name: string;
  /** 函数摘要 */
  summary?: string;
  /** 最后更新人员 */
  lastUpdatedBy?: string;
  /** 最后更新时间 */
  lastUpdatedAt?: Date;
  /** 函数评分 */
  score?: number;
  /** 是否已弃用 */
  deprecated?: boolean;
}

interface FunctionListContentProps {
  /** 函数列表 */
  functions: FunctionItem[];
  /** 当前选中的函数名 */
  selectedFuncStr?: string | null;
  /** 选择函数时的回调 */
  onSelectFunction?: (name: string) => void;
  /** 删除函数回调 */
  onDelete?: (functionName: string) => void;
  /** 导出回调 */
  onExport?: (functionName: string) => void;
  /** 是否显示空状态 */
  showEmptyState?: boolean;
  /** 空状态文本 */
  emptyStateText?: string;
}

/**
 * 格式化时间显示
 */
function formatTimeAgo(date: Date): string {
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

  if (diffInSeconds < 60) return '刚刚';
  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} 分钟前`;
  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} 小时前`;
  if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)} 天前`;

  return date.toLocaleDateString('zh-CN');
}



/**
 * 单个函数项组件
 */
function FunctionListItem({
  item,
  isSelected,
  onSelect,
  onDelete,
  onExport
}: {
  item: FunctionItem;
  isSelected: boolean;
  onSelect: () => void;
  onDelete?: () => void;
  onExport?: () => void;
}) {
  return (
    <div
      className={`
        group relative mx-2 my-2 px-3 py-3 rounded-lg cursor-pointer transition-all duration-200 bg-white border
        ${isSelected
          ? 'border-blue-500 bg-blue-50 shadow-md ring-1 ring-blue-200'
          : 'border-gray-200 hover:border-gray-300 hover:shadow-sm'
        }
        ${item.deprecated ? 'opacity-75' : ''}
      `}
      onClick={onSelect}
    >
      <div className="flex items-start justify-between">
        {/* 左侧：函数名称和摘要 */}
        <div className="flex-1 min-w-0 pr-2">
          <div className="flex items-center gap-2 mb-1">
            <h3
              title={item.name}
              className={`
                font-medium text-sm truncate
                ${isSelected ? 'text-blue-900' : 'text-gray-900'}
                ${item.deprecated ? 'line-through text-gray-500' : ''}
              `}
            >
              {item.name}
            </h3>

            {/* 弃用标签 */}
            {item.deprecated && (
              <span className="text-xs text-orange-600 bg-orange-50 border border-orange-200 px-1.5 py-0.5 rounded flex-shrink-0">
                弃用
              </span>
            )}

            {/* 评分显示 */}
            {item.score !== undefined && (
              <span className="text-xs text-gray-500 bg-gray-100 px-1.5 py-0.5 rounded flex-shrink-0">
                {item.score.toFixed(1)}
              </span>
            )}
          </div>

          {/* 摘要 */}
          {item.summary && (
            <div
              title={item.summary}
              className="text-xs text-gray-600 line-clamp-2 leading-relaxed"
            >
              {item.summary}
            </div>
          )}
        </div>

        {/* 右侧：操作菜单 */}
        <div className="flex items-start flex-shrink-0">
          {/* 操作菜单 */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0 opacity-20 group-hover:opacity-100 hover:opacity-100 transition-opacity"
                onClick={(e) => e.stopPropagation()}
              >
                <MoreHorizontal className="h-3 w-3" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-32">
              {onExport && (
                <DropdownMenuItem onClick={(e) => {
                  e.stopPropagation();
                  onExport();
                }}>
                  <Download className="h-3 w-3 mr-2" />
                  导出
                </DropdownMenuItem>
              )}
              {onDelete && (
                <DropdownMenuItem
                  onClick={(e) => {
                    e.stopPropagation();
                    onDelete();
                  }}
                  className="text-red-600 focus:text-red-600"
                >
                  <Trash2 className="h-3 w-3 mr-2" />
                  删除
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* 底部：更新信息（紧凑显示） */}
      {(item.lastUpdatedBy || item.lastUpdatedAt) && (
        <div className="flex items-center justify-between text-xs text-gray-400 mt-1">
          {item.lastUpdatedBy && (
            <div className="flex items-center gap-1">
              <User className="h-3 w-3" />
              <span className="truncate max-w-[100px]">
                {item.lastUpdatedBy}
              </span>
            </div>
          )}

          {item.lastUpdatedAt && (
            <div className="flex items-center gap-1">
              <Clock className="h-3 w-3" />
              <span>
                {formatTimeAgo(item.lastUpdatedAt)}
              </span>
            </div>
          )}
        </div>
      )}
    </div>
  );
}

/**
 * 空状态组件
 */
function EmptyState({ text }: { text: string }) {
  return (
    <div className="flex flex-col items-center justify-center py-16 text-center mx-4">
      <span className="text-gray-500 text-sm leading-relaxed">{text}</span>
    </div>
  );
}
/**
 * FunctionListContent 组件
 * 
 * 函数列表内容区域
 * UI展示：函数右侧显示更新人员和最新更新时间，下面小屏幕展示summary
 */
export function FunctionListContent({
  functions,
  selectedFuncStr,
  onSelectFunction,
  onDelete,
  onExport,
  showEmptyState = true,
  emptyStateText = "暂无函数"
}: FunctionListContentProps) {
  // 处理删除确认
  const handleDelete = (functionName: string) => {
    if (!onDelete) return;

    try {
      const confirmed = confirm(`确定要删除函数 "${functionName}" 吗？此操作不可恢复。`);
      if (confirmed) {
        onDelete(functionName);
      }
    } catch (error) {
      console.error('删除函数时发生错误:', error);
    }
  };

  // 空状态
  if (functions.length === 0 && showEmptyState) {
    return (
      <div className="flex-1 overflow-y-auto bg-white p-1">
        <EmptyState text={emptyStateText} />
      </div>
    );
  }

  return (
    <div className="flex-1 overflow-y-auto bg-white p-1">
      {functions.map((item, index) => (
        <FunctionListItem
          key={`${item.name}-${index}`}
          item={item}
          isSelected={selectedFuncStr === item.name}
          onSelect={() => onSelectFunction?.(item.name)}
          onDelete={onDelete ? () => handleDelete(item.name) : undefined}
          onExport={onExport ? () => onExport(item.name) : undefined}
        />
      ))}
    </div>
  );
}
