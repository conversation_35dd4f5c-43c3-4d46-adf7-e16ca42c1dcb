<script module>
  //    👆 notice the module context, defineMeta does not work in a regular <script> tag - instance
  import { defineMeta } from '@storybook/addon-svelte-csf';

  import BotMessageActions from './BotMessageActions.svelte';

  //      👇 Get the Story component from the return value
  const { Story } = defineMeta({
    title: 'UI/BotMessageActions',
    component: BotMessageActions,
    decorators: [
      /* ... */
    ],
    parameters: {
      /* ... */
    },
  });
</script>

<Story
  name="Default"
  args={{
    messageId: '1',
    onCopy: () => console.log('Copy!'),
    onLike: () => console.log('Like!'),
    onDislike: () => console.log('Dislike!'),
    likeStatus: 'disliked',
    onReload: () => console.log('Reload!'),
  }}
/>
