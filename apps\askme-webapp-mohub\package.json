{"name": "askme-webapp-mohub", "private": true, "version": "1.1.0", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "prepare": "svelte-kit sync && (test -f .env || cp .env.example .env) || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "format": "prettier --write .", "lint": "prettier --check . && eslint .", "test:e2e": "playwright test", "test": "pnpm test:unit:run && pnpm test:e2e", "test:unit": "vitest", "test:unit:run": "vitest run"}, "peerDependencies": {"svelte": "^5.0.0"}, "dependencies": {"@ai-sdk/openai-compatible": "^0.2.14", "@askme/lib-ui": "workspace:*", "@oslojs/crypto": "^1.0.1", "@oslojs/encoding": "^1.1.0", "@prisma/adapter-pg": "^6.9.0", "@prisma/client": "6.9.0", "ai": "^4.3.16", "arctic": "3.7.0", "dotenv": "^16.5.0", "langfuse": "^3.37.4", "minio": "^8.0.5", "mode-watcher": "1.1.0", "pg": "^8.16.0", "zod": "^3.25.58"}, "devDependencies": {"@playwright/test": "^1.53.0", "@sveltejs/adapter-node": "^5.2.12", "@sveltejs/kit": "2.21.4", "@tailwindcss/vite": "^4.1.8", "@types/node": "^24.0.0", "clsx": "^2.1.1", "eslint-config-prettier": "^10.1.5", "eslint-plugin-svelte": "3.9.2", "globals": "^16.2.0", "prettier": "^3.5.3", "prettier-plugin-svelte": "^3.4.0", "prettier-plugin-tailwindcss": "^0.6.12", "prisma": "^6.9.0", "svelte": "5.33.19", "svelte-check": "^4.2.1", "tailwindcss": "^4.1.8", "typescript": "^5.8.3", "vite": "6.3.5"}}