import React from "react"
import { ChevronDownIcon, SlashIcon } from "lucide-react"

import {
    Breadcrumb,
    BreadcrumbItem,
    BreadcrumbLink,
    BreadcrumbList,
    BreadcrumbPage,
    BreadcrumbSeparator,
} from "@/lib/components/ui/breadcrumb"
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from "@/lib/components/ui/dropdown-menu"

/**
 * 面包屑导航项数据接口
 * @description 用于面包屑导航项的数据类型定义。
 */
export interface BreadcrumbItemData {
    /** 导航项的标签 */
    label: string;
    /** 导航项的链接地址 */
    href?: string;
    /** 版本选择器 */
    versionSelector?: {
      /** 版本选择器的当前值 */
      value: string;
      /** 版本选择器的选项列表 */
      options: { value: string; label: string }[];
      /** 版本选择器的回调函数 */
      onVersionChange?: (value: string) => void;
    };
}
/**
 * 面包屑导航组件属性接口
 * @description 用于面包屑导航组件的数据类型定义。
 * @field items 导航项数据列表
 */
export interface BreadcrumbProps {
    items: BreadcrumbItemData[];
}
/**
 * 面包屑导航组件
 * @description 用于面包屑导航的组件，支持版本选择器和自定义导航处理。
 */
export function AppBreadcrumb({ items }: BreadcrumbProps) {
    return (
      <Breadcrumb>
        <BreadcrumbList className="flex items-center">
          {items.map((item, index) => (
            <React.Fragment key={item.label || index}>
              <BreadcrumbItem>
                {item.versionSelector ? (
                  // 有版本选择器时显示下拉菜单
                  <div className="flex items-center gap-1">
                    {!item.href ? (
                      <BreadcrumbPage>{item.label}</BreadcrumbPage>
                    ) : (
                      <BreadcrumbLink asChild>
                        <a href={`${item.href}?version=${item.versionSelector.value}`}>{item.label}</a>
                      </BreadcrumbLink>
                    )}
                    <DropdownMenu>
                      <DropdownMenuTrigger
                        className="flex items-center gap-1 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-3.5"
                      >
                        [{item.versionSelector.value}]
                        <ChevronDownIcon />
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="start">
                        {item.versionSelector.options.map((option) => {
                          // 版本切换逻辑 - 始终基于当前 URL 改变 version 参数
                          const currentUrl = new URL(window.location.href);
                          currentUrl.searchParams.set('version', option.value);

                          return (
                            <DropdownMenuItem key={option.value} asChild>
                              <a
                                href={currentUrl.toString()}
                                onClick={() => {
                                  item.versionSelector?.onVersionChange?.(option.value);
                                }}
                              >
                                {option.label}
                              </a>
                            </DropdownMenuItem>
                          );
                        })}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                ) : !item.href ? (
                  // 没有 href，视为不可点击
                  <BreadcrumbPage>
                    {item.label}
                  </BreadcrumbPage>
                ) : (
                  <BreadcrumbLink asChild>
                    <a href={item.href}>{item.label}</a>
                  </BreadcrumbLink>
                )}
              </BreadcrumbItem>

              {index < items.length - 1 && (
                <BreadcrumbSeparator className="mx-2">
                  <SlashIcon />
                </BreadcrumbSeparator>
              )}
            </React.Fragment>
          ))}
        </BreadcrumbList>
      </Breadcrumb>
    );
}

export default AppBreadcrumb;
