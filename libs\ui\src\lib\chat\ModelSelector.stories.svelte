<script module lang="ts">
  //    👆 notice the module context, defineMeta does not work in a regular <script> tag - instance
  import { defineMeta } from '@storybook/addon-svelte-csf';
  import type { ModelConfig } from '@askme/lib-common';
  import ModelSelector from './ModelSelector.svelte';

  //      👇 Get the Story component from the return value
  const { Story } = defineMeta({
    title: 'UI/ModelSelector',
    component: ModelSelector,
    decorators: [
      /* ... */
    ],
    parameters: {
      /* ... */
    },
  });

  const CHAT_MODELS: ModelConfig[] = [
    {
      id: 'glm-4-airx',
      name: '智谱GLM-4-AiRX',
      description: '普通聊天模型，具有超快的推理速度和强大的推理效果',
    },
    {
      id: 'glm-4-zero',
      name: '智谱GLM-Zero-Preview',
      description: '深度思考模型，具备强大的复杂推理能力',
    },
  ];
</script>

<Story name="Default" args={{ modelConfigs: CHAT_MODELS }} />
