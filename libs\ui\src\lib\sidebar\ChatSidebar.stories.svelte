<script module lang="ts">
  //    👆 notice the module context, defineMeta does not work in a regular <script> tag - instance
  import { defineMeta } from '@storybook/addon-svelte-csf';
  import * as Sidebar from '$lib/components/ui/sidebar/index.js';
  import ChatSidebar from './ChatSidebar.svelte';
  import type { ChatModel } from '@askme/lib-common';
  import SidebarToggle from './SidebarToggle.svelte';

  //      👇 Get the Story component from the return value
  const { Story } = defineMeta({
    title: 'UI/ChatSidebar',
    component: ChatSidebar,
    decorators: [
      /* ... */
    ],
    parameters: {
      /* ... */
    },
  });
  const chats: ChatModel[] = [
    {
      id: '1',
      createdAt: new Date(),
      title: '如何理解Julia的多重派发',
    },
    {
      id: '2',
      createdAt: new Date('2025-03-06T15:30:00'),
      title: 'Go的泛型是怎么实现的',
    },
    {
      id: '3',
      createdAt: new Date('2025-03-04T15:30:00'),
      title: '哪吒2好看吗？',
    },
    {
      id: '4',
      createdAt: new Date('2025-02-06T15:30:00'),
      title: 'steam游戏怎么老打折？',
    },
    {
      id: '5',
      createdAt: new Date('2025-02-06T15:30:00'),
      title: '怎么哄男朋友开心？',
    },
  ];
</script>

<Story name="Default" asChild>
  <Sidebar.Provider>
    <ChatSidebar
      sidebarTitle="MWORKS 智能问答助手"
      newChatButtonTitle="开启新对话"
      {chats}
      onNewChat={() => console.log('new chat!')}
    />
    <main>
      <SidebarToggle />
    </main>
  </Sidebar.Provider>
</Story>
