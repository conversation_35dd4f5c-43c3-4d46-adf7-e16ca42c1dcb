<script module lang="ts">
  //    👆 notice the module context, define<PERSON><PERSON> does not work in a regular <script> tag - instance
  import { defineMeta } from '@storybook/addon-svelte-csf';

  import ChatPanel from './ChatPanel.svelte';
  import { Chat, type MessageTreeRoot } from '@askme/lib-common';

  //      👇 Get the Story component from the return value
  const { Story } = defineMeta({
    title: 'UI/ChatPanel',
    component: ChatPanel,
    decorators: [
      /* ... */
    ],
    parameters: {
      /* ... */
    },
  });
</script>

<script lang="ts">
  const messages = {
    child: [
      {
        id: '1',
        role: 'user',
        content: 'Hello',
        parts: [{ type: 'text', text: 'Hello' }],
        createdAt: new Date(),
        child: [
          {
            id: '2',
            role: 'assistant',
            content: 'Response 1',
            parts: [{ type: 'text', text: 'Response 1' }],
            createdAt: new Date(),
            child: [],
          },
          {
            id: '3',
            role: 'assistant',
            content: 'Response 2',
            parts: [{ type: 'text', text: 'Response 2' }],
            createdAt: new Date(),
            child: [],
          },
          {
            id: '4',
            role: 'assistant',
            content: 'Response 3',
            parts: [{ type: 'text', text: 'Response 3' }],
            createdAt: new Date(),
            child: [],
          },
        ],
      },
    ],
    infoMap: new Map([
      [null, { level: 0, parentMsgId: null, childCount: 1 }],
      ['1', { level: 1, parentMsgId: null, childCount: 3 }],
      ['2', { level: 2, parentMsgId: '1', childCount: 0 }],
      ['3', { level: 2, parentMsgId: '1', childCount: 0 }],
      ['4', { level: 2, parentMsgId: '1', childCount: 0 }],
    ]),
  } as MessageTreeRoot;

  const chatClient = new Chat({
    id: '1',
    initialTree: messages,
  });
</script>

<Story name="Default" asChild>
  <div class="bg-background flex h-dvh flex-col">
    <div class="flex min-w-0 flex-1 flex-col gap-6 overflow-y-scroll px-[max(10vw,32px)] pt-8">
      <ChatPanel {chatClient} />
    </div>
  </div>
</Story>
