import type { PrismaClient } from '$lib/generated/prisma';
import type { GitLab } from 'arctic';
import type <PERSON><PERSON> from 'langfuse';
import * as Minio from 'minio';

interface OAuthClients {
  gitlab: GitLab;
}

/** Resources that are available to the server. */
export interface ServerResources {
  /** The Prisma client. */
  prisma: PrismaClient;
  /** The oauth clients. */
  oauth?: OAuthClients;
  /** The s3 client. */
  s3: Minio.Client;
  /** The langfuse client. */
  langfuse: Langfuse;
}

/**
 * OAuth用户信息
 */
export interface UserInfo {
  UID: string;
  username: string;
  email: string | null;
}

/**
 * 向s3请求文件所必须的配置信息
 */
export interface RequestS3Config {
  minioClient: Minio.Client;
  userId: number;
  bucketName: string;
  prefix?: string;
}

/**
 * 内容安全审查结果
 */
export interface ContentModerationResult {
  isRiskQuery: boolean;
  riskQueryAnswer: string;
}

/**
 * 消息部分类型
 */
export interface MessagePart {
  type: string;
  text?: string;
  // 根据需要添加其他属性
}

/**
 * 聊天搜索结果
 */
export interface ChatSearchResult {
  chatId: string;
  title: string;
  dateStr: string;
  detail: string;
}

/**
 * 从数据库中搜索的聊天结果
 */
export interface DBChatSearchResult {
  matchedText: string;
  chatId: string;
  chatTitle: string;
  createdAt: Date;
}
