import type { <PERSON>a, StoryObj } from "@storybook/react-vite";
import { LogoutButton } from "./LogoutButton";

const meta: Meta<typeof LogoutButton> = {
  title: "Components/LogoutButton",
  component: LogoutButton,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    variant: {
      control: { type: "select" },
      options: ["default", "outline", "secondary", "ghost", "link", "destructive"],
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    url: "/logout",
    content: "登出",
  },
};




