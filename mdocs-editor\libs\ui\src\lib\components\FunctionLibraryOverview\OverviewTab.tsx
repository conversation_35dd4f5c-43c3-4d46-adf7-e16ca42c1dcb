
export function OverviewTab({ summary, maintainer, lastUpdate }: {
  summary: string;
  maintainer: string;
  lastUpdate: string | Date;
}) {
  return (
    <div className="space-y-4 overflow-y-auto h-[500px]">
      <div className="text-base font-medium">
        Summary:
        <div className="ml-2 text-gray-700 leading-relaxed mt-1 break-words whitespace-pre-wrap">
          {summary}
        </div>
      </div>
      <div className="text-base font-medium">
        Maintainer: <span className="ml-2 text-gray-700">{maintainer}</span>
      </div>
      <div className="text-base font-medium">
        Last update: <span className="ml-2 text-gray-700">
          {typeof lastUpdate === 'string' ? lastUpdate : lastUpdate?.toLocaleDateString() || '-'}
        </span>
      </div>
    </div>
  );
}
export default OverviewTab; 