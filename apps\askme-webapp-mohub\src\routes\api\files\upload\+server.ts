// src/routes/api/files/upload/+server.ts
import type { RequestHandler } from './$types';
import { json, error } from '@sveltejs/kit';
import serverCtx from '$lib/context.server';
import * as path from 'path';
import { Readable } from 'stream';
import * as LogFormat from '@askme/lib-common/logformat';
import { MAX_FILE_SIZE_IN_MB } from '$lib/config';
import { config } from '$lib/config.server';
import { buildFullPath } from '$lib/s3utils';

// 用于统一添加 CORS 响应头（包括 POST 和 OPTIONS）
function withCORSHeaders(response: Response): Response {
  response.headers.set('Access-Control-Allow-Origin', 'https://askme.tongyuan.cc:5051');
  response.headers.set('Access-Control-Allow-Credentials', 'true');
  response.headers.set('Access-Control-Allow-Methods', 'POST, OPTIONS');
  response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  return response;
}

// 处理预检请求（CORS OPTIONS）
export const OPTIONS: RequestHandler = async () => {
  return withCORSHeaders(new Response(null, { status: 204 }));
};

// 文件上传
export const POST: RequestHandler = async ({ request, locals }) => {
  if (!locals.user) {
    console.debug(LogFormat.debug('Unauthorized in /api/files/upload POST'));
    throw error(401, 'Unauthorized');
  }

  const userId = locals.user.id;
  const formData = await request.formData();
  const file = formData.get('file') as File;

  if (!file) {
    throw error(400, 'No file uploaded');
  }
  if (file.size > MAX_FILE_SIZE_IN_MB * 1024 * 1024) {
    throw error(400, 'File too large');
  }

  try {
    const originalExt = path.extname(file.name).toLowerCase();
    const uuid = crypto.randomUUID();
    const uniqueFileName = `${uuid}${originalExt}`;

    const fileStream = file.stream();

    await serverCtx.s3.putObject(
      config.s3.bucket,
      buildFullPath([`${userId}`, uniqueFileName], config.s3.prefix),
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      Readable.fromWeb(fileStream as any),
      file.size,
      {
        'Content-Type': file.type,
      },
    );

    return withCORSHeaders(
      json({
        success: true,
        uuid,
      }),
    );
  } catch (err) {
    console.error('File upload error:', err);
    throw error(500, 'Failed to upload files');
  }
};
