import { useState, useRef, useEffect } from "react";
import { Upload, FileText, X, AlertCircle } from "lucide-react";
import { Button } from "@/lib/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/lib/components/ui/dialog";

/**
 * ImportDialog 组件属性接口
 */
export interface ImportDialogProps {
  /** 对话框是否打开 */
  open: boolean;
  /** 对话框打开状态变化回调 */
  onOpenChange: (open: boolean) => void;
  /** 导入文件回调函数 */
  onImport: (file: File) => void | Promise<void>;
  /** 加载状态 */
  loading?: boolean;
  /** 允许的文件类型，格式为 ".ext1,.ext2"（例如 ".json,.txt"） */
  allowedFileTypes: string;
  /** 最大文件大小限制（单位：MB，默认 10MB） */
  maxFileSize?: number;
}

/**
 * ImportDialog 组件
 * 功能：
 * - 提供导入对话框
 * - 支持文件拖拽和选择上传
 * - 增强文件类型验证（后缀 + MIME 类型）
 * - 大文件限制提示
 */
export function ImportDialog({
  open,
  onOpenChange,
  onImport,
  loading = false,
  allowedFileTypes = '.json',
  maxFileSize = 10, // 默认 10MB
}: ImportDialogProps) {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isDragOver, setIsDragOver] = useState(false);
  const [error, setError] = useState("");
  const fileInputRef = useRef<HTMLInputElement>(null);
  // 转换最大文件大小为字节
  const maxFileSizeBytes = maxFileSize * 1024 * 1024;

  // 重置状态
  useEffect(() => {
    if (open) {
      setSelectedFile(null);
      setError("");
      setIsDragOver(false);
    } else {
      // 关闭时重置文件输入框，允许重新选择同一文件
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    }
  }, [open]);

  /**
   * 验证文件（后缀 + 大小）
   * @param file - 待验证的文件
   * @returns 错误信息（空字符串表示验证通过）
   */
  const validate =  (file: File): string => {
    // 1. 验证文件大小
    if (file.size > maxFileSizeBytes) {
      return `文件过大（${(file.size / 1024 / 1024).toFixed(2)}MB），最大支持 ${maxFileSize}MB`;
    }
    // 2. 标准化允许的文件类型（去重、小写、去空格）
    const allowedTypes = allowedFileTypes
      .split(',')
      .map(type => type.trim().toLowerCase())
      .filter(Boolean)
      .filter((type, index, self) => self.indexOf(type) === index);

    // 3. 提取并验证文件后缀
    const fileNameParts = file.name.split('.');
    const fileExtension = fileNameParts.length > 1 
      ? `.${fileNameParts.pop()?.toLowerCase()}` 
      : '';

    // 无后缀的情况
    if (!fileExtension) {
      return `文件无后缀，仅支持: ${allowedTypes.join(', ')}`;
    }

    // 后缀不匹配的情况
    if (!allowedTypes.includes(fileExtension)) {
      return `不支持的文件格式，仅支持: ${allowedTypes.join(', ')}`;
    }
    return "";
  };

  /** 处理文件选择 */
  const handleFileSelect =  (file: File) => {
    const validationError =  validate(file);
    if (validationError) {
      setError(validationError);
      return;
    }

    setSelectedFile(file);
    setError("");
  };

  /** 处理拖拽进入 */
  const handleDragEnter = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  /** 处理拖拽离开 */
  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  /** 处理拖拽释放 */
  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);

    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileSelect(files[0]);
    }
  };

  /** 处理文件输入变化 */
  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      handleFileSelect(files[0]);
    }
  };

  /** 处理导入逻辑 */
  const handleImport = async () => {
    if (!selectedFile) return;

    try {
      await onImport(selectedFile);
      onOpenChange(false);
    } catch (error) {
      setError(error instanceof Error ? error.message : "导入失败，请重试");
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>导入文件</DialogTitle>
          <DialogDescription>
            选择或拖拽文件进行导入（支持 {allowedFileTypes} 格式，最大 {maxFileSize}MB）
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          {/* 文件上传区域 */}
          <div
            className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
              isDragOver
                ? "border-primary bg-primary/5"
                : "border-muted-foreground/25 hover:border-muted-foreground/50"
            }`}
            onDragEnter={handleDragEnter}
            onDragOver={handleDragEnter}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
          >
            <Upload className="mx-auto h-12 w-12 text-muted-foreground" />
            <div className="mt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => fileInputRef.current?.click()}
                disabled={loading}
              >
                选择文件
              </Button>
              <p className="mt-2 text-sm text-muted-foreground">
                支持 {allowedFileTypes} 格式 · 最大 {maxFileSize}MB
              </p>
            </div>
            <input
              ref={fileInputRef}
              type="file"
              className="hidden"
              accept={allowedFileTypes}
              onChange={handleFileInputChange}
              disabled={loading}
            />
          </div>

          {/* 已选择的文件 */}
          {selectedFile && (
            <div className="flex items-center justify-between rounded-lg border p-3">
              <div className="flex items-center space-x-2">
                <FileText className="h-4 w-4" />
                <div>
                  <p className="text-sm font-medium truncate max-w-[200px]">
                    {selectedFile.name}
                  </p>
                  <p className="text-xs text-muted-foreground">
                    {selectedFile.size} 字节 · {selectedFile.type || "未知类型"}
                  </p>
                </div>
              </div>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => setSelectedFile(null)}
                disabled={loading}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          )}

          {/* 错误信息 */}
          {error && (
            <div className="p-3 bg-destructive/10 text-destructive rounded-md text-sm flex items-start gap-2">
              <AlertCircle className="h-4 w-4 mt-0.5 flex-shrink-0" />
              <span>{error}</span>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={loading}
          >
            取消
          </Button>
          <Button
            onClick={handleImport}
            disabled={!selectedFile || loading}
          >
            {loading ? "导入中..." : "导入"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

export default ImportDialog;