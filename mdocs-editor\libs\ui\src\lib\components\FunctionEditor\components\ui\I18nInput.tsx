import React, { useState } from "react";
import { Input } from "../../../ui/input";
import { Textarea } from "../../../ui/textarea";
import { Label } from "../../../ui/label";
import { Button } from "../../../ui/button";
import { Plus, X } from "lucide-react";
/**
 * 国际化文本接口
 */
export interface I18nText {
  /** 中文内容 */
  zh: string;
  /** 英文内容 */
  en: string;
}

/**
 * 国际化输入组件的属性接口
 */
export interface I18nInputProps {
  /** 字段标签 */
  label: string;
  /** 当前值 */
  value?: I18nText;
  /** 值变化回调 */
  onChange: (value: I18nText) => void;
  /** 是否禁用 */
  disabled?: boolean;
  /** 中文占位符 */
  placeholderZh?: string;
  /** 英文占位符 */
  placeholderEn?: string;
  /** 是否必填 */
  required?: boolean;
  /** 输入类型 */
  type?: 'input' | 'textarea';
  /** 文本区域行数（仅当 type='textarea' 时有效） */
  rows?: number;
  /** 字段ID前缀 */
  idPrefix?: string;
}

/**
 * 国际化输入组件
 * 
 * 提供中英文双语输入界面，减少重复的双语输入代码
 */
export const I18nInput: React.FC<I18nInputProps> = ({
  label,
  value = { zh: '', en: '' },
  onChange,
  disabled = false,
  placeholderZh = '',
  placeholderEn = '',
  required = false,
  type = 'input',
  rows = 3,
  idPrefix = 'i18n',
}) => {
  // 语言字段显示状态
  const [showZh, setShowZh] = useState(!!value?.zh);
  const [showEn, setShowEn] = useState(!!value?.en);

  /**
   * 更新指定语言的值
   * @param lang 语言代码
   * @param newValue 新值
   */
  const updateValue = (lang: keyof I18nText, newValue: string) => {
    onChange({
      ...value,
      [lang]: newValue,
    });
  };

  /**
   * 添加语言字段
   */
  const addLanguage = (lang: keyof I18nText) => {
    if (lang === 'zh') {
      setShowZh(true);
      if (!value?.zh) updateValue('zh', '');
    } else {
      setShowEn(true);
      if (!value?.en) updateValue('en', '');
    }
  };

  /**
   * 删除语言字段
   */
  const removeLanguage = (lang: keyof I18nText) => {
    if (lang === 'zh') {
      setShowZh(false);
      updateValue('zh', '');
    } else {
      setShowEn(false);
      updateValue('en', '');
    }
  };

  const InputComponent = type === 'textarea' ? Textarea : Input;

  // 根据rows设置滚动条：计算行高来设置最大高度
  const lineHeight = 25; // 每行大约25px
  const maxHeight = rows * lineHeight;

  const inputProps = type === 'textarea' ? {
    rows,
    className: `min-h-12 resize-y overflow-y-auto`,
    style: {
      maxHeight: `${maxHeight}px`,
      lineHeight: `${lineHeight}px`
    }
  } : {};

  return (
    <div className="space-y-2">
      {/* 标题和添加按钮 */}
      <div className="flex items-center justify-between">
        <Label className="text-base font-medium">
          {label}{required && <span className="text-red-500 ml-1">*</span>}
        </Label>

        {/* 添加语言按钮 */}
        <div className="flex items-center gap-1">
          {!showZh && (
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => addLanguage('zh')}
              disabled={disabled}
            >
              <Plus className="w-4 h-4 mr-1" />
              中文
            </Button>
          )}
          {!showEn && (
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => addLanguage('en')}
              disabled={disabled}
            >
              <Plus className="w-4 h-4 mr-1" />
              English
            </Button>
          )}
        </div>
      </div>

      {/* 输入字段 */}
      <div className="pl-4 space-y-2">
        {/* 中文输入 */}
        {showZh && (
          <div className="space-y-1">
            <div className="flex items-center justify-between">
              <Label htmlFor={`${idPrefix}-zh`} className="text-sm text-gray-600">
                中文
              </Label>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => removeLanguage('zh')}
                disabled={disabled}
                className="h-5 w-5 p-0 hover:text-red-500 rounded"
                title="删除"
              >
                <X className="w-4 h-4" />
              </Button>
            </div>
            <InputComponent
              id={`${idPrefix}-zh`}
              value={value?.zh || ''}
              onChange={(e) => updateValue('zh', e.target.value)}
              disabled={disabled}
              placeholder={placeholderZh}
              {...inputProps}
            />
          </div>
        )}

        {/* 英文输入 */}
        {showEn && (
          <div className="space-y-1">
            <div className="flex items-center justify-between">
              <Label htmlFor={`${idPrefix}-en`} className="text-sm text-gray-600">
                English
              </Label>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => removeLanguage('en')}
                disabled={disabled}
                className="h-5 w-5 p-0 hover:text-red-500 rounded"
                title="删除"
              >
                <X className="w-4 h-4" />
              </Button>
            </div>
            <InputComponent
              id={`${idPrefix}-en`}
              value={value?.en || ''}
              onChange={(e) => updateValue('en', e.target.value)}
              disabled={disabled}
              placeholder={placeholderEn}
              {...inputProps}
            />
          </div>
        )}

        {/* 空状态提示 */}
        {!showZh && !showEn && (
          <div className="text-center py-1 text-gray-500 text-sm">
            点击右上角按钮添加语言版本
          </div>
        )}
      </div>
    </div>
  );
};
