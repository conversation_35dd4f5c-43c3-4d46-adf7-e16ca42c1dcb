import type { LayoutServerLoad } from './$types';
import { config } from '$lib/config.server';

export const load: LayoutServerLoad = async ({}) => {
  const defaultTheme = config.theme.default_theme;
  let theme = 'orange';
  let mode: 'dark' | 'light' = 'light';
  switch (defaultTheme) {
    case 'ORANGE':
      theme = 'orange';
      mode = 'light';
      break;
    case 'DARK':
      theme = 'default';
      mode = 'dark';
      break;
    case 'LIGHT':
      theme = 'default';
      mode = 'light';
      break;
  }
  return {
    enableThemeSwitcher: config.theme.enable_theme_switcher,
    theme,
    mode,
  };
};
