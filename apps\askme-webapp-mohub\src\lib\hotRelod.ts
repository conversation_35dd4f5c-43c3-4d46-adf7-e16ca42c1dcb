import { z } from 'zod/v4';
import { getCachedFile } from './fileCache';
import type { SuggestedActionModel } from '@askme/lib-common';
import { SUGGESTED_ACTIONS } from './config';
import { env } from '$env/dynamic/private';
import { DEFAULT_ASKME_CONFIG } from './config';

const askmeConfigSchema = z.object({
  appTitle: z.string().min(1).default(DEFAULT_ASKME_CONFIG.appTitle),
  llmModels: z.object({
    chatModel: z.string().min(1).default(DEFAULT_ASKME_CONFIG.llmModels.chatModel),
    reasonModel: z.string().min(1).default(DEFAULT_ASKME_CONFIG.llmModels.reasonModel),
    multiModalModel: z.string().min(1).default(DEFAULT_ASKME_CONFIG.llmModels.multiModalModel),
  }),
});

type AskMeConfig = z.infer<typeof askmeConfigSchema>;

export const hotReload = {
  get_demo_questions: () =>
    getCachedFile(
      z
        .string()
        .endsWith('.json')
        .parse(env.DEMO_QUESTIONS_PATHS ?? './demo_questions.json'),
      (text) => JSON.parse(text) as SuggestedActionModel[],
      SUGGESTED_ACTIONS,
    ),
  get_askme_config: () =>
    getCachedFile(
      z
        .string()
        .endsWith('.json')
        .parse(env.ASKME_CONFIG_PATHS ?? './askme_config.json'),
      (text) => askmeConfigSchema.parse(JSON.parse(text)) as AskMeConfig,
      DEFAULT_ASKME_CONFIG,
    ),
};
