import type { Meta, StoryObj } from '@storybook/react-vite';
import { LibraryToolbar } from './LibraryToolbar';

const meta: Meta<typeof LibraryToolbar> = {
  title: 'Components/LibraryManager/LibraryToolbar',
  component: LibraryToolbar,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: '库工具栏组件，包含导入和导出所有功能。',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    disabled: {
      description: '是否禁用所有按钮',
      control: 'boolean',
    },
    libraryCount: {
      description: '库的数量，用于判断导出所有按钮状态',
      control: 'number',
    },
    onImport: {
      description: '导入回调函数',
    },
    onExportAll: {
      description: '导出所有回调函数',
    },
  },
};

export default meta;
type Story = StoryObj<typeof LibraryToolbar>;

/**
 * 默认状态
 */
export const Default: Story = {
  args: {
    disabled: false,
    libraryCount: 5,
    onImport: (file: File) => console.log('Import file:', file.name),
    onExportAll: () => console.log('Export all libraries'),
  },
};

/**
 * 禁用状态
 */
export const Disabled: Story = {
  args: {
    disabled: true,
    libraryCount: 5,
    onImport: (file: File) => console.log('Import file:', file.name),
    onExportAll: () => console.log('Export all libraries'),
  },
};

