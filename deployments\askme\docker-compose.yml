version: "3.9"

services:
  database:
    image: postgres:15
    container_name: askme-postgres
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 3s
      timeout: 3s
      retries: 10
    environment:
      POSTGRES_DB: askme
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5433:5432"
    volumes:
      - pgdata:/var/lib/postgresql/data

  init-db:
    image: git.tongyuan.cc:5050/ai/askme/askme-webapp-mono/askme-app-mohub:latest
    container_name: askme-init-db
    depends_on:
      database:
        condition: service_healthy
    volumes:
      - ./.env:/app/apps/askme-webapp-mohub/.env
    entrypoint: ["pnpm", "exec", "prisma", "migrate", "deploy"]
    restart: "no"

  webapp:
    image: git.tongyuan.cc:5050/ai/askme/askme-webapp-mono/askme-app-mohub:latest
    container_name: askme-webapp
    depends_on:
      - database
      - init-db
    volumes:
      - ./.env:/app/apps/askme-webapp-mohub/.env
    ports:
      - "5173:5173"
    environment:
      ORIGIN: https://askme.tongyuan.cc:5051
      BODY_SIZE_LIMIT: 10M
    command: ["node", "-r", "dotenv/config", "build/index.js"]

volumes:
  pgdata:
