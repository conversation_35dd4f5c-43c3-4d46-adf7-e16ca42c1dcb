import { config } from '$lib/config.server';

export async function GET(): Promise<Response> {
  if (config.oauth.provider.toUpperCase() !== 'KEYCLOAK') {
    return new Response('Unauthorized', {
      status: 401,
    });
  }

  const oauthParams = new URLSearchParams({
    client_id: config.oauth.application_id,
    response_type: 'code',
    redirect_uri: config.oauth.redirect_uri,
    scope: config.oauth.scopes.join(' '),
  });

  const oauthURL = `${config.oauth.authorization_url}?${oauthParams.toString()}`;

  return new Response(null, {
    status: 302,
    headers: {
      Location: oauthURL.toString(),
    },
  });
}
