# 部署

该目录提供了一个基于 docker-compose 方案的 askme 项目部署配置，但部署需要接入以下现有服务：

- 【必须】[llm-store](http://ai.gitpages.tongyuan.cc/copilot/llm-store/): 同元自研 LLM 代理服务及 RAG 搜索服务
- 【必须】S3 存储：Minio 或 华为云 OBS
- 【必须】OAuth 授权服务：Gitlab 或 [同元 Login](https://login.tongyuan.cc)
- 【可选】Langfuse 日志服务
- 【可选】阿里云内容审核服务

部署所需的环境变量配置见 [env.example](../../apps/askme-webapp-mohub/.env.example).

## 部署配置

### 调整askme配置 （可热更新）

通过提供类似下述格式的 JSON 文件，并设置好 `ASKME_CONFIG_PATHS` 环境变量即可覆盖默认配置。

```json
{
  "appTitle": "MoHub智能问答助手",
  "llmModels": {
    "chatModel": "mm-plus",
    "reasonModel": "mm-plus-reason",
    "multiModalModel": "mm-vision-plus"
  }
}
```

字段说明：

- appTitle: askme应用的默认标题.
- llmModels: askme应用的LLM模型配置.
  - chatModel: askme应用的对话模型id.
  - reasonModel: askme应用的推理模型id.
  - multiModalModel: askme应用的多模态模型id.

### 调整示例问题（可热更新）

在当前聊天还未开始时，askme 会在输入框上方随机提供示例问题，方便用户进行快速测试。
通过提供类似下述格式的 JSON 文件，并设置好 `DEMO_QUESTIONS_PATHS` 环境变量即可覆盖默认示例。

```json
[
  {
    "title": "Julia语言有哪些优点",
    "label": "相较于Python",
    "action": "相较与Python, Julia语言有哪些优点"
  },
  {
    "title": "Modelica语言是什么",
    "label": "简单介绍一下",
    "action": "介绍一下Modelica语言"
  },
  {
    "title": "Syslab2025a",
    "label": "有什么新特性",
    "action": "介绍一下Syslab2025a的新特性"
  },
  {
    "title": "Sysplorer2025a",
    "label": "有什么新特性",
    "action": "介绍一下Sysplorer2025a的新特性"
  }
]
```

### docker-compose 部署配置

对于 docker-compose 容器方式部署的场景，可以补充以下配置：

```diff
webapp:
    image: git.tongyuan.cc:5050/ai/askme/askme-webapp-mono/askme-app-mohub:latest
    container_name: askme-webapp
    depends_on:
      - database
      - init-db
    volumes:
      - ./.env:/app/apps/askme-webapp-mohub/.env
+     - ./questions.json:/app/apps/askme-webapp-mohub/questions.json:ro
+     - ./askme_config.json:/app/apps/askme-webapp-mohub/askme_config.json:ro
    ports:
      - "5173:5173"
    environment:
+      DEMO_QUESTIONS_PATHS: /app/apps/askme-webapp-mohub/questions.json
+      ASKME_CONFIG_PATHS: /app/apps/askme-webapp-mohub/askme_config.json
      ORIGIN: https://askme.tongyuan.cc:5051
      BODY_SIZE_LIMIT: 10M
    command: ["node", "-r", "dotenv/config", "build/index.js"]
```

该 `DEMO_QUESTIONS_PATHS` 环境变量也可以写到 `.env` 文件中。

### 内容审核服务

内容安全审查服务会对用户的问题输入进行安全审查，从而避免对涉及政治、恐怖等敏感话题的回答。
对于公网产品，推荐增加这一检查环节。

目前仅支持阿里云，所需的环境变量配置见 [env.example](../../apps/askme-webapp-mohub/.env.example).
