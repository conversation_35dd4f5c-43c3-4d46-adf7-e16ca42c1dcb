stages:
  - lint
  - test
  - build
  - deploy

cache:
  key:
    files:
      - ${CI_PROJECT_DIR}/pnpm-lock.yaml
  paths:
    - ${CI_PROJECT_DIR}/.pnpm-store/

.rules_on_merge_request:
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH && $CI_OPEN_MERGE_REQUESTS
      when: never
    - if: $CI_COMMIT_BRANCH

.prepare:
  image: node:22
  tags:
    - docker-linux-x86_64
  before_script:
    - |
      sed -i 's/deb.debian.org/mirrors-dev.tongyuan.cc/g' /etc/apt/sources.list.d/debian.sources
      echo 'Acquire::Check-Valid-Until "0";' >> /etc/apt/apt.conf.d/10no--check-valid-until
    - |
      apt-get -qq update
      apt-get install -y -qq libsecret-1-dev
    - node -v
    - npm -v
    - npm config set registry https://mirrors-dev.tongyuan.cc/npm
    - npm install -g pnpm@9
    - pnpm -v
    - pnpm config set store-dir $CI_PROJECT_DIR/.pnpm-store
    - pnpm config set registry https://mirrors-dev.tongyuan.cc/npm
    - pnpm install --frozen-lockfile
    - pnpm -C apps/askme-webapp-mohub exec prisma generate

静态检查:
  image: node:22
  stage: lint
  script:
    - pnpm run check-versions
    - pnpm run build:libs
    - pnpm run check
    - pnpm run lint
  rules:
    - !reference [.rules_on_merge_request, rules]
  extends: .prepare

build-storybook:
  image: node:22
  stage: build
  rules:
    - !reference [.rules_on_merge_request, rules]
  extends: .prepare
  dependencies: []
  script:
    - pnpm --filter "./libs/ui" build-storybook
    - mkdir -p dist/libs/ui
    - mv ./libs/ui/storybook-static dist/libs/ui/storybook
  artifacts:
    paths:
      - dist

build-webapp-mohub:
  image: node:22
  stage: build
  rules:
    - !reference [.rules_on_merge_request, rules]
  extends: .prepare
  dependencies: []
  script:
    - pnpm run build:libs
    - pnpm run --filter "./apps/askme-webapp-mohub" build
    - mkdir -p dist/apps/askme-webapp-mohub
    - mv ./apps/askme-webapp-mohub/build dist/apps/askme-webapp-mohub/build
  artifacts:
    paths:
      - dist

deploy_review_storybook:
  image: node:22
  stage: deploy
  tags: [docker]
  script:
    - echo "Deploy a review app"
  rules:
    - !reference [.rules_on_merge_request, rules]
  environment:
    name: review/storybook-$CI_COMMIT_REF_SLUG
    url: http://ai.gitpages.tongyuan.cc/askme/askme-webapp-mono/$CI_COMMIT_REF_SLUG/storybook
    on_stop: remove_review_storybook
  resource_group: review/storybook-$CI_COMMIT_REF_SLUG
  needs: ["pages"]

remove_review_storybook:
  image: node:22
  stage: deploy
  tags: [docker]
  script:
    - echo "Remove review app"
  environment:
    name: review/storybook-$CI_COMMIT_REF_SLUG
    action: stop
  rules:
    - !reference [.rules_on_merge_request, rules]
  when: manual
  resource_group: review/storybook-$CI_COMMIT_REF_SLUG

pages:
  image: node:22
  stage: deploy
  tags: [docker]
  dependencies:
    - build-storybook
  rules:
    - !reference [.rules_on_merge_request, rules]
  script:
    - if [ "$CI_COMMIT_BRANCH" = "main" ]; then
      mkdir -p public;
      touch public/index.html;
      echo "<!DOCTYPE HTML><script>window.location.href = 'http://ai.gitpages.tongyuan.cc/askme/askme-webapp-mono/main/storybook'</script>" > public/index.html;
      fi
    - rm -rf public/$CI_COMMIT_REF_SLUG
    - mkdir -p public/$CI_COMMIT_REF_SLUG
    - mv dist/libs/ui/storybook public/$CI_COMMIT_REF_SLUG/storybook
  artifacts:
    paths:
      - public

build-webapp-mohub-docker:
  tags:
    - podman
  stage: build
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH && $CI_COMMIT_REF_PROTECTED
    - if: $CI_COMMIT_TAG
  variables:
    IMAGE_PREFIX: git.tongyuan.cc:5050/ai/askme/askme-webapp-mono
  script:
    - podman login git.tongyuan.cc:5050 -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD
    - podman build -f deployments/askme/Dockerfile -t $IMAGE_PREFIX/askme-app-mohub:latest .
    - podman push $IMAGE_PREFIX/askme-app-mohub:latest
    - |
      if [ -n "$CI_COMMIT_TAG" ]; then
        podman tag $IMAGE_PREFIX/askme-app-mohub:latest $IMAGE_PREFIX/askme-app-mohub:$CI_COMMIT_TAG
        podman push $IMAGE_PREFIX/askme-app-mohub:$CI_COMMIT_TAG
      fi

deploy-tongyuan-preview:
  image: git.tongyuan.cc:5050/syslab/ci/tools/ubuntu:20.04-full-python3.10
  tags:
    - docker
  stage: deploy
  rules:
    - if: '$CI_COMMIT_BRANCH && $CI_COMMIT_BRANCH != "" && $CI_COMMIT_REF_PROTECTED == "true"'
      when: on_success
    - when: never
  needs: ["build-webapp-mohub-docker"]
  environment:
    name: tongyuan-preview
    url: https://askme.tongyuan.cc:5051
  script:
    - |
      if [ -z "$CONFIG_ENVFILE" ]; then
        echo "Error: CONFIG_ENVFILE environment variable is not set"
        exit 1
      fi
    - cp $DEPLOY_SSH_PRIVATE_KEY id_rsa
    - chmod 400 id_rsa
    - bash deployments/askme/deploy.sh
