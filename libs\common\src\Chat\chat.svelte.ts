// 由ai sdk svelte修改而来，和原库不一样的行为：
// 1.messages字段是一个外部可写的MessageTreeRoot
// 2.使用initialTree字段初始化消息
// 3.前端的线性的消息由linearMessages取得
// 4.可以指定需要reload的message id
// 5.错误处理时，onError推荐作为错误处理程序，用于恢复异常状态
// 其他部分暂时没有修改，数据交换仍通过ui-utils使用stream Protocol

import {
  generateId,
  type UIMessage,
  type UseChatOptions,
  type JSONValue,
  type ChatRequest,
  extractMaxToolInvocationStep,
  callChatApi,
  shouldResubmitMessages,
  type Message,
  type CreateMessage,
  type ChatRequestOptions,
  getMessageParts,
  type Attachment,
} from '@ai-sdk/ui-utils';
import { isAbortError } from '@ai-sdk/provider-utils';
import { KeyedChatStore, getChatContext, hasChatContext } from './chat-context.svelte';
import { untrack } from 'svelte';
import { initMessageTree, insertMessageNode, isNodeExist, updateMessageNode } from '../messageTree';
import type { MessageTreeRoot } from '../messageTree';
import { insertAction } from '../switchActions';

export type ChatOptions = Readonly<
  Omit<UseChatOptions, 'keepLastMessageOnError'> &
    Omit<UseChatOptions, 'initialMessages'> & {
      /**
       * Maximum number of sequential LLM calls (steps), e.g. when you use tool calls.
       * Must be at least 1.
       * A maximum number is required to prevent infinite loops in the case of misconfigured tools.
       * By default, it's set to 1, which means that only a single LLM call is made.
       * @default 1
       */
      maxSteps?: number;
      initialTree?: MessageTreeRoot;
      /**
       * 保存手动中断的用户消息到后端的api地址
       * 接收json格式如下：
       * {
            chatID: string,
            preID: string,
            message: UIMessage,
          }
       */
      saveMessageAPI?: string;
    }
>;

export type UIAttachment = Attachment & {
  uuid: string;
  fileSize: number;
};

export type ChatRequestOpts = Readonly<
  Omit<ChatRequestOptions, 'experimental_attachments'> & {
    attachments?: UIAttachment[];
  }
>;

export type { CreateMessage, Message, UIMessage };

/** ⚠️在$derive(new Chat())的时候，务必保证Chat只响应chatId的变化，untrack掉其他传入的响应式变量，否则可能因为意外的状态变化重新计算Chat，打断当前输出。
 */
export class Chat {
  readonly #options: ChatOptions = {};
  readonly #api = $derived(this.#options.api ?? '/api/chat');
  readonly #generateId = $derived(this.#options.generateId ?? generateId);
  readonly #maxSteps = $derived(this.#options.maxSteps ?? 1);
  readonly #streamProtocol = $derived(this.#options.streamProtocol ?? 'data');
  readonly #keyedStore = $state<KeyedChatStore>()!;

  /**
   * The id of the chat. If not provided through the constructor, a random ID will be generated
   * using the provided `generateId` function, or a built-in function if not provided.
   */
  readonly id = $derived(this.#options.id ?? this.#generateId());
  readonly #store = $derived(this.#keyedStore.get(this.id));
  /** 由树和用户操作映射出的前端线性历史 */
  get linearMessages() {
    return this.#store.linearMessages;
  }
  /** chat中的最后一条消息 */
  get latestMessage() {
    return this.linearMessages.at(-1);
  }
  #abortController: AbortController | undefined;
  /**
   * Additional data added on the server via StreamData.
   *
   * This is writable, so you can use it to transform or clear the chat data.
   */
  get data() {
    return this.#store.data;
  }
  set data(value: JSONValue[] | undefined) {
    this.#store.data = value;
  }

  /**
   * Hook status:
   *
   * - `submitted`: The message has been sent to the API and we're awaiting the start of the response stream.
   * - `streaming`: The response is actively streaming in from the API, receiving chunks of data.
   * - `ready`: The full response has been received and processed; a new user message can be submitted.
   * - `error`: An error occurred during the API request, preventing successful completion.
   */
  get status() {
    return this.#store.status;
  }

  /** The error object of the API request */
  get error() {
    return this.#store.error;
  }

  /** The current value of the input. Writable, so it can be bound to form inputs. */
  input = $state<string>()!;

  /** 用户的Message切换操作历史 */
  get switchActions() {
    return this.#store.switchActions;
  }
  set switchActions(value) {
    this.#store.switchActions = value;
  }

  /**
   * Current messages in the chat.
   *
   * This is writable, which is useful when you want to edit the messages on the client, and then
   * trigger {@link reload} to regenerate the AI response.
   */
  get messages(): MessageTreeRoot {
    return this.#store.messages;
  }
  set messages(value: MessageTreeRoot) {
    // ⚠️没有untrack将触发：Svelte error: state_unsafe_mutation Updating state inside a derived or a template expression is forbidden.
    untrack(() => (this.#store.messages = value));
  }

  constructor(options: ChatOptions = {}) {
    // 如果keyedStore已经被其他组件初始化了，则使用外部的全局keyedStore
    this.#keyedStore = hasChatContext() ? getChatContext() : new KeyedChatStore();
    this.#options = options;
    this.messages = options.initialTree ?? initMessageTree();
    this.input = options.initialInput ?? '';
  }

  /**
   * Append a user message to the chat list. This triggers the API call to fetch
   * the assistant's response.
   * @param message The message to append
   * @param options Additional options to pass to the API call
   */
  append = async (
    message: Message | CreateMessage,
    { data, headers, body, attachments }: ChatRequestOpts = {},
  ) => {
    const newUserMessage = {
      ...message,
      id: message.id ?? this.#generateId(),
      createdAt: message.createdAt ?? new Date(),
      experimental_attachments: attachments && attachments.length > 0 ? attachments : undefined,
      parts: getMessageParts(message),
    } as UIMessage;
    insertMessageNode(this.messages, newUserMessage, this.latestMessage?.id ?? null);

    return this.#triggerRequest({ messages: this.linearMessages, headers, body, data });
  };

  /**
   * 重新生成LLM回复。
   * @param options ChatRequestOptions
   * @param reloadId 指定需要重新加载的bot message id。如果不指定reloadId，若linearMessages最后一条消息来自用户，重新生成该用户消息的LLM回复，若linearMessages最后一条消息来自LLM，重新生成该LLM回复。
   */
  reload = async ({ data, headers, body }: ChatRequestOptions = {}, reloadId?: string) => {
    // 还没有聊天记录，reload直接结束，不继续进行逻辑
    if (this.linearMessages.length === 0) {
      return;
    }

    //查不到需要重新加载的bot message，reload直接结束，不继续进行逻辑
    if (reloadId && !this.messages.infoMap.has(reloadId)) {
      return;
    }

    // reload函数的主要作用是构造出正确的Message[]列表，并向#triggerRequest函数传入，这个Message[]确定了最终网络请求的内容。
    // reload不处理任何UI状态变化

    // 三种情况：1.reload为Bot message
    // 2.reload为空，最后一条消息是User Message
    // 3.reload为空，最后一条消息是Bot Message

    let messages: Message[];
    if (reloadId) {
      // 找到需要relaod的消息的index
      const reloadMsgIndex = this.linearMessages.findIndex((m) => m.id === reloadId);
      // 网络请求的消息列表为从开始到reloadId的前一条消息
      messages = $state.snapshot(this.linearMessages.slice(0, reloadMsgIndex));
    } else if (this.latestMessage!.role === 'user') {
      // 网络请求的消息列表为所有消息
      messages = $state.snapshot(this.linearMessages);
    } else if (this.latestMessage!.role === 'assistant') {
      // 网络请求的消息列表为除最后一条BotMessage以外的所有消息
      messages = $state.snapshot(this.linearMessages.slice(0, -1));
      reloadId = this.latestMessage!.id;
    } else {
      throw new Error('reload error');
    }

    await this.#triggerRequest(
      {
        messages,
        headers,
        body,
        data,
      },
      reloadId,
    );
  };

  /**
   * Abort the current request immediately, keep the generated tokens if any.
   * Send unfinished message to server.
   */
  stop = () => {
    try {
      this.#abortController?.abort();
    } catch {
      // ignore
    } finally {
      this.#store.status = 'ready';
      this.#abortController = undefined;
      // 保存中断的消息
      if (this.#options.saveMessageAPI && this.latestMessage?.role === 'assistant') {
        // 不校验未完成的消息是否正确保存。非重要信息
        fetch(this.#options.saveMessageAPI, {
          method: 'POST',
          body: JSON.stringify({
            chatID: this.id,
            preID: this.linearMessages.at(-2)!.id,
            message: this.latestMessage,
          }),
          headers: {
            'Content-Type': 'application/json',
          },
        });
      }
    }
  };

  /** Form submission handler to automatically reset input and append a user message */
  handleSubmit = async (event?: { preventDefault?: () => void }, options: ChatRequestOpts = {}) => {
    event?.preventDefault?.();
    if (!this.input && !options.allowEmptySubmit) return;

    // 新的用户消息
    const newUserMessage = {
      id: this.#generateId(),
      createdAt: new Date(),
      role: 'user',
      content: this.input,
      experimental_attachments:
        options.attachments && options.attachments.length > 0 ? options.attachments : undefined,
      parts: [{ type: 'text', text: this.input }],
    } as UIMessage;

    // 网络请求前，乐观的直接更新UI(更新用户发送的消息)
    insertMessageNode(this.messages, newUserMessage, this.latestMessage?.id ?? null);

    const chatRequest: ChatRequest = {
      messages: this.linearMessages,
      headers: options.headers,
      body: options.body,
      data: options.data,
    };

    const request = this.#triggerRequest(chatRequest);
    this.input = '';
    await request;
  };

  /**
   * 触发网络请求和UI更新
   * @param chatRequest ChatRequest
   * @param reloadId 需要重新生成回复的Message id。
   * @returns
   */
  #triggerRequest = async (chatRequest: ChatRequest, reloadId?: string) => {
    this.#store.status = 'submitted';
    this.#store.error = undefined;

    const messages = chatRequest.messages;
    const messageCount = messages.length;
    const maxStep = extractMaxToolInvocationStep(messages[messages.length - 1]?.toolInvocations);

    try {
      const abortController = new AbortController();
      this.#abortController = abortController;

      const constructedMessagesPayload = this.#options.sendExtraMessageFields
        ? messages
        : messages.map(
            ({
              role,
              content,
              experimental_attachments,
              data,
              annotations,
              toolInvocations,
              parts,
            }) => ({
              role,
              content,
              ...(experimental_attachments !== undefined && {
                experimental_attachments,
              }),
              ...(data !== undefined && { data }),
              ...(annotations !== undefined && { annotations }),
              ...(toolInvocations !== undefined && { toolInvocations }),
              ...(parts !== undefined && { parts }),
            }),
          );

      let lastMsg;
      if (reloadId) {
        const reloadMsgIndex = this.linearMessages.findIndex((m) => m.id === reloadId);
        lastMsg = $state.snapshot(this.linearMessages.at(reloadMsgIndex - 1));
      } else {
        // 没有reloadId的情况：linearMessages的最后一条消息是用户消息（新发送）
        lastMsg = $state.snapshot(this.latestMessage);
      }

      const existingData = this.data ?? [];
      await callChatApi({
        api: this.#api,
        body: {
          id: this.id,
          messages: constructedMessagesPayload,
          data: chatRequest.data,
          ...$state.snapshot(this.#options.body),
          ...chatRequest.body,
        },
        streamProtocol: this.#streamProtocol,
        credentials: this.#options.credentials,
        headers: {
          ...this.#options.headers,
          ...chatRequest.headers,
        },
        abortController: () => abortController,
        restoreMessagesOnFailure: () => {},
        onResponse: this.#options.onResponse,
        onUpdate: ({ message, data }) => {
          this.#store.status = 'streaming';
          //新消息是否已经在messageTree中
          if (isNodeExist(this.messages, message.id)) {
            // 更新对应的node
            updateMessageNode(this.messages, message);
          } else {
            // 插入新节点
            const parentId = lastMsg?.id;
            insertMessageNode(this.messages, message, parentId ?? null);
            if (reloadId) {
              // 如果是重新生产回复，要写入用户操作表，自动跳转到最新的回复
              this.#store.switchActions = insertAction(
                this.#store.switchActions,
                {
                  messageId: parentId ?? null,
                  childIndex: this.messages.infoMap.get(parentId ?? null)!.childCount - 1,
                },
                this.messages.infoMap,
              );
            }
          }

          if (data?.length) {
            this.data = existingData;
            this.data.push(...data);
          }
        },
        onToolCall: this.#options.onToolCall,
        onFinish: this.#options.onFinish,
        generateId: this.#generateId,
        fetch: this.#options.fetch,
        // callChatApi calls structuredClone on the message
        lastMessage: lastMsg,
      });

      this.#abortController = undefined;
      this.#store.status = 'ready';
    } catch (error) {
      if (isAbortError(error)) {
        return;
      }

      const coalescedError = error instanceof Error ? error : new Error(String(error));

      this.#store.status = 'error';
      this.#store.error = coalescedError;

      if (this.#options.onError) {
        this.#options.onError(coalescedError);
      }
    }

    // auto-submit when all tool calls in the last assistant message have results
    // and assistant has not answered yet
    if (
      shouldResubmitMessages({
        originalMaxToolInvocationStep: maxStep,
        originalMessageCount: messageCount,
        maxSteps: this.#maxSteps,
        messages: this.linearMessages,
      })
    ) {
      await this.#triggerRequest({ messages: this.linearMessages });
    }
  };
}
