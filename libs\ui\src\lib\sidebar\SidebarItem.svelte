<script lang="ts">
  import * as DropdownMenu from '$lib/components/ui/dropdown-menu';
  import * as Sidebar from '$lib/components/ui/sidebar';
  import Ellipsis from '@lucide/svelte/icons/ellipsis';
  import Trash2 from '@lucide/svelte/icons/trash-2';
  import Pen from '@lucide/svelte/icons/pen';

  interface Props {
    /** @prop {boolean} isActive - 该item是否被激活。*/
    isActive?: boolean;
    /** @prop {string} content - Chat的内容*/
    content: string;
    /** @prop {string} id - Chat的id。*/
    id: string;
    /** @prop {(id: string) => void} onRename - Chat的重命名回调函数*/
    onRename: (id: string) => void;
    /** @prop {(id: string) => void} onDelete - Chat的删除回调函数*/
    onDelete: (id: string) => void;
    /** @prop {string} itemURL - Item点击后跳转的URL*/
    itemURL: string;
  }
  const { isActive = false, content, id, onDelete, onRename, itemURL }: Props = $props();
</script>

<!--
@component
SidebarItem用于展示侧边栏中LLM与用户的单条对话条目。

- 用法:
``` svelte
<Sidebar.GroupContent>
    <Sidebar.Menu>
      <SidebarItem
        isActive={true}
        content="侧边栏内容"
        id={3}
        onDelete={(id) => console.log('Delete Chat' + id)}
        itemURL='/chat/'
      />
    </Sidebar.Menu>
  </Sidebar.GroupContent>
```
-->

<Sidebar.MenuItem class="cursor-pointer">
  <Sidebar.MenuButton {isActive}>
    {#snippet child({ props })}
      <!-- 默认情况下滚动浏览sidebar会触发后端大量数据预载 -->
      <!-- 禁用鼠标悬停自动触发后端数据预载的机制，以提升服务端性能，减少网络请求 -->
      <!-- 现在数据预载仅在鼠标点击后，松开前的时机触发 -->
      <a data-sveltekit-preload-data="tap" href={itemURL} {...props}>
        <!-- 添加span，防止意外生成过长的标题时，Menu button高度不正确 -->
        <span>{content}</span>
      </a>
    {/snippet}
  </Sidebar.MenuButton>
  <DropdownMenu.Root>
    <DropdownMenu.Trigger>
      {#snippet child({ props })}
        <Sidebar.MenuAction showOnHover={!isActive} {...props}>
          <Ellipsis />
        </Sidebar.MenuAction>
      {/snippet}
    </DropdownMenu.Trigger>
    <DropdownMenu.Content side="right" align="start">
      <DropdownMenu.Item
        onclick={() => onRename(id)}
        class="focus:bg-destructive/15 cursor-pointer"
      >
        <Pen />
        <span>重命名</span>
      </DropdownMenu.Item>
      <DropdownMenu.Item
        onclick={() => onDelete(id)}
        class="text-destructive focus:bg-destructive/15 focus:text-destructive cursor-pointer"
      >
        <Trash2 class="text-red-500" />
        <span class="text-red-500">删除</span>
      </DropdownMenu.Item>
    </DropdownMenu.Content>
  </DropdownMenu.Root>
</Sidebar.MenuItem>
