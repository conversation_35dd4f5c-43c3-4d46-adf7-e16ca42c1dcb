<script lang="ts">
  import BotMessageActions from './BotMessageActions.svelte';
  import { toast } from 'svelte-sonner';
  import * as Avatar from '$lib/components/ui/avatar/index.js';
  import MarkdownRenderer from '$lib/markdown/MarkdownRenderer.svelte';
  import BotReasonMessage from './BotReasonMessage.svelte';
  import { getVoteContext, type MessageAnnotation } from '@askme/lib-common';
  import DislikeDialog from './DislikeDialog.svelte';
  import type { ClassValue } from 'svelte/elements';
  import { cn } from '$lib/utils';
  import type { UIMessage } from 'ai';
  import ExternalSource from './ExternalSource.svelte';

  interface Props {
    /** @prop {UIMessage} message - ChatBot的消息. */
    message: UIMessage;
    /** @prop {string} avatarSrc - ChatBot头像src */
    avatarSrc: string;
    /** @prop {boolean} showActions - ChatBot是否在回复，回复时Action toolbar 自动关闭。 */
    showActions: boolean;
    /** @prop {ClassValue} class - 自定义样式. */
    class?: ClassValue;
    /** @prop {() => void} reload - 重新生成回复的回调函数 */
    reload: () => void;
  }
  const { message, avatarSrc, showActions, class: className, reload }: Props = $props();

  let isReasonTextExpanded = $state(true);
  let isDislikeDialogOpen = $state(false);
  const messageText = $derived(message.parts.find((p) => p.type === 'text')?.text ?? '');
  const isReasoning = $derived(messageText.length === 0 && !showActions);
  const annotations = $derived((message.annotations as unknown as MessageAnnotation[]) ?? []);
  const externalLinks = $derived(annotations.filter((a) => a.type === 'externalLink'));

  const voteMap = getVoteContext();
  let likeStatus: 'noAction' | 'liked' | 'disliked' = $derived.by(() => {
    switch (voteMap.get(message.id)) {
      case undefined:
        return 'noAction';
      case true:
        return 'liked';
      case false:
        return 'disliked';
    }
  });

  function onLike() {
    toast.promise(
      //这里toast会自动捕获http status 4xx和5xx，不需要手动判断
      fetch(`/api/vote/like/${message.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ like: true }),
      }),
      {
        loading: '点赞中',
        success: () => {
          voteMap.set(message.id, true);
          return '已点赞';
        },
        error: () => {
          return '发生错误';
        },
      },
    );
  }
</script>

<!--
  @component
  BotMessage用于显示bot消息。

  - 用法:
    ``` svelte
      <BotMessage
        {avatarSrc}
        {message}
        {showActions}
        {reload}
      />
    ```
-->

<div class={cn('flex w-full gap-4 pb-4', className)}>
  <Avatar.Root>
    <Avatar.Image src={avatarSrc} alt="bot avatar" />
  </Avatar.Root>
  <div class="flex min-w-0 flex-1 flex-col gap-2">
    {#each message.parts as part, index (index)}
      <!-- 渲染深度思考 -->
      {#if part.type === 'reasoning'}
        <BotReasonMessage
          reasoningTitle="深度思考中"
          reasonFinishedTitle="已深度思考"
          isExpanded={isReasonTextExpanded}
          {isReasoning}
          message={part.reasoning}
        />
      {/if}

      <!-- 渲染chatbot消息 -->
      {#if part.type === 'text'}
        <MarkdownRenderer markdown={part.text} />
      {/if}
    {/each}

    <!-- 渲染外部链接 -->
    {#if externalLinks.length > 0}
      <hr />
      <div>相关链接：</div>
      {#each externalLinks as externalLink, index (index)}
        <ExternalSource {externalLink} />
      {/each}
    {/if}

    <!-- 渲染底部工具栏 -->
    {#if showActions}
      <div class="mt-4">
        <BotMessageActions
          messageId={message.id}
          {likeStatus}
          onCopy={() =>
            navigator.clipboard.writeText(messageText).then(() => {
              toast.success('已复制');
            })}
          {onLike}
          onDislike={() => (isDislikeDialogOpen = true)}
          onReload={reload}
        />
      </div>
    {/if}
  </div>
</div>

<DislikeDialog messageId={message.id} bind:isDislikeDialogOpen />
