import { error } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import serverCtx from '$lib/context.server';
import * as LogFormat from '@askme/lib-common/logformat';

// 对某个message投票 “/vote/like/[messageId]”
export const PUT = (async ({ locals, params, request }) => {
  if (!locals.user) {
    console.debug(LogFormat.debug('Unauthorized in /api/vote/like/[messageId] PUT'));
    error(401, 'Unauthorized');
  }

  const jsonBody = await request.json();

  if (typeof jsonBody.like !== 'boolean') {
    error(400, 'Bad Request');
  }

  let message;
  //检查要投票的message是否存在，且是否属于该用户
  try {
    message = await serverCtx.prisma.message.findUnique({
      where: {
        id: params.messageId,
      },
      include: {
        chat: {
          include: {
            creator: true,
          },
        },
      },
    });
    if (!message) {
      error(404, 'Message not found');
    }
    if (message.chat.creator.id !== locals.user.id) {
      error(403, 'Forbidden');
    }
  } catch (e) {
    console.warn(LogFormat.warn('Failed to find message from DB: ' + e));
    error(500, 'Internal Server Error');
  }

  // 检查用户是否已经投过票
  let existingVote;
  try {
    existingVote = await serverCtx.prisma.vote.findUnique({
      where: {
        messageId: params.messageId,
      },
    });
  } catch (e) {
    console.warn(LogFormat.warn('Failed to find vote from DB: ' + e));
    error(500, 'Internal Server Error');
  }

  // 如果投过票，更新投票状态
  if (existingVote) {
    // 投票状态不同，更新投票状态
    if (existingVote.like !== jsonBody.like) {
      try {
        await serverCtx.prisma.vote.update({
          where: {
            messageId: params.messageId,
          },
          data: {
            like: jsonBody.like,
            dislikeReason: jsonBody.like ? '' : jsonBody.reason,
          },
        });
      } catch (e) {
        console.error(LogFormat.error('Failed to update vote: ' + e));
        error(500, 'Internal Server Error');
      }
    }
  } else {
    // 否则创建投票
    try {
      await serverCtx.prisma.vote.create({
        data: {
          messageId: params.messageId,
          like: jsonBody.like,
          dislikeReason: jsonBody.like ? '' : jsonBody.reason,
        },
      });
    } catch (e) {
      console.error(LogFormat.error('Failed to create vote: ' + e));
      error(500, 'Internal Server Error');
    }
    // ----------langfuse埋点------------
    serverCtx.langfuse.score({
      traceId: message.id,
      name: 'vote',
      value: jsonBody.like ? 'like' : 'dislike',
      comment: jsonBody.like ? '' : jsonBody.reason,
    });
    // ----------langfuse埋点结束--------
  }

  return new Response('Message voted', { status: 201 });
}) satisfies RequestHandler;
