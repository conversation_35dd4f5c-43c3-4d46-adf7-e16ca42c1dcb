import { config } from '$lib/config.server';
import type { PageServerLoad } from './$types';
import * as LogFormat from '@askme/lib-common/logformat';
import { redirect } from '@sveltejs/kit';
import { hotReload } from '$lib/hotRelod';

export const load: PageServerLoad = async ({ locals }) => {
  if (locals.user) {
    redirect(302, '/chat');
  }
  let url: string;
  let content: string;

  if (config.oauth.provider.toUpperCase() === 'GITLAB') {
    url = '/login/gitlab';
    content = 'GitLab 登陆';
  } else if (config.oauth.provider.toUpperCase() === 'TONGYUAN') {
    url = '/login/tongyuan';
    content = '同元账号登陆';
  } else if (config.oauth.provider.toUpperCase() === 'KEYCLOAK') {
    url = '/login/keycloak';
    content = 'Keycloak 登陆';
  } else {
    // 在后端启动时zod会校验env.OAUTH_PROVIDER参数，如果错误启动会失败并提示报错
    // 一般来说代码不会执行到这里
    console.error(
      LogFormat.error(
        'Unsupported OAUTH_PROVIDER:' +
          config.oauth.provider +
          ', please check OAUTH_PROVIDER in .env file',
      ),
    );
    process.exit(1);
  }

  return {
    url: url!,
    content: content!,
    title: hotReload.get_askme_config().appTitle,
  };
};
