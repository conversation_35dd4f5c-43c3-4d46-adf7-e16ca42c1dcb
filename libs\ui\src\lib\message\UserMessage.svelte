<script lang="ts">
  import type { ClassValue } from 'svelte/elements';
  import FileCard from '$lib/userInput/FileCard.svelte';
  import type { UIAttachment } from '@askme/lib-common';
  import { cn } from '$lib/utils.js';

  interface Props {
    /** @prop {string} message - 用户的消息. */
    message: string;
    /** @prop {ClassValue} class - 自定义样式. */
    class?: ClassValue;
    /** @prop {Attachment[]} attachments - 用户的消息附件. */
    attachments?: UIAttachment[];
  }
  const { message, class: className, attachments }: Props = $props();
</script>

<!--
@component
UserMessage用于显示用户消息气泡，带有基本样式。

- 用法:
  ``` svelte
  <div class="">
    <UserMessage message="消息内容" className="" attachments={[]} />
  </div>
  ```
-->
<div class={cn('flex flex-col items-end', className)}>
  {#if attachments}
    <div class="bg-background mb-2 flex flex-wrap justify-end gap-2">
      {#each attachments as attachment (attachment.url)}
        <FileCard
          class="w-48"
          uuid={attachment.uuid}
          fileName={attachment.name!}
          fileSize={attachment.fileSize}
          status="uploaded"
        />
      {/each}
    </div>
  {/if}
  {#if message.length > 0}
    <div
      class="bg-primary text-primary-foreground inline-block rounded-lg px-3 py-2 whitespace-pre-wrap"
    >
      {message}
    </div>
  {/if}
</div>
