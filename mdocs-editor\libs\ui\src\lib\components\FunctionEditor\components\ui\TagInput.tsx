import React, { useCallback, useMemo, useState } from "react";
import { But<PERSON> } from "../../../ui/button";
import { Input } from "../../../ui/input";
import { X, Search, Plus } from "lucide-react";
import {
  Popover,
  PopoverContent,
  PopoverTrigger
} from "../../../ui/popover";
import{ cn } from "@/lib/utils";
import { Options } from "../../views/FormView";
import { Tooltip, TooltipContent, TooltipTrigger, TooltipProvider } from "@/lib/components/ui/tooltip";



/**
 * TagInput组件属性接口
 */
export interface TagInputProps {
  /** 已选择的标签值数组 */
  tags: string[];
  /** 可选择的标签列表 */
  availableTags: Options[];
  /** 添加标签回调 */
  onTagAdd: (tagValue: string) => void;
  /** 移除标签回调 */
  onTagRemove: (index: number) => void;
  /**是否必填字段 */
  required?:boolean;
  /** 是否禁用 */
  disabled?: boolean;
  /** 组件标签 */
  label?: string;
  /** 空状态提示文本 */
  placeholder?: string;
  /** 自定义样式类名 */
  className?: string;
}

/**
 * 基于shadcn的TagInput组件
 *
 * 提供标签的添加、删除和选择功能
 */
export const TagInput: React.FC<TagInputProps> = ({
  tags = [],
  availableTags = [],
  onTagAdd,
  onTagRemove,
  disabled = false,
  label = "标签",
  placeholder = "暂无标签",
  className,
  required = false,
}) => {
  const [open, setOpen] = useState(false);
  const [searchValue, setSearchValue] = useState("");

  // 根据标签值获取显示名称
  const getTagDisplayName = useCallback((tagValue: string): string => {
    const tagOption = availableTags.find(tag => tag.value === tagValue);
    return tagOption ? tagOption.label : tagValue.replace('#/i18n/', '');
  }, [availableTags]);

  // 获取可选择的标签列表（包含搜索过滤）
  const availableTagsForSelection = useMemo(() => {
    const filtered = availableTags.filter(tag => !tags.includes(tag.value));
    if (!searchValue) return filtered;

    return filtered.filter(tag =>
      tag.label.toLowerCase().includes(searchValue.toLowerCase()) ||
      tag.description?.toLowerCase().includes(searchValue.toLowerCase())
    );
  }, [availableTags, tags, searchValue]);

  // 添加标签
  const handleSelectTag = useCallback((tagValue: string) => {
    if (!tags.includes(tagValue)) {
      onTagAdd(tagValue);
      // 立即关闭弹窗和清空搜索
      setOpen(false);
      setSearchValue("");
    }
  }, [tags, onTagAdd]);

  // 删除标签
  const handleRemoveTag = useCallback((index: number) => {
    onTagRemove(index);
  }, [onTagRemove]);

  const getTagDiscription = useCallback((tagValue: string): string => {
    const tagOption = availableTags.find(tag => tag.value === tagValue);
    return tagOption ? tagOption.description ?? "" : ""
  }, [availableTags]);

  return (
    <div className={cn("flex items-start gap-2", className)}>
      <span className={cn("text-sm font-medium flex-shrink-0 mt-1",className)}>
        {label}{required && <span className="text-red-500 ml-1">*</span>}
      </span>
      {/* 标签容器 */}
      <div className="flex flex-wrap items-center gap-1 flex-1 min-w-0 min-h-[24px]">
        {/* 已选择的标签 */}
        <TooltipProvider>
          {tags.map((tag, index) => {
            const label = getTagDisplayName(tag);
            const isDeleted = typeof label === 'string' && label.includes('已删除');
            return (
              <Tooltip key={`${tag}-${index}`}>
                <TooltipTrigger asChild>
                  <div
                    className={cn(
                      "inline-flex items-center gap-1 px-2 py-1 rounded-md text-sm font-medium",
                      isDeleted
                        ? "bg-yellow-100 text-yellow-700 border border-yellow-200"
                        : "bg-secondary text-secondary-foreground"
                    )}
                  >
                    <span>{label}</span>
                    <button
                      type="button"
                      onClick={() => handleRemoveTag(index)}
                      disabled={disabled}
                      className="hover:text-destructive disabled:opacity-50"
                      title={`删除: ${label}`}
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </div>
                </TooltipTrigger>
                <TooltipContent>
                  {getTagDiscription(tag)
                    ? <span>{getTagDiscription(tag)}</span>
                    : <span>暂无描述</span>
                  }
                </TooltipContent>
              </Tooltip>
            );
          })}
        </TooltipProvider>

        {/* 添加标签按钮 */}
        {availableTags.length > 0 && availableTagsForSelection.length > 0 && (
          <Popover
            open={open}
            onOpenChange={(newOpen) => {
              setOpen(newOpen);
              // 当关闭时清空搜索
              if (!newOpen) {
                setSearchValue("");
              }
            }}
          >
            <PopoverTrigger asChild>
              <Button
                type="button"
                variant="outline"
                size="sm"
                disabled={disabled || availableTagsForSelection.length === 0}
              >
                <Plus className="w-4 h-4 mr-1" />
                添加
              </Button>
            </PopoverTrigger>
            <PopoverContent align="start" className="w-64 p-0">
              {/* 搜索框 */}
              <div className="p-3 border-b">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
                  <Input
                    placeholder="搜索..."
                    value={searchValue}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => setSearchValue(e.target.value)}
                    className="pl-9 h-8 text-sm"
                  />
                </div>
              </div>

              {/* 标签列表 */}
              <div className="max-h-60 overflow-y-auto">
                {availableTagsForSelection.length > 0 ? (
                  <div className="p-1">
                    {availableTagsForSelection.map((tag) => (
                      <button
                        key={tag.value}
                        onClick={() => handleSelectTag(tag.value)}
                        className="w-full text-left px-3 py-2 text-sm hover:bg-accent rounded-sm"
                      >
                        <div className="flex flex-col items-start w-full">
                          <div className="font-medium">{tag.label}</div>
                          {tag.description && (
                            <div className="text-xs text-muted-foreground mt-0.5">
                              {tag.description}
                            </div>
                          )}
                        </div>
                      </button>
                    ))}
                  </div>
                ) : (
                  <div className="p-4 text-center text-sm text-muted-foreground">
                    {searchValue ? "未找到匹配的标签" : "所有标签已添加"}
                  </div>
                )}
              </div>
            </PopoverContent>
          </Popover>
        )}

        {/* 空状态提示 */}
        {tags.length === 0 && (
          <span className="text-sm text-center text-muted-foreground italic">{placeholder}</span>
        )}
      </div>
    </div>
  );
};
