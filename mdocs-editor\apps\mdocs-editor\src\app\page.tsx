"use client"
import { useState } from "react";
import { useRouter } from "next/navigation";

export default function Home() {
	const [functionName, setFunctionName] = useState("");
	const router = useRouter();

	const handleSubmit = (e: React.FormEvent) => {
		e.preventDefault();
		if (functionName.trim()) {
			// 跳转到编辑页面，传递函数名作为查询参数
			router.push(`/editor-demo?functionName=${encodeURIComponent(functionName.trim())}`);
		}
	};

	return (
		<div className="min-h-screen flex items-center justify-center p-4">
			<div className="max-w-md w-full bg-white rounded-lg shadow-lg p-8">
				<div className="text-center mb-8">
					<h1 className="text-3xl font-bold text-gray-900 mb-2">
						元函数编辑器
					</h1>
				</div>

				<form onSubmit={handleSubmit} className="space-y-6">
					<div>
						<label htmlFor="functionName" className="block text-sm font-medium text-gray-700 mb-2">
							函数名称
						</label>
						<input
							type="text"
							id="functionName"
							value={functionName}
							onChange={(e) => setFunctionName(e.target.value)}
							placeholder="请输入函数名称，如：plot"
							className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
							required
						/>
					</div>

					<button
						type="submit"
						className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors font-medium"
					>
						开始编辑
					</button>
				</form>
			</div>
		</div>
	);
}
