<script lang="ts">
  import { UserInput, ChatPanel, Overview, SuggestedActions } from '@askme/lib-ui';
  import ChatHeader from './ChatHeader.svelte';
  import { Chat } from '@askme/lib-common/chat';
  import { untrack } from 'svelte';
  import { replaceState } from '$app/navigation';
  import { page } from '$app/state';
  import type { ChatModel, MessageTreeRoot, SuggestedActionModel } from '@askme/lib-common';
  import { findMessageNodeById, getChatHistoryContext } from '@askme/lib-common';
  import { DEFAULT_CHAT_TITLE, MAX_FILES, MAX_FILE_SIZE_IN_MB } from '$lib/config';
  import { toast } from 'svelte-sonner';

  interface Props {
    messageTreeRoot: MessageTreeRoot;
    chatId: string | undefined;
    suggestedActions?: SuggestedActionModel[];
    isSSO: boolean;
    enableThemeSwitcher: boolean;
    overviewTitle: string;
  }
  interface MessageStatus {
    isRiskQuery: boolean | null;
  }
  const {
    chatId,
    messageTreeRoot,
    suggestedActions,
    isSSO,
    overviewTitle,
    enableThemeSwitcher,
  }: Props = $props();
  const chatHistory = getChatHistoryContext();

  // ⚠️：解构chat会引发按值复制并且取消响应性
  let useChat = $derived(
    new Chat({
      api: '/api/chat/send_message',
      saveMessageAPI: '/api/message',
      sendExtraMessageFields: true,
      // ⚠️这里不使用context的chatId的原因是：context的chatId是响应式的，并且在新chat id生成后执行后更新，这会导致Chat类重新初始化而刷新组件
      id: chatId,
      // ⚠️标准的方法其实是crypto.randomUUID
      // 但是这里使用bind将this改为crypto，否则会出现TypeError [ERR_INVALID_THIS]: Value of "this" must be of type Crypto
      generateId: crypto.randomUUID.bind(crypto),
      // 不跟踪message的变化
      initialTree: untrack(() => messageTreeRoot),
      onResponse: () => {
        // 检查是否是由用户发起的新对话
        if (
          !useChat.linearMessages.find((m) => m.role === 'assistant') &&
          page.url.pathname === '/chat'
        ) {
          // 新建chat时，后端开始响应，前端立刻切换路由
          // ⚠️由于sveltekit在前端url变化后，强制自动执行后端url匹配的加载函数
          // ⚠️若在其他阶段执行replaceState，将导致/chat/chatID的加载函数和/api/chat端点的POST请求处理函数并行执行，对数据库中的chat和message表产生竞态条件
          // ⚠️在onResponse中执行replaceState，可以保证加载函数在/api/chat端点的chat和message数据库表初始化之后执行
          replaceState(`/chat/${useChat.id}`, {});
          //生成临时标题
          chatHistory.push({
            id: useChat.id,
            createdAt: new Date(),
            title: DEFAULT_CHAT_TITLE,
          } as ChatModel);
        }
      },
      onFinish: async () => {
        const isRiskQuery = useChat.data
          ? (useChat.data.at(-1) as unknown as MessageStatus).isRiskQuery
          : false;
        if (isRiskQuery) {
          const lastUserMessageId = useChat.linearMessages.at(-2)?.id;
          const userMessageNode = findMessageNodeById(lastUserMessageId!, useChat.messages);
          // 发现用户查询是风险查询，直接将用户消息内容清空
          userMessageNode!.parts.filter((p) => p.type === 'text')[0].text = ' ';
          // 避免大模型根据用户的风险查询生成标题，直接返回
          return;
        }
        // 检查是否存在对话总结标题
        const oldChat = chatHistory.find((c) => c.id === useChat.id)!;
        if (oldChat.title === DEFAULT_CHAT_TITLE) {
          const response = await fetch('/api/summary', {
            method: 'POST',
            body: JSON.stringify({
              chatID: useChat.id,
            }),
            headers: {
              'Content-Type': 'application/json',
            },
          });
          const result = await response.json();
          // 前端更换标题
          if (response.ok) {
            // 前端sidebar更换标题;
            oldChat.title = result.text;
          } else {
            toast.error('对话标题生成失败');
          }
        }
      },
      onError: () => {
        toast.error('消息生成失败');
        useChat.stop();
      },
    }),
  );
</script>

<div class="bg-background flex h-dvh flex-col">
  <ChatHeader {isSSO} {enableThemeSwitcher} />
  {#if useChat.linearMessages.length > 0}
    <!-- ⚠️：这个flex是必须的，否则会将UserInput顶出视口 -->
    <!-- 如果去掉 flex，内容区变成普通块级元素（默认 block），高度取决于内容（即消息列表多少内容） -->
    <ChatPanel chatClient={useChat} class="flex min-h-0 flex-1" />
  {:else}
    <div class="mx-auto max-w-3xl flex-1 md:mt-20">
      <Overview title={`欢迎使用${overviewTitle}！`} />
    </div>
    <div class="mx-[max(10vw,32px)] mb-4 grid gap-2 sm:grid-cols-2">
      {#if suggestedActions && suggestedActions.length > 0}
        <SuggestedActions chatClient={useChat} {suggestedActions} />
      {/if}
    </div>
  {/if}

  <div class="mx-[max(10vw,32px)]">
    <UserInput bind:chat={useChat} {MAX_FILES} {MAX_FILE_SIZE_IN_MB} />
  </div>

  <span class="text-center text-xs text-gray-500">内容由 AI 生成，请仔细甄别</span>
</div>
