generator client {
  provider        = "prisma-client"
  previewFeatures = ["driverAdapters"]
  output          = "../src/lib/generated/prisma" // Prisma 6.6不再默认导出到node modules, 未填写output将出现警告
  moduleFormat    = "esm"
}

datasource db {
  provider = "postgresql"
  url      = env("DB_URL")
}

model User {
  id       Int       @id @default(autoincrement())
  UID      String    @unique //关联其他账号系统id
  email    String?
  username String
  provider Provider
  chats    Chat[]
  sessions Session[]
}

model Chat {
  id        String    @id @default(uuid()) @db.Uuid
  createdAt DateTime  @default(now())
  title     String
  creator   User      @relation(fields: [creatorId], references: [id])
  creatorId Int
  messages  Message[]
  deletedAt DateTime? //软删除字段
}

model Message {
  id          String   @id @default(uuid()) @db.Uuid
  preId       String?  @db.Uuid // 该消息的直接前驱message id，若第一条用户消息，则为空
  createdAt   DateTime @default(now())
  role        Role
  parts       Json //只能保存JSON，不再保存纯字符串
  attachments Json? // 使用JSON保存该条消息关联的附件信息（显示，获取该附件相关的所有信息）
  annotations Json? // 使用JSON保存消息的自定义其他数据
  chat        Chat     @relation(fields: [chatId], references: [id])
  chatId      String   @db.Uuid
  vote        Vote?
}

model Vote {
  id            Int     @id @default(autoincrement())
  like          Boolean
  dislikeReason String?
  message       Message @relation(fields: [messageId], references: [id])
  messageId     String  @unique @db.Uuid
}

enum Role {
  user
  assistant
}

enum Provider {
  GITLAB
  TONGYUAN
  KEYCLOAK
}

model Session {
  id                 String    @id
  userId             Int
  expiresAt          DateTime
  accessToken        String
  refreshToken       String?
  oauthExpiresAt     DateTime?
  user               User      @relation(references: [id], fields: [userId], onDelete: Cascade)
  useCookieUserToken Boolean
}
