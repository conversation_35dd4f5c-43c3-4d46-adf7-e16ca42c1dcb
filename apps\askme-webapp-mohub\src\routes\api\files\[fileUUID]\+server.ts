import type { RequestHand<PERSON> } from './$types';
import { error } from '@sveltejs/kit';
import * as LogFormat from '@askme/lib-common/logformat';
import serverCtx from '$lib/context.server';
import { config } from '$lib/config.server';
import { buildFullPath } from '$lib/s3utils';

export const GET = (async ({ locals, url, params }) => {
  if (!locals.user) {
    console.debug(LogFormat.debug('Unauthorized in /api/files/[fileUUID] GET'));
    error(401, 'Unauthorized');
  }
  const userId = locals.user.id;
  const fileUUID = params.fileUUID;
  const originalExt = url.searchParams.get('ext')?.toLowerCase();
  const fileKey = buildFullPath([userId, `${fileUUID}.${originalExt}`], config.s3.prefix);

  try {
    const stat = await serverCtx.s3.statObject(config.s3.bucket, fileKey);
    const fileStream = await serverCtx.s3.getObject(config.s3.bucket, fileKey);
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    return new Response(fileStream as any, {
      headers: {
        'Content-Type': stat.metaData?.['content-type'] || 'application/octet-stream',
        'Content-Disposition': `attachment; filename="${fileKey}"`,
      },
    });
  } catch {
    error(404, 'File not found');
  }
}) satisfies RequestHandler;
