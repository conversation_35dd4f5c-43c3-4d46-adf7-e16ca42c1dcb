import { z } from 'zod/v4';
import { env } from '$env/dynamic/private';

export const ThemeConfigSchema = z
  .object({
    default_theme: z.enum(['ORANGE', 'DARK', 'LIGHT']),
    enable_theme_switcher: z.boolean(),
  })
  .readonly();

export const ContentModerationConfigSchema = z
  .object({
    provider: z.enum(['ALIYUN']),
    baseurl: z.url(),
    access_key_id: z.string().min(1),
    access_key_secret: z.string().min(1),
  })
  .readonly();

export const LogConfigSchema = z
  .object({
    level: z.enum(['trace', 'debug', 'info', 'warn', 'error', 'fatal']),
  })
  .readonly();

export const CommonConfigSchema = z
  .object({
    listen_host: z.string().min(1),
    listen_port: z.number().int().positive(),
    deployment_target: z.enum(['PUBLIC', 'PRIVATE']),
    sso_cookie_name: z.string(),
    sso_enabled: z.boolean(),
  })
  .readonly();

export const LLMConfigSchema = z
  .object({
    llm_store_api_baseurl: z.url(),
    openai_api_baseurl: z.url(),
  })
  .readonly();

export const OAuthConfigSchema = z
  .object({
    provider: z.enum(['GITLAB', 'TONGYUAN', 'KEYCLOAK']),
    baseurl: z.url().refine((url) => !url.endsWith('/'), 'URL must not end with a trailing slash'),
    authorization_url: z
      .url()
      .refine((url) => !url.endsWith('/'), 'URL must not end with a trailing slash'),
    token_url: z
      .url()
      .refine((url) => !url.endsWith('/'), 'URL must not end with a trailing slash'),
    userinfo_url: z
      .url()
      .refine((url) => !url.endsWith('/'), 'URL must not end with a trailing slash'),
    redirect_uri: z
      .url()
      .refine((url) => !url.endsWith('/'), 'URL must not end with a trailing slash'),
    application_secret: z.string().min(1),
    application_id: z.string().min(1),
    scopes: z.array(z.string().min(1)).default(['openid', 'email']),
  })
  .readonly();

export const S3ConfigSchema = z
  .object({
    provider: z.enum(['MINIO', 'HUAWEI_OBS']),
    bucket: z.string().min(1),
    prefix: z.string(),
    hostname: z.string(),
    port: z.number().int().positive(),
    ssl: z.boolean(),
    access_key: z.string().min(1),
    secret_key: z.string().min(1),
    isPathStyle: z.boolean(),
  })
  .readonly();

export const DbConfigSchema = z
  .object({
    provider: z.literal('POSTGRES'),
    url: z.url(),
  })
  .readonly();

export const LangfuseConfigSchema = z
  .object({
    baseurl: z.url(),
    access_token: z.string().min(1),
    secret_token: z.string().min(1),
  })
  .readonly();

export const ServerConfigSchema = z
  .object({
    common: CommonConfigSchema,
    oauth: OAuthConfigSchema,
    s3: S3ConfigSchema,
    db: DbConfigSchema,
    langfuse: LangfuseConfigSchema,
    llm: LLMConfigSchema,
    log: LogConfigSchema,
    // 内容安全未开启该字段为undefined
    content_moderation: ContentModerationConfigSchema.optional(),
    theme: ThemeConfigSchema,
  })
  .readonly();

export type ServerConfig = z.infer<typeof ServerConfigSchema>;
export type CommonConfig = z.infer<typeof CommonConfigSchema>;
export type OAuthConfig = z.infer<typeof OAuthConfigSchema>;
export type S3Config = z.infer<typeof S3ConfigSchema>;
export type DbConfig = z.infer<typeof DbConfigSchema>;
export type LangfuseConfig = z.infer<typeof LangfuseConfigSchema>;
export type LogConfig = z.infer<typeof LogConfigSchema>;
export type LLMConfig = z.infer<typeof LLMConfigSchema>;
export type ContentModerationConfig = z.infer<typeof ContentModerationConfigSchema>;
export type ThemeConfig = z.infer<typeof ThemeConfigSchema>;

/**
 *  配置对象构建函数
 *  归一化所有配置，保证服务器初始配置的正确性
 *  抛出异常将终止进程，此时应当重新检查填写配置
 *  ⚠️该函数只保证配置字段的正确性，不保证配置的实际可用性
 *  ⚠️你需要其他手段检查配置是否实际可用（比如手动检查服务健康）
 * @returns ServerConfig
 */
function buildServerConfig(): ServerConfig {
  try {
    // 提示：在schema.parse中完成字段校验和默认值设置
    // 若parse失败，抛出异常

    let themeConfig;
    try {
      themeConfig = ThemeConfigSchema.parse({
        default_theme: validTheme(env.DEFAULT_THEME),
        enable_theme_switcher: env.ENABLE_THEME_SWITCHER === 'true' ? true : false,
      });
    } catch (error) {
      throw new Error('PLEASE CHECK THEME CONFIG in .env FILE!\n' + error);
    }

    let contentModerationConfig;
    const contentModerationProvider = validContentModerationProvider(
      env.CONTENT_MODERATION_PROVIDER,
    );
    if (contentModerationProvider) {
      try {
        contentModerationConfig = ContentModerationConfigSchema.parse({
          provider: contentModerationProvider,
          baseurl: normalizeUrl(env.CONTENT_MODERATION_BASE_URL),
          access_key_id: env.CONTENT_MODERATION_ACCESS_KEY_ID,
          access_key_secret: env.CONTENT_MODERATION_ACCESS_KEY_SECRET,
        });
      } catch (error) {
        throw new Error(
          'PLEASE CHECK ALIYUN CONTENT MODERATION PLUS CONFIG in .env FILE!\n' + error,
        );
      }
    }

    let commonConfig;
    try {
      commonConfig = CommonConfigSchema.parse({
        listen_host: env.HOST ?? env.LISTEN_HOST ?? '127.0.0.1',
        listen_port: parseInt(env.PORT ?? env.LISTEN_PORT ?? '5000', 10),
        deployment_target: env.DEPLOYMENT_TARGET ? env.DEPLOYMENT_TARGET.toUpperCase() : 'PUBLIC',
        sso_cookie_name: env.SSO_COOKIE_NAME ? env.SSO_COOKIE_NAME : '',
        sso_enabled: env.SSO_COOKIE_NAME ? true : false,
      });
    } catch (error) {
      throw new Error('PLEASE CHECK COMMON CONFIG in .env FILE!\n' + error);
    }

    let logConfig;
    try {
      logConfig = LogConfigSchema.parse({
        level: env.LOG_LEVEL ?? 'info',
      });
    } catch (error) {
      throw new Error('PLEASE CHECK LOG CONFIG in .env FILE!\n' + error);
    }

    let llmConfig;
    try {
      llmConfig = LLMConfigSchema.parse({
        llm_store_api_baseurl: normalizeUrl(env.LLM_STORE_API_BASE_URL),
        openai_api_baseurl:
          normalizeUrl(env.OPENAI_API_BASE_URL) ?? normalizeUrl(env.LLM_STORE_API_BASE_URL),
      });
    } catch (error) {
      throw new Error('PLEASE CHECK LLM CONFIG in .env FILE!\n' + error);
    }

    let oauthConfig;
    try {
      oauthConfig = OAuthConfigSchema.parse({
        provider: env.OAUTH_PROVIDER.toUpperCase(),
        baseurl: normalizeUrl(env.OAUTH_BASE_URL),
        authorization_url:
          env.OAUTH_AUTHORIZATION_URL ??
          buildOAuthAuthorizationUrl(env.OAUTH_PROVIDER, env.OAUTH_BASE_URL),
        token_url:
          env.OAUTH_TOKEN_URL ?? buildOAuthTokenUrl(env.OAUTH_PROVIDER, env.OAUTH_BASE_URL),
        userinfo_url:
          env.OAUTH_USERINFO_URL ?? buildOAuthUserInfoUrl(env.OAUTH_PROVIDER, env.OAUTH_BASE_URL),
        redirect_uri: env.OAUTH_REDIRECT_URI,
        application_secret: env.OAUTH_APPLICATION_SECRET,
        application_id: env.OAUTH_APPLICATION_ID,
        scopes: env.OAUTH_SCOPES?.split(' ') ?? ['openid', 'email'],
      });
    } catch (error) {
      throw new Error('PLEASE CHECK OAUTH CONFIG in .env FILE!\n' + error);
    }

    let s3Config;
    try {
      const s3URL = parseS3URL(env.S3_BASE_URL);
      s3Config = S3ConfigSchema.parse({
        provider:
          env.S3_PROVIDER ?? (commonConfig.deployment_target == 'PUBLIC' ? 'HUAWEI_OBS' : 'MINIO'),
        bucket: env.S3_BUCKET_NAME,
        prefix: normalizeS3Prefix(env.S3_BUCKET_PREFIX ?? ''),
        hostname: s3URL.hostname,
        port: s3URL.port,
        ssl: s3URL.ssl,
        access_key: env.S3_ACCESS_KEY,
        secret_key: env.S3_SECRET_KEY,
        isPathStyle: getPathStyleByS3Provider(env.S3_PROVIDER),
      });
    } catch (error) {
      throw new Error('PLEASE CHECK S3 CONFIG in .env FILE!\n' + error);
    }

    let dbConfig;
    try {
      dbConfig = DbConfigSchema.parse({
        provider: env.DB_PROVIDER ?? 'POSTGRES',
        url: normalizeUrl(env.DB_URL),
      });
    } catch (error) {
      throw new Error('PLEASE CHECK DB CONFIG in .env FILE!\n' + error);
    }

    let langfuseConfig;
    try {
      langfuseConfig = LangfuseConfigSchema.parse({
        baseurl: normalizeUrl(env.LANGFUSE_BASEURL),
        access_token: env.LANGFUSE_PUBLIC_KEY,
        secret_token: env.LANGFUSE_SECRET_KEY,
      });
    } catch (error) {
      throw new Error('PLEASE CHECK LANGFUSE CONFIG in .env FILE!\n' + error);
    }

    const config = {
      common: commonConfig,
      log: logConfig,
      oauth: oauthConfig,
      s3: s3Config,
      db: dbConfig,
      langfuse: langfuseConfig,
      llm: llmConfig,
      content_moderation: contentModerationConfig,
      theme: themeConfig,
    };

    return ServerConfigSchema.parse(config);
  } catch (error) {
    console.error(error);
    process.exit(1);
  }
}

/** remove trailing / */
function normalizeUrl(url?: string) {
  if (!url) return url;
  return url.endsWith('/') ? url.slice(0, -1) : url;
}

/** Normalize S3 prefix by removing leading and trailing slashes */
function normalizeS3Prefix(prefix?: string): string {
  if (!prefix) return '';
  return prefix.trim().replace(/^\/+|\/+$/g, '');
}

/** parse s3 config */
function parseS3URL(baseurl: string) {
  const url = new URL(baseurl);
  if (url.protocol === 'http:') {
    return {
      hostname: url.hostname,
      port: parseInt(url.port || '80', 10),
      ssl: false,
    };
  } else if (url.protocol === 'https:') {
    return {
      hostname: url.hostname,
      port: parseInt(url.port || '443', 10),
      ssl: true,
    };
  } else {
    throw new Error(`unknown S3 url protocol: ${url.protocol}`);
  }
}

function buildOAuthUserInfoUrl(provider: string, baseUrl: string) {
  if (provider.toUpperCase() === 'GITLAB') {
    return `${baseUrl}/oauth/userinfo`;
  } else if (provider?.toUpperCase() === 'TONGYUAN') {
    return `${baseUrl}/authentication/userinfo`;
  } else if (provider?.toUpperCase() === 'KEYCLOAK') {
    return `${baseUrl}/protocol/openid-connect/userinfo`;
  } else {
    throw new Error(`Unsupported OAuth provider: ${provider}`);
  }
}

function buildOAuthAuthorizationUrl(provider: string, baseUrl: string) {
  if (provider.toUpperCase() === 'GITLAB') {
    return `${baseUrl}/oauth/authorize`;
  } else if (provider?.toUpperCase() === 'TONGYUAN') {
    return `${baseUrl}/authentication/oauth2/authorize`;
  } else if (provider?.toUpperCase() === 'KEYCLOAK') {
    return `${baseUrl}/protocol/openid-connect/auth`;
  } else {
    throw new Error(`Unsupported OAuth provider: ${provider}`);
  }
}

function buildOAuthTokenUrl(provider: string, baseUrl: string) {
  if (provider.toUpperCase() === 'GITLAB') {
    return `${baseUrl}/oauth/token`;
  } else if (provider?.toUpperCase() === 'TONGYUAN') {
    return `${baseUrl}/authentication/oauth2/token`;
  } else if (provider?.toUpperCase() === 'KEYCLOAK') {
    return `${baseUrl}/protocol/openid-connect/token`;
  } else {
    throw new Error(`Unsupported OAuth provider: ${provider}`);
  }
}

function validContentModerationProvider(provider: string | undefined): string | undefined {
  if (!provider) {
    return undefined;
  }
  if (provider.toUpperCase() === 'ALIYUN') {
    return 'ALIYUN';
  } else {
    return undefined;
  }
}

function validTheme(theme: string | undefined): string | undefined {
  if (!theme) {
    return 'ORANGE';
  }
  if (theme.toUpperCase() === 'ORANGE') {
    return 'ORANGE';
  } else if (theme.toUpperCase() === 'DARK') {
    return 'DARK';
  } else if (theme.toUpperCase() === 'LIGHT') {
    return 'LIGHT';
  } else {
    return 'ORANGE';
  }
}

function getPathStyleByS3Provider(provider: string) {
  switch (provider) {
    case 'MINIO':
      return true;
    case 'HUAWEI_OBS':
      return false;
    default:
      throw new Error('Unsupported S3 provider: ' + provider);
  }
}

/** Server Deployment Config */
export const config = buildServerConfig();
