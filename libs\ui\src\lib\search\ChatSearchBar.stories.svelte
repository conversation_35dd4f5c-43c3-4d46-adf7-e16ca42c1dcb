<script module lang="ts">
  //    👆 notice the module context, defineMeta does not work in a regular <script> tag - instance
  import { defineMeta } from '@storybook/addon-svelte-csf';
  import ChatSearchBar from './ChatSearchBar.svelte';

  import type { ChatSearchResult } from '@askme/lib-common';
  let count = 1;

  //      👇 Get the Story component from the return value
  const { Story } = defineMeta({
    title: 'UI/ChatSearchBar',
    component: ChatSearchBar,
    decorators: [
      /* ... */
    ],
    parameters: {
      mockData: [
        {
          url: '/api/search?content=&page=',
          method: 'GET',
          status: 200,
          response: () => {
            return generateChatSearchResults(count);
          },
        },
      ],
    },
  });

  // 20% 的几率生成 0 个结果 （模拟搜索不存在）
  // 20% 的几率生成 1-9 个结果（模拟搜索到最后一页）
  // 60% 的几率生成 10 个结果（模拟正常搜索）
  function generateChatSearchResults(startId: number): ChatSearchResult[] {
    const results: ChatSearchResult[] = [];
    const rand = Math.random();
    let numResults: number;
    if (rand < 0.2) {
      numResults = 0;
    } else if (rand < 0.4) {
      numResults = Math.floor(Math.random() * 9) + 1;
    } else {
      numResults = 10;
    }

    for (let i = 0; i < numResults; i++) {
      const id = (startId + i).toString();
      results.push({
        title: `Search Result ${id}`,
        detail: `Detail for search result ${id}`,
        dateStr: new Date(
          Date.now() - Math.floor(Math.random() * 30) * 24 * 60 * 60 * 1000,
        ).toISOString(),
        chatId: id,
      });
    }
    count += numResults;
    return results;
  }
</script>

<Story
  name="Default"
  args={{
    isOpen: true,
  }}
/>
