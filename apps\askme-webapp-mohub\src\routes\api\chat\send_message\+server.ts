import { streamText, appendResponseMessages, createDataStreamResponse } from 'ai';
import type { UIMessage, JSONValue } from 'ai';
import { smoothStream } from '@askme/lib-common';
import type { UserPreferancesCookies, ExternalLinkPart } from '@askme/lib-common';
import serverCtx from '$lib/context.server';
import type { RequestS3Config } from '$lib/types';
import * as LogFormat from '@askme/lib-common/logformat';
import { error } from '@sveltejs/kit';
import {
  CHUNK_TIMEOUT_IN_MS,
  SMOOTH_DELAY_IN_MS,
  SYSTEM_PROMPT,
  DEFAULT_CHAT_TEMPERATURE,
} from '$lib/config';
import { config } from '$lib/config.server';
import { getOpenAIforUser, getRiskResponseModel } from '$lib/models';
import {
  createChatIfNotExists,
  createMessageIfNotExists,
  buildLLMStoreRAGFormData,
  fetchLLMStoreRAGResult,
  buildRagPromptFromRagResult,
  buildSourceLinkPartsFromRagResult,
  fillAttachmentByDeploymentTarget,
  fetchContentModerationResult,
} from '$lib/serverUtils';
import { getSelectedModel } from '$lib/utils';
import type { RequestHandler } from '@sveltejs/kit';

export const POST = (async ({ request, locals, cookies }) => {
  if (!locals.user) {
    console.debug(LogFormat.debug('Unauthorized in /api/chat/send_message POST'));
    error(401, 'Unauthorized');
  }

  const userPreferances = JSON.parse(cookies.get('userPreferances')!) as UserPreferancesCookies;

  // 待处理的消息
  const {
    id,
    messages,
  }: {
    id: string;
    messages: Array<UIMessage>;
  } = await request.json();

  //  --- 处理用户消息为空的意外情况 ---
  const lastUserMessage = messages.findLast((m) => m.role === 'user');
  if (!lastUserMessage) {
    return new Response('No user message found', { status: 400 });
  }
  const lastUserMessageTextPart = lastUserMessage.parts.find((p) => p.type === 'text');
  if (!lastUserMessageTextPart) {
    return new Response('No user message text found', { status: 400 });
  }
  const lastUserMessageTextStr = lastUserMessageTextPart.text;
  if (
    lastUserMessageTextStr.length === 0 &&
    (!lastUserMessage.experimental_attachments ||
      lastUserMessage.experimental_attachments.length === 0)
  ) {
    return new Response('User message string not found', { status: 400 });
  }
  //  --- 处理用户消息为空的意外情况结束 ---

  // ----------------- 阿里用户llm查询内容审核 -----------------

  let isRiskQuery: boolean | null = null;
  let riskQueryAnswer = '';
  if (config.content_moderation) {
    const checkResult = await fetchContentModerationResult(lastUserMessageTextStr);
    if (checkResult) {
      isRiskQuery = checkResult.isRiskQuery;
      riskQueryAnswer = checkResult.riskQueryAnswer;
    }
  }
  // ----------------- 阿里用户llm查询内容审核结束 -----------------

  // 根据用户偏好cookie数据和用户消息内容选择模型
  const selectedModel = getSelectedModel(userPreferances.isReasoningEnabled, lastUserMessage);

  // 移除掉chatbot回复消息中非text部分（推理部分）
  messages.forEach((m) => {
    if (m.role === 'assistant') {
      m.parts = m.parts.filter((p) => p.type === 'text');
    }
  });

  // 检查chat id是否存在，如果不存在则创建
  await createChatIfNotExists(serverCtx.prisma, id, locals.user.id, '新对话');

  const lastBotMessage = messages.findLast((m) => m.role === 'assistant');

  if (isRiskQuery) {
    // 不保存用户的危险言论，以免查询出来造成其他风险
    lastUserMessageTextPart.text = ' ';
  }
  // 用户消息写入数据库
  await createMessageIfNotExists(
    serverCtx.prisma,
    lastUserMessage,
    lastBotMessage ? lastBotMessage.id : undefined,
    id,
    'user',
  );

  // ----------langfuse埋点设置（初始化trace）------------
  const trace = serverCtx.langfuse.trace({
    sessionId: id, // chat id
    id: lastUserMessage.id,
    name: 'message-' + lastUserMessage.id,
    // ⚠️locals.user.id是number类型，需要转为string
    // 如果不转为string，整个trace都不会被正确记录信息
    userId: locals.user.id.toString(),
    input: lastUserMessage,
    tags: ['common-chat'],
  });

  // ----------langfuse埋点设置（RAG时间计算）------------
  const ragSpan = trace.span({
    id: 'rag-' + lastUserMessage.id,
    name: 'rag-' + lastUserMessage.id,
    metadata: {
      httpRoute: `${config.llm.llm_store_api_baseurl}/mds/search`,
    },
  });
  // ----------langfuse埋点结束-------------------------

  // TODO：对于大模型厂商不支持的文件输入格式。提取文字写入消息里的提示词，并移除相应的attachment字段，开始rag流程

  const s3Config: RequestS3Config = {
    minioClient: serverCtx.s3,
    userId: locals.user.id,
    bucketName: config.s3.bucket,
    prefix: config.s3.prefix,
  };

  // --- RAG开始 ----
  let ragPropmt = '';
  let ragResult;
  let sourceLinkArray: ExternalLinkPart[] = [];
  try {
    // RAG CHUNK数量
    const TOP_K = 20;
    const formData = await buildLLMStoreRAGFormData(
      s3Config,
      lastUserMessageTextStr,
      'mdocs_public',
      TOP_K,
      lastUserMessage.experimental_attachments,
    );
    let ragResult = await fetchLLMStoreRAGResult(
      config.llm.llm_store_api_baseurl,
      locals.session.accessToken,
      formData,
    );
    const SCORE_THRESHOLD = 0.5;
    ragResult = ragResult.filter((item) => item.score >= SCORE_THRESHOLD);
    ragPropmt = buildRagPromptFromRagResult(ragResult);

    const isPublicDeployment = config.common.deployment_target === 'PUBLIC';
    // 溯源链接数量
    const REFERENCE_SOURSE_NUMBER = 3;
    sourceLinkArray = buildSourceLinkPartsFromRagResult(
      ragResult,
      isPublicDeployment,
      REFERENCE_SOURSE_NUMBER,
    );
  } catch (error) {
    console.error(LogFormat.error('RAG search error:' + error));
  }
  // ---- 结束RAG ------

  // ----------langfuse埋点设置（RAG时间计算）------------
  ragSpan.end({ output: ragResult });
  // ----------langfuse埋点结束-------------------------

  // ----------开始根据部署环境处理附件----------
  const requestMessage: UIMessage[] = [];
  for (const m of messages) {
    if (selectedModel === 'MULTI_MODAL_MODEL') {
      if (m.experimental_attachments) {
        m.experimental_attachments = await fillAttachmentByDeploymentTarget(
          s3Config,
          config.common.deployment_target,
          m.experimental_attachments,
        );
      }
    } else {
      m.experimental_attachments = [];
    }
    requestMessage.push(m);
  }

  //----------处理附件结束----------

  const provider = getOpenAIforUser(locals.session.accessToken);

  // --- Chunk 超时设置 ---
  // 收到chunk后，会调用resetTimer函数，重置计时器
  // 如果超过指定时间没有收到chunk，则会调用abort函数，终止请求
  const chunkTimeoutAbortController = new AbortController();
  let chunkTimerId: NodeJS.Timeout | null = null;

  const resetTimer = () => {
    if (chunkTimerId) {
      clearTimeout(chunkTimerId);
    }
    chunkTimerId = setTimeout(() => {
      chunkTimeoutAbortController.abort();
    }, CHUNK_TIMEOUT_IN_MS);
  };
  // --- 结束 Chunk 超时设置 ---

  // ----------langfuse埋点设置（api请求前）------------
  const generation = trace.generation({
    id: lastUserMessage.id,
    name: 'chat-completion-' + lastUserMessage.id,
    model: selectedModel,
    modelParameters: {
      temperature: DEFAULT_CHAT_TEMPERATURE,
    },
    input: messages,
    metadata: {
      model: selectedModel,
      httpRoute: '/api/chat/send_message',
    },
  });

  // ----------langfuse埋点设置结束--------

  let firstCharReceived = false;
  return createDataStreamResponse({
    execute: (dataStream) => {
      // 为第一个chunk启动初始计时器
      resetTimer();
      const result = streamText({
        model: isRiskQuery
          ? getRiskResponseModel(riskQueryAnswer)
          : provider.languageModel(selectedModel),
        system: SYSTEM_PROMPT + '\n' + ragPropmt,
        messages: requestMessage,
        // ⚠️：最好设置大于等于0.4的temperature,保证重新获取差异性。否则前端重新加载按钮几乎失效。
        temperature: DEFAULT_CHAT_TEMPERATURE,
        // ai sdk的整流（平滑输出）没有考虑CJK文字，因此使用正则匹配单个字符
        // ⚠️：正则里匹配同时带有文字和标点的chunk会导致无法区分推理文本结束和大模型输出开始
        experimental_transform: smoothStream({
          chunking: /[\u4E00-\u9FFF]|[^\u4E00-\u9FFF\s]|\s/,
          delayInMs: SMOOTH_DELAY_IN_MS,
        }),
        experimental_generateMessageId: crypto.randomUUID.bind(crypto),
        onChunk: () => {
          if (!firstCharReceived) {
            firstCharReceived = true;
            generation.update({
              completionStartTime: new Date(),
            });
          }
          // 收到 chunk 时重置计时器
          resetTimer();
        },
        onError: async ({ error }) => {
          // ----------langfuse埋点(记录生成错误的LLM消息)------------
          serverCtx.langfuse.score({
            traceId: lastUserMessage.id,
            name: 'llm-output',
            value: 'failed',
            comment: 'Failed reason:' + error,
          });
          // ----------langfuse埋点结束-------------------------
          // 出错时清理计时器
          if (chunkTimerId) {
            clearTimeout(chunkTimerId);
          }
          console.error(LogFormat.error('StreamText error:' + error));
        },
        abortSignal: chunkTimeoutAbortController.signal,
        onFinish: async ({ response, usage }) => {
          // 检查是否正确生成LLM消息
          const assisstMessage = response.messages.findLast((m) => m.role === 'assistant');

          // 没有生成LLM消息
          if (!assisstMessage) {
            // ----------langfuse埋点(记录生成错误的LLM消息)------------
            serverCtx.langfuse.score({
              traceId: lastUserMessage.id,
              name: 'llm-output',
              value: 'failed',
              comment: 'Failed reason: No assistant message generated',
            });
            // ----------langfuse埋点结束-------------------------
            console.error(LogFormat.error('Failed to generate assistant message'));
            return;
          }
          // ----------langfuse埋点(记录正常生成LLM消息)------------
          serverCtx.langfuse.score({
            traceId: lastUserMessage.id,
            name: 'llm-output',
            value: 'success',
          });
          // ----------langfuse埋点结束-------------------------

          // 利用了appendResponseMessages把ResponseMessage转为UIMessage
          const [, assistantMessage] = appendResponseMessages({
            messages: [lastUserMessage],
            responseMessages: response.messages,
          });

          // 检查数据库里有没有这个消息
          try {
            const messageInDB = await serverCtx.prisma.message.findFirst({
              where: {
                id: assistantMessage.id,
              },
            });
            //若存在此消息结束流程
            if (messageInDB) {
              return;
            }
          } catch (error) {
            console.error(LogFormat.error('Failed to find assistant message:' + error));
            return;
          }

          // ----------langfuse埋点(api请求后写入结果)------------
          trace.update({
            output: assistantMessage.parts,
          });
          generation.end({
            output: assistantMessage.parts,
            usage: {
              promptTokens: usage.promptTokens,
              totalTokens: usage.totalTokens,
              completionTokens: usage.completionTokens,
            },
          });
          // -----------langfuse埋点结束-----------

          // ----------stream中写入RAG溯源外部链接信息----------------
          const externalLinks = sourceLinkArray as unknown as JSONValue[];
          //写入溯源消息到消息流中
          externalLinks.forEach(async (item) => {
            dataStream.writeMessageAnnotation(item);
          });

          dataStream.writeData({
            isRiskQuery,
          });

          // ----------结束写入RAG溯源外部链接信息----------------

          // 结束时清理计时器
          if (chunkTimerId) {
            clearTimeout(chunkTimerId);
          }
          assistantMessage.annotations = externalLinks;
          // LLM回复写入数据库
          await createMessageIfNotExists(
            serverCtx.prisma,
            assistantMessage as UIMessage,
            lastUserMessage.id,
            id,
            'assistant',
          );
        },
      });
      result.mergeIntoDataStream(dataStream, { sendReasoning: true });
    },
    onError: (error) => {
      // 如果是 AbortError 且是由于超时引起的，可以返回特定消息
      if (error instanceof Error && error.name === 'AbortError') {
        console.error(
          LogFormat.error(`Aborting stream due to inactivity for ${CHUNK_TIMEOUT_IN_MS}ms`),
        );
      } else if (error instanceof Error) {
        console.error(LogFormat.error('DataStreamResponse error:' + error));
      }
      //前端用户看到的错误
      return 'An error occurred!';
    },
  });
}) satisfies RequestHandler;
