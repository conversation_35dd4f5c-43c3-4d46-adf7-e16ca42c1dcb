<script lang="ts">
  import * as Command from '$lib/components/ui/command/index.js';
  import SearchCommandItem from './SearchCommandItem.svelte';
  import { Debounced } from 'runed';
  import type { ChatSearchResult } from '@askme/lib-common';
  import { Skeleton } from '$lib/components/ui/skeleton/index.js';
  import { toast } from 'svelte-sonner';

  interface Props {
    /** @prop {boolean} isOpen - 是否打开搜索栏. */
    isOpen: boolean;
  }

  let { isOpen = $bindable() }: Props = $props();

  const FETCH_COUNT = 10;
  const SKELETON_COUNT_WHEN_SEARCHING = 5;
  const SKELETON_COUNT_WHEN_FETCHING_NEXT_PAGE = 1;
  const SKELETON_COUNT_WHEN_NO_SEARCH = 0;
  const DEBOUNCE_TIME_IN_MS = 500;

  let searchContent = $state('');
  let searchList = $state([] as ChatSearchResult[]);
  let searchStatus: 'searchingNewContent' | 'fetchingNextPage' | 'noSearch' = $state('noSearch');

  let searchPage = 1;
  let isLastPage = false;
  let abortController: AbortController | null = null;

  const debouncedContent = new Debounced(() => searchContent, DEBOUNCE_TIME_IN_MS);

  const skeletonCount = $derived.by(() => {
    switch (searchStatus) {
      case 'searchingNewContent':
        return SKELETON_COUNT_WHEN_SEARCHING;
      case 'fetchingNextPage':
        return SKELETON_COUNT_WHEN_FETCHING_NEXT_PAGE;
      case 'noSearch':
        return SKELETON_COUNT_WHEN_NO_SEARCH;
    }
  });

  $effect(() => {
    // 对搜索进行防抖处理
    if (debouncedContent.current.trim().length !== 0) {
      const FIRST_PAGE = 1;
      search(debouncedContent.current, FIRST_PAGE);
    }
  });

  $effect(() => {
    // 当搜索内容更新时，立刻更新搜索占位动画
    searchStatus = searchContent.trim().length > 0 ? 'searchingNewContent' : 'noSearch';
    searchList = [];
    searchPage = 1;
    isLastPage = false;
  });

  // 搜索并且更新搜索结果（searchList列表）
  async function search(content: string, page: number) {
    // 取消之前的请求（如果存在）（更新输入内容时允许覆盖前一个搜索的fetch请求）
    if (abortController) {
      abortController.abort();
    }

    // 创建新的 AbortController
    abortController = new AbortController();
    try {
      const response = await fetch(`/api/search?content=${content}&page=${page}`, {
        signal: abortController.signal,
      });
      if (!response.ok) {
        throw new Error('Network response was not ok');
      }
      const jsonBody: ChatSearchResult[] = await response.json();
      searchList.push(
        ...jsonBody.map((item) => {
          return {
            ...item,
            dateStr: new Date(item.dateStr).toLocaleDateString(), //将UTC转换为用户浏览器本地时间
          };
        }),
      );
      if (jsonBody.length < FETCH_COUNT) {
        isLastPage = true;
      }
      // 搜索请求成功更新以后，取消Skeleton占位
      searchStatus = 'noSearch';
      abortController = null;
    } catch (e) {
      // 忽略取消fetch请求的错误
      if (e instanceof Error && e.name !== 'AbortError') {
        // 非主动abort请求出现其他异常，取消搜索状态
        searchStatus = 'noSearch';
        // 前端ui搜索报错
        toast.error('搜索失败，请稍后再试。');
      }
    }
  }

  async function handleScroll(event: Event) {
    const element = event.currentTarget as HTMLDivElement;
    // 滚动距底部多远开始fetch下一页
    const REFRESH_THRESHOLD = 20;
    // 计算滚动条距离底部的距离
    const distanceToBottom = element.scrollHeight - element.scrollTop - element.clientHeight;
    if (distanceToBottom < REFRESH_THRESHOLD) {
      // 如果之前fetch请求还在或是已查询到最后一页，直接跳过（查询后续分页时，不允许覆盖前一个搜索fetch请求）
      if (abortController || isLastPage) {
        return;
      }
      searchStatus = 'fetchingNextPage';
      searchPage++;
      search(debouncedContent.current, searchPage);
    }
  }
</script>

<!--
@component
ChatSearchBar是用于显示对话搜索组件。

- 用法:
  ``` svelte
  <ChatSearchBar bind:isOpen={isOpen} />
  ```
-->

<Command.Dialog
  bind:open={isOpen}
  class="rounded-lg border shadow-md md:min-w-[450px]"
  shouldFilter={false}
>
  <Command.Input placeholder="搜索聊天" bind:value={searchContent} maxlength={25} />
  <Command.List onscroll={handleScroll}>
    <Command.Empty>无结果。</Command.Empty>
    <Command.Group>
      {#each searchList as item (item.chatId)}
        <Command.Item value={item.chatId}>
          <a
            data-sveltekit-preload-data="tap"
            href="/chat/{item.chatId}"
            class="w-full"
            onclick={() => (isOpen = false)}
          >
            <SearchCommandItem searchResult={item} />
          </a>
        </Command.Item>
      {/each}
      <!-- 搜索占位动画 -->
      {#each Array.from(Array(skeletonCount).keys()) as i (i)}
        <Command.Item value="">
          <div class="flex items-center space-x-4">
            <Skeleton class="size-6 rounded-full" />
            <div class="space-y-2">
              <Skeleton class="h-4 w-[200px]" />
              <Skeleton class="h-4 w-[250px]" />
            </div>
          </div>
        </Command.Item>
      {/each}
    </Command.Group>
  </Command.List>
</Command.Dialog>
