"use client"
import { FunctionEditor, Options } from "@mdocs/ui";
import { useState, useEffect, Suspense } from "react";
import { useSearchParams, useRouter } from "next/navigation";
import { JlFunction } from "@mdocs/schema";

type FunctionContent = JlFunction

// 示例类型选项
const sampleTypeOptions: Options[] = [
  // 基础类型
  { value: "#/builtins/Scalar", label: "标量 (Scalar)", description: "标量类型" },
  { value: "#/builtins/Vector", label: "向量 (Vector)", description: "向量类型" },
  { value: "#/builtins/Matrix", label: "矩阵 (Matrix)", description: "矩阵类型" },
  { value: "#/builtins/String", label: "字符串 (String)", description: "字符串类型" },
  { value: "#/builtins/Boolean", label: "布尔值 (Boolean)", description: "布尔类型" },
  { value: "#/builtins/Any", label: "任意类型 (Any)", description: "任意类型" },

  // 数值类型
  { value: "#/builtins/Int32", label: "32位整数 (Int32)", description: "32位整数类型" },
  { value: "#/builtins/Int64", label: "64位整数 (Int64)", description: "64位整数类型" },
  { value: "#/builtins/Float32", label: "32位浮点数 (Float32)", description: "32位浮点数类型" },
  { value: "#/builtins/Float64", label: "64位浮点数 (Float64)", description: "64位浮点数类型" },

  // 视觉类型
  { value: "#/builtins/Color", label: "颜色 (Color)", description: "颜色类型" },
  { value: "#/builtins/RGB", label: "RGB颜色 (RGB)", description: "RGB颜色类型" },
  { value: "#/builtins/LineStyle", label: "线型 (LineStyle)", description: "线型类型" },
  { value: "#/builtins/Marker", label: "标记 (Marker)", description: "标记类型" },
];

const sampleVersionOptions: Options[] = [
  {
    value: "2025a",
    label: "2025a",
    description: "2025年第一个版本"
  },
  {
    value: "2024b",
    label: "2024b",
    description: "2024年第二个版本"
  },
  {
    value: "2024a",
    label: "2024a",
    description: "2024年第一个版本"
  }
];
// 示例标签选项
const sampleTagOptions: Options[] = [
  {
    value: "#/i18n/mathematics",
    label: "数学计算",
    description: "数学相关的计算函数，如代数运算、三角函数等"
  },
  {
    value: "#/i18n/plotting",
    label: "绘图可视化",
    description: "用于数据可视化和绘图的函数，支持2D/3D图表"
  },
  {
    value: "#/i18n/dataProcessing",
    label: "数据处理",
    description: "数据处理和分析相关函数，包括清洗、转换、聚合"
  },
  {
    value: "#/i18n/fileIO",
    label: "文件操作",
    description: "文件读写和I/O操作函数，支持多种格式"
  },
  {
    value: "#/i18n/statistics",
    label: "统计分析",
    description: "统计学相关的分析函数，如描述性统计、假设检验"
  },
  {
    value: "#/i18n/signalProcessing",
    label: "信号处理",
    description: "信号处理和滤波函数，用于时域和频域分析"
  },
  {
    value: "#/i18n/optimization",
    label: "优化算法",
    description: "优化和求解算法函数，包括线性和非线性优化"
  },
  {
    value: "#/i18n/experimental",
    label: "实验性",
    description: "实验性功能，API可能会发生变化，谨慎使用"
  },
  {
    value: "#/i18n/advanced",
    label: "高级功能",
    description: "高级用户使用的复杂功能，需要深入理解"
  },
];


const EditorDemoContent = () => {
  const searchParams = useSearchParams();
  const router = useRouter();

  // 从URL参数获取函数名，默认为'demoFunction'
  const functionName = searchParams.get('functionName') || 'demoFunction';

  const [functionData, setFunctionData] = useState<Partial<FunctionContent>>(() => ({
    name: functionName,
    summary: {
      zh: `${functionName} 函数`,
      en: `${functionName} function`
    }
  }));

  const [lastFunctionName, setLastFunctionName] = useState(functionName);

  // 当函数名改变时，重新初始化数据
  useEffect(() => {
    if (functionName !== lastFunctionName) {
      setFunctionData({
        name: functionName,
        summary: {
          zh: `${functionName} 函数`,
          en: `${functionName} function`
        }
      });

      setLastFunctionName(functionName);
    }
  }, [functionName, lastFunctionName]);

  // 演示外部控制按钮
  const ExternalControls = () => {
    const handleReset = () => {
      setFunctionData({});

    };

    const handleGetData = async () => {
      try {
        // 使用Clipboard API复制文本
        await navigator.clipboard.writeText(JSON.stringify(functionData, null, 2));
        alert('复制成功');
      } catch (err) {
        console.error('复制失败:', err);
        alert('复制失败，请打开json视图手动复制');
      }
    };

    const handleBackToHome = () => {
      router.push('/');
    };

    const handleExportJson = () => {
      const json = JSON.stringify(functionData, null, 2);
      const blob = new Blob([json], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'function-data.json';
      a.click();
      URL.revokeObjectURL(url);
    };

    return (
      <div className="flex items-center gap-2 p-4 bg-gray-50 border-b border-gray-200 shadow-sm">
        <button
          onClick={handleBackToHome}
          className="px-3 py-1 text-sm bg-gray-500 text-white border border-gray-500 rounded hover:bg-gray-600 hover:border-gray-600 transition-colors"
        >
          ← 返回主页
        </button>

        <div className="h-4 w-px bg-gray-300 mx-2"></div>

        <div className="flex items-center gap-2">
          <span className="text-sm font-medium text-gray-700">编辑函数:</span>
          <span className="text-sm font-mono bg-blue-100 text-blue-800 px-2 py-1 rounded">
            {functionName}
          </span>
        </div>

        <div className="flex gap-2 ml-4">
          <button
            onClick={handleReset}
            className="px-3 py-1 text-sm bg-white border border-gray-300 rounded hover:bg-gray-50 hover:border-gray-400 transition-colors"
          >
            重置数据
          </button>
          <button
            onClick={handleGetData}
            className="px-3 py-1 text-sm bg-white border border-gray-300 rounded hover:bg-gray-50 hover:border-gray-400 transition-colors"
          >
            复制数据
          </button>

          <button
            onClick={handleExportJson}
            className="px-3 py-1 text-sm bg-white border border-gray-300 rounded hover:bg-gray-50 hover:border-gray-400 transition-colors"
          >
            导出 JSON
          </button>
        </div>
      </div>
    );
  };

  return (
    <div className="h-screen flex flex-col bg-gray-100">
      <ExternalControls />


      <div className="flex-1 border border-gray-200 bg-white m-2 rounded-lg shadow-sm overflow-hidden">
        <FunctionEditor
          functionData={functionData}
          availableTags={sampleTagOptions}
          availableVersions={sampleVersionOptions}
          availableTypes={sampleTypeOptions}
          functionID={functionName}
          onDataChange={(data) => {
            console.log('数据更新:', data);
            setFunctionData(data);
          }}
          isLoading={false}
        />
      </div>
    </div>
  );
};

export default function EditorDemoPage() {
  return (
    <Suspense fallback={<div className="h-screen flex items-center justify-center text-gray-500">加载中...</div>}>
      <EditorDemoContent />
    </Suspense>
  );
}