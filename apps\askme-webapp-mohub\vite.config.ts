import { sveltekit } from '@sveltejs/kit/vite';
import { defineConfig } from 'vite';
import tailwindcss from '@tailwindcss/vite';

export default defineConfig({
  plugins: [tailwindcss(), sveltekit()],
  ssr: {
    noExternal: ['@lucide/svelte', 'svelte-sonner', '@askme/lib-common', 'bits-ui'],
  },
  server: {
    host: '0.0.0.0', // 是否允许局域网访问（0.0.0.0）
    port: 5173, // 自定义开发环境端口（默认5173，可改为你想要的）
    watch: {
      ignored: ['**/askme_config.json', '**/demo_questions.json'],
    },
  },
  preview: {
    host: '0.0.0.0',
    port: 4173, // 自定义生产预览端口（默认4173）
  },
});
