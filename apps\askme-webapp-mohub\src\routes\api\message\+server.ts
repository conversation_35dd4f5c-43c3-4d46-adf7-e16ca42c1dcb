import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from './$types';
import { error } from '@sveltejs/kit';
import * as LogFormat from '@askme/lib-common/logformat';
import serverCtx from '$lib/context.server';
import type { UIMessage } from 'ai';
import { <PERSON>risma, Role } from '$lib/generated/prisma';

// 保存前端发来的message
export const POST = (async ({ request, locals }) => {
  if (!locals.user) {
    console.debug(LogFormat.debug('Unauthorized in /api/summary POST'));
    error(401, 'Unauthorized');
  }

  const requestJson: {
    chatID: string;
    preID: string;
    message: UIMessage;
  } = await request.json();
  // ----------langfuse埋点------------
  const trace = serverCtx.langfuse.trace({
    id: requestJson.preID,
  });
  const generation = trace.generation({
    id: requestJson.preID,
  });
  trace.update({
    output: requestJson.message.parts,
  });
  generation.end({
    output: requestJson.message.parts,
  });
  // -----------langfuse埋点结束-----------
  try {
    await serverCtx.prisma.message.create({
      data: {
        id: requestJson.message.id,
        preId: requestJson.preID,
        parts: requestJson.message.parts as Prisma.InputJsonValue,
        role: requestJson.message.role as Role,
        annotations: requestJson.message.annotations,
        chatId: requestJson.chatID,
        attachments: requestJson.message
          .experimental_attachments as unknown as Prisma.InputJsonValue,
      },
    });
  } catch (error) {
    console.error(LogFormat.error('Failed to save message: ' + error));
  }
  // ⚠️目前这个端点只用在保存中断的消息，暂时不校验任何异常
  return new Response('ok', { status: 201 });
}) satisfies RequestHandler;
