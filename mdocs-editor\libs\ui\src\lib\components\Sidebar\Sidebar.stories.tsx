import type { <PERSON><PERSON>, StoryObj } from "@storybook/react-vite";
import { useState } from "react";
import SideBar from "./Sidebar";

const meta: Meta<typeof SideBar> = {
  title: "Components/Sidebar",
  component: SideBar,
  parameters: {
    layout: "fullscreen",
  },
};
export default meta;
type Story = StoryObj<typeof SideBar>;

export const GlobalLayout: Story = {
  render: () => {
    const [activeTab, setActiveTab] = useState<"llm" | "statistics" | "validation">("llm");
    return (
      <div className="h-screen w-screen flex bg-gray-50">
        {/* 主内容区 - 占满剩余空间 */}
        <div className="flex-1 flex items-center justify-center text-2xl text-gray-400 select-none">
          这里是主内容区 - 占满左侧空间
        </div>
        {/* Sidebar 组件 - 固定在右侧，全高 */}
        <SideBar
          llmScore={92}
          llmComment="模型评审通过，建议优化部分边界条件处理。"
          statisticsInfo={{
            functionCount: 12,
            parameterCount: 37,
            lastModified: "2024-06-10 15:23:45"
          }}
          validationInfo={{
            errors: [
              "函数 validateUser 缺少返回类型声明",
              "参数 userId 类型不匹配"
            ],
            warnings: [
              "建议为函数 addUser 添加注释",
              "部分参数未提供默认值"
            ]
          }}
          activeTab={activeTab}
          onTabChange={setActiveTab}
        />
      </div>
    );
  },
};

export const LongTextContent: Story = {
  render: () => {
    const [activeTab, setActiveTab] = useState<"llm" | "statistics" | "validation">("llm");
    return (
      <div className="h-screen w-full flex bg-gray-50">
        <div className="flex-1 flex items-center justify-center text-2xl text-gray-400 select-none bg-gray-50">
          大量文本测试 - 主内容区
        </div>
        <SideBar
          llmScore={75}
          llmComment="这是一个非常长的评审意见，用来测试文本溢出和滚动效果。这是一个非常长的评审意见，用来测试文本溢出和滚动效果。这是一个非常长的评审意见，用来测试文本溢出和滚动效果。这是一个非常长的评审意见，用来测试文本溢出和滚动效果。这是一个非常长的评审意见，用来测试文本溢出和滚动效果。这是一个非常长的评审意见，用来测试文本溢出和滚动效果。该函数的实现基本符合预期，但存在以下几个问题需要注意：1. 参数验证不够严格，建议增加边界条件检查；2. 错误处理机制需要完善，当前的异常捕获过于宽泛；3. 性能方面可以进一步优化，特别是在处理大量数据时；4. 代码注释需要更加详细，便于后续维护；5. 单元测试覆盖率还需要提升。总的来说，这是一个可以接受的实现，但还有改进空间。建议在下一个版本中重点关注错误处理和性能优化。另外，建议添加更多的日志记录，以便于问题排查和性能监控。在部署到生产环境之前，请确保所有的边界条件都经过充分测试。"
          statisticsInfo={{
            functionCount: 156,
            parameterCount: 423,
            lastModified: "2024-06-11 10:00:00"
          }}
          validationInfo={{
            errors: [
              "函数 validateUserCredentials 缺少返回类型声明，这可能导致类型推断错误",
              "参数 userId 类型不匹配，期望 string 类型但接收到 number 类型",
              "函数 processLargeDataSet 存在潜在的内存泄漏风险，未正确释放资源",
              "异步函数 fetchUserData 缺少错误处理，可能导致未捕获的 Promise 异常",
              "函数 calculateComplexMetrics 的时间复杂度过高，建议优化算法实现"
            ],
            warnings: [
              "建议为函数 addUser 添加详细的 JSDoc 注释，包括参数说明和返回值描述",
              "部分参数未提供默认值，可能导致运行时错误",
              "函数 updateUserProfile 的参数过多，建议使用对象参数模式",
              "变量命名不够语义化，如 'data1', 'temp' 等应该使用更有意义的名称",
              "建议将长函数拆分为更小的、职责单一的函数",
              "某些魔法数字应该定义为常量，提高代码可维护性",
              "建议添加输入参数的边界检查，提高代码健壮性",
              "部分 TODO 注释已存在较长时间，建议及时处理或移除",
              "建议统一代码风格，特别是缩进和空格的使用",
              "某些依赖项版本过旧，建议升级到最新稳定版本"
            ]
          }}
          activeTab={activeTab}
          onTabChange={setActiveTab}
        />
      </div>
    );
  },
};

export const ManyErrors: Story = {
  render: () => {
    const [activeTab, setActiveTab] = useState<"llm" | "statistics" | "validation">("validation");
    return (
      <div className="h-screen w-full flex bg-gray-50">
        <div className="flex-1 flex items-center justify-center text-2xl text-gray-400 select-none bg-gray-50">
          大量错误测试 - 滚动效果
        </div>
        <SideBar
          llmScore={45}
          llmComment="代码质量较差，存在大量问题需要修复。"
          statisticsInfo={{
            functionCount: 89,
            parameterCount: 267,
            lastModified: "2024-06-09 14:30:22"
          }}
          validationInfo={{
            errors: [
              "函数 authenticateUser 缺少返回类型声明",
              "参数 password 类型不匹配，期望 string 但接收到 any",
              "函数 validateEmail 存在潜在的正则表达式注入风险",
              "异步函数 connectDatabase 缺少超时处理机制",
              "函数 encryptData 使用了已弃用的加密算法",
              "变量 globalConfig 存在线程安全问题",
              "函数 parseJsonData 缺少异常处理，可能导致程序崩溃",
              "内存泄漏：事件监听器未正确移除",
              "函数 calculateHash 的实现存在安全漏洞",
              "数据库连接未正确关闭，可能导致连接池耗尽",
              "函数 validateInput 的边界检查不完整",
              "异步操作缺少 Promise 错误处理",
              "函数 sortLargeArray 的时间复杂度过高",
              "缓存机制实现有误，可能导致数据不一致",
              "函数 generateReport 存在 SQL 注入风险",
              "文件操作缺少权限检查",
              "函数 processImage 未处理大文件情况",
              "网络请求缺少重试机制",
              "函数 mergeObjects 存在原型污染风险",
              "日志记录包含敏感信息，存在安全隐患"
            ],
            warnings: [
              "建议为所有公共函数添加 JSDoc 注释",
              "部分变量命名不符合驼峰命名规范",
              "建议使用 TypeScript 严格模式",
              "某些函数过于复杂，建议拆分",
              "建议添加单元测试覆盖",
              "部分依赖项存在已知安全漏洞",
              "建议使用 ESLint 进行代码规范检查",
              "某些魔法数字应该定义为常量",
              "建议优化导入语句的组织方式",
              "部分注释已过时，需要更新",
              "建议使用更现代的 JavaScript 特性",
              "某些条件判断可以简化",
              "建议添加错误边界处理",
              "部分异步操作可以并行执行以提高性能",
              "建议使用配置文件管理环境变量",
              "某些工具函数可以提取到公共库中",
              "建议添加性能监控和日志记录",
              "部分正则表达式可以优化以提高性能",
              "建议使用更安全的随机数生成方法",
              "某些循环可以使用更高效的数组方法替代"
            ]
          }}
          activeTab={activeTab}
          onTabChange={setActiveTab}
        />
      </div>
    );
  },
};

export const EmptyContent: Story = {
  render: () => {
    const [activeTab, setActiveTab] = useState<"llm" | "statistics" | "validation">("llm");
    return (
      <div className="h-screen w-full flex bg-gray-50">
        <div className="flex-1 flex items-center justify-center text-2xl text-gray-400 select-none bg-gray-50">
          空内容测试
        </div>
        <SideBar
          activeTab={activeTab}
          onTabChange={setActiveTab}
        />
      </div>
    );
  },
};

// 校验通过状态
export const ValidationPassed: Story = {
  render: () => {
    const [activeTab, setActiveTab] = useState<"llm" | "statistics" | "validation">("validation");
    return (
      <div className="h-screen w-full flex bg-gray-50">
        <div className="flex-1 flex items-center justify-center text-2xl text-gray-400 select-none bg-gray-50">
          校验通过状态示例
        </div>
        <SideBar
          llmScore={95}
          llmComment="代码质量优秀，校验全部通过。"
          statisticsInfo={{
            functionCount: 15,
            parameterCount: 42,
            lastModified: "2024-06-11 11:30:00"
          }}
          validationInfo={{
            errors: [],
            warnings: []
          }}
          activeTab={activeTab}
          onTabChange={setActiveTab}
        />
      </div>
    );
  },
};
