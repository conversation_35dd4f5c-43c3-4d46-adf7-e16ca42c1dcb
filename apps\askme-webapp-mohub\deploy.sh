#! /bin/bash

scp -i id_rsa -o StrictHostKeychecking=no $MOHUB_ENV gitlab-bot@***********:$DEPLOY_REPO_DIR/apps/askme-webapp-mohub/.env


ssh -i "${DEPLOY_SSH_PRIVATE_KEY}" -o StrictHostKeyChecking=no gitlab-bot@*********** \
    "cd $DEPLOY_REPO_DIR && git checkout ${CI_COMMIT_BRANCH} && git fetch && git reset --hard origin/${CI_COMMIT_BRANCH} && docker-compose pull && docker-compose down && docker-compose up -d"
