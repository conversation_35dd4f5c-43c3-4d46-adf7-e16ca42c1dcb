import type { JlSyntax } from "./types";
import { type Context, getPath, type ValidationError } from "./utils";
/**
 * @summary 语义检查规则: 检查syntax中的params引用是否存在
 * @param context - parser的上下文对象
 * @param syntaxName - 当前syntax对象的名字，用于错误信息中标识
 * @param syntax - 当前syntax对象，包含需要检查的params引用
 * @example
 * ```typescript
 * const context = {
 *   currentFunctionName: "plot",
 *   params: new Set(["plot/params/Y"]),
 *   errors: []
 * };
 * const syntax = { params: ["#params/X", "#params/Y"] };
 * checkParamsRefInSyntax(context, 'X_Y', syntax);
 * // context.errors 会包含一个错误:
 * // [{
 * //   scope: "plot",
 * //   message: "被 syntax X_Y 引用的 Parameter X 不存在"
 * // }]
 * ```
 */
export function checkParamsRefInSyntax(context: Context, syntaxName: string, syntax: JlSyntax): void {
  for (const param of syntax.params) {
    const path = getPath(param);
    const parameName = `${context.currentFunctionName}/${path.slice(0, path.length).join("/")}`;
    if (!context.params.has(parameName)) {
      context.errors.push({
        scope: [...context.scope],
        message: `被 syntax ${syntaxName} 引用的 Parameter ${path[path.length - 1]} 不存在`,
      } satisfies ValidationError);
    }
  }
}
