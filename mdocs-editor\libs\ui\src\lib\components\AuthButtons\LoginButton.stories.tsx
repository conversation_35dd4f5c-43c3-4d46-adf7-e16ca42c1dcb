import type { <PERSON>a, StoryObj } from "@storybook/react-vite";
import { LoginButton } from "./LoginButton";

const meta: Meta<typeof LoginButton> = {
  title: "Components/LoginButton",
  component: LoginButton,
  parameters: {
    layout: "fullscreen",
  },
  tags: ["autodocs"],
  argTypes: {
    variant: {
      control: { type: "select" },
      options: ["default", "outline", "secondary", "ghost", "link", "destructive"],
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    url: "/login/gitlab",
    content: "GitLab 登录",
    title: "欢迎登录",
  },
};

export const Disabled: Story = {
  args: {
    url: "/login/gitlab",
    content: "GitLab 登录",
    title: "登录功能暂时不可用",
    disabled: true,
  },
};
