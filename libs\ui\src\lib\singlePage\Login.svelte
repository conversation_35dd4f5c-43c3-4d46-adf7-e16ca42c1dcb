<script lang="ts">
  import { Button } from '$lib/components/ui/button';
  interface Props {
    /** @prop {string} url - 登陆按钮跳转的URL*/
    url: string;
    /** @prop {string} content - 登陆按钮内容*/
    content: string;
    /** @prop {string} title - 登陆页面标题*/
    title: string;
  }
  const { url, content, title }: Props = $props();
</script>

<!--
@component
Login用于展示登录页面。

- 用法:
``` svelte
<Login url="/login/gitlab" content="GitLab 登陆" />
```
-->
<div class="flex min-h-screen flex-col items-center justify-center">
  <h1 class="mb-8 text-2xl font-semibold">{title}</h1>
  <Button variant="outline" href={url}>{content}</Button>
</div>
