#!/usr/bin/env bash
set -euo pipefail

echo "正在部署分支: ${CI_COMMIT_REF_NAME}"

REMOTE="gitlab-bot@***********"
SSH_OPTS="-i id_rsa -o StrictHostKeyChecking=no"
APP_DIR="$DEPLOY_REPO_DIR/deployments/mdocs-editor"

# 1) 远端：切换到目标分支，并确保部署目录存在
ssh $SSH_OPTS "$REMOTE" \
  "DEPLOY_REPO_DIR='$DEPLOY_REPO_DIR' CI_COMMIT_REF_NAME='$CI_COMMIT_REF_NAME' bash -s" <<'EOF'
cd "$DEPLOY_REPO_DIR"
git reset --hard
git fetch origin

git checkout -B "${CI_COMMIT_REF_NAME}" "origin/${CI_COMMIT_REF_NAME}"
git reset --hard "origin/${CI_COMMIT_REF_NAME}" --

mkdir -p "$DEPLOY_REPO_DIR/deployments/mdocs-editor"
EOF

# 2) 传 .env（此时目录已存在，避免 scp 失败）
scp $SSH_OPTS "$CONFIG_ENVFILE" "$REMOTE:$APP_DIR/.env"

# 3) 远端：进入部署目录并启动
ssh $SSH_OPTS "$REMOTE" \
  "DEPLOY_REPO_DIR='$DEPLOY_REPO_DIR' bash -s" <<'EOF'
set -euo pipefail
APP_DIR="$DEPLOY_REPO_DIR/deployments/mdocs-editor"

cd "$APP_DIR"
if [ ! -f docker-compose.yml ]; then
  echo "缺少 $APP_DIR/docker-compose.yml" >&2
  exit 12
fi

# docker compose v2 优先，兼容 v1
if docker compose version >/dev/null 2>&1; then
  docker compose pull
  docker compose down
  docker compose up -d --remove-orphans
else
  docker-compose pull
  docker-compose down
  docker-compose up -d --remove-orphans
fi
EOF
