import logoImage from "./logo.png";
import { cn } from "../../utils";

export interface LogoProps {
  /** Logo 文本 */
  text?: string;
  /** 跳转链接 */
  href?: string;
  /** 自定义样式 */
  className?: string;
}

/**
 * 简单的 Logo 组件
 * @example
 * ```tsx
 * <Logo href="/" />
 * ```
 */
export function Logo({
  text,
  href = "/",
  className,
}: LogoProps) {
  return (
    <a
      href={href}
      className={cn(
        "flex items-center gap-2 text-xl font-bold hover:opacity-80 transition-opacity",
        className
      )}
    >
      <img
        src={logoImage}
        alt="Logo"
        className="w-32 h-10 object-contain"
      />
      {text}
    </a>
  );
}

export default Logo; 