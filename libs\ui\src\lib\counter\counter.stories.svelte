<script module>
  //    👆 notice the module context, defineMeta does not work in a regular <script> tag - instance
  import { defineMeta } from '@storybook/addon-svelte-csf';

  import Counter from './counter.svelte';

  //      👇 Get the Story component from the return value
  const { Story } = defineMeta({
    title: 'UI/Counter',
    component: Counter,
    decorators: [
      /* ... */
    ],
    parameters: {
      /* ... */
    },
  });
</script>

<Story name="Default" />
