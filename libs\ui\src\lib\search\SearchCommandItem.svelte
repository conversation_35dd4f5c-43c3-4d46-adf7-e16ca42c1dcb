<script lang="ts">
  import type { ChatSearchResult } from '@askme/lib-common';
  import MessageCircle from '@lucide/svelte/icons/message-circle';

  interface Props {
    /** @prop {SearchResult} searchResult - 搜索项的结果. */
    searchResult: ChatSearchResult;
  }

  const { searchResult }: Props = $props();
  let isHover = $state(false);
</script>

<div
  class="group flex w-full cursor-pointer items-center gap-2"
  role="button"
  tabindex="0"
  onmouseenter={() => {
    isHover = true;
  }}
  onmouseleave={() => {
    isHover = false;
  }}
>
  <MessageCircle class="flex-shrink-0" />
  <div class="flex min-w-0 flex-1 flex-col">
    <div class="truncate text-lg">{searchResult.title}</div>
    <div class="truncate text-sm text-gray-600">{searchResult.detail}</div>
  </div>
  {#if isHover}
    <div class="ml-auto shrink-0">
      {searchResult.dateStr}
    </div>
  {/if}
</div>
