<script module>
  //    👆 notice the module context, defineMeta does not work in a regular <script> tag - instance
  import { defineMeta } from '@storybook/addon-svelte-csf';

  import ExternalSource from './ExternalSource.svelte';

  //      👇 Get the Story component from the return value
  const { Story } = defineMeta({
    title: 'UI/ExternalSource',
    component: ExternalSource,
    decorators: [
      /* ... */
    ],
    parameters: {
      /* ... */
    },
  });
</script>

<Story
  name="Default"
  args={{
    externalLink: {
      type: 'externalLink',
      url: 'https://www.mohub.com',
      title: 'MoHub',
      chunkText:
        'MoHub是苏州同元软控公司基于独立自主的系统建模仿真求解内核打造的建模仿真云平台，支持各行业工业知识、经验、数据的模型化、软件化、平台化，实现工业知识的众创、 ...',
    },
  }}
/>

<Story
  name="Long title"
  args={{
    externalLink: {
      type: 'externalLink',
      url: 'https://www.mohub.com',
      title:
        'MoHub是苏州同元软控公司基于独立自主的系统建模仿真求解内核打造的建模仿真云平台，支持各行业工业知识、经验、数据的模型化、软件化、平台化，实现工业知识的众创',
      chunkText:
        'MoHub是苏州同元软控公司基于独立自主的系统建模仿真求解内核打造的建模仿真云平台，支持各行业工业知识、经验、数据的模型化、软件化、平台化，实现工业知识的众创、 ...',
    },
  }}
/>

<Story
  name="Long title and long url"
  args={{
    externalLink: {
      type: 'externalLink',
      url: 'https://www.mohub.com/aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa',
      title:
        'MoHub是苏州同元软控公司基于独立自主的系统建模仿真求解内核打造的建模仿真云平台，支持各行业工业知识、经验、数据的模型化、软件化、平台化，实现工业知识的众创',
      chunkText:
        'MoHub是苏州同元软控公司基于独立自主的系统建模仿真求解内核打造的建模仿真云平台，支持各行业工业知识、经验、数据的模型化、软件化、平台化，实现工业知识的众创、 ...',
    },
  }}
/>

<Story
  name="Long url"
  args={{
    externalLink: {
      type: 'externalLink',
      url: 'https://www.mohub.com/aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa',
      title: 'MoHub',
      chunkText:
        'MoHub是苏州同元软控公司基于独立自主的系统建模仿真求解内核打造的建模仿真云平台，支持各行业工业知识、经验、数据的模型化、软件化、平台化，实现工业知识的众创、 ...',
    },
  }}
/>
