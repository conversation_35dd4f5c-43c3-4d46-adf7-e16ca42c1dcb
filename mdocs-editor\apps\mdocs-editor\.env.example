# 注意下面配置了两个一样的postgres连接
# 因为postgresql的镜像不支持使用完整url配置
# 而prisma又不支持分别使用URL/PASSWORD字段配置
# 所以你需要分别配置

#postgresql配置（dockercompose利用，开发时可忽略）
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres
POSTGRES_DB=mdocs_editor

#postgresql配置（primsa利用）（docker部署记得换成compose.yml中的数据库service，而不是localhost）
#参考：postgres[ql]://[user[:password]@]host[:port][/dbname][?param1=value1&param2=value2]
DATABASE_URL=************************************/mdocs_editor