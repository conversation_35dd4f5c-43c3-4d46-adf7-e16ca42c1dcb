import { MoreHorizontal, Download, Trash2 } from "lucide-react";
import { useState } from "react";
import { Button } from "@/lib/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/lib/components/ui/dropdown-menu";
import { Library } from "../LibraryManager";


/**
 * LibraryActions 组件属性接口
 */
export interface LibraryActionsProps {
  /** Library 数据 */
  library: Library;
  /** 导出回调函数 */
  onExport?: (library: Library) => void | Promise<void>;
  /** 删除回调函数 */
  onDelete?: (library: Library) => void | Promise<void>;
}

/**
 * LibraryActions 组件
 * 
 * 功能：
 * - 提供导出和删除 Library 的操作菜单
 * - 管理操作过程中的加载状态
 * - 处理操作过程中的错误
 * 
 */
export function LibraryActions({
  library,
  onExport,
  onDelete,
}: LibraryActionsProps) {
  const [isExporting, setIsExporting] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  const handleExport = async () => {
    if (!onExport) return;

    try {
      setIsExporting(true);
      await onExport(library);
    } catch (error) {
      console.error(error, '导出失败');
    } finally {
      setIsExporting(false);
    }
  };

  const handleDelete = async () => {
    if (!onDelete) return;

    try {
      setIsDeleting(true);
      await onDelete(library);
    } catch (error) {
      console.error(error, '删除失败');
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="h-8 w-8 p-0">
          <span className="sr-only">打开菜单</span>
          <MoreHorizontal className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        {onExport && (
          <DropdownMenuItem onClick={handleExport} disabled={isExporting}>
            <Download className="mr-2 h-4 w-4" />
            {isExporting ? "导出中..." : "导出"}
          </DropdownMenuItem>
        )}
        {onDelete && (
          <DropdownMenuItem 
            onClick={handleDelete}
            disabled={isDeleting}
            className="text-destructive focus:text-destructive"
          >
            <Trash2 className="mr-2 h-4 w-4" />
            {isDeleting ? "删除中..." : "删除"}
          </DropdownMenuItem>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
