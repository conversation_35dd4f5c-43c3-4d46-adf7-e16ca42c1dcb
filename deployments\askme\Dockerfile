FROM node:22

WORKDIR /app

COPY . .

# 安装依赖
RUN sed -i 's/deb.debian.org/mirrors-dev.tongyuan.cc/g' /etc/apt/sources.list.d/debian.sources && \
    echo 'Acquire::Check-Valid-Until "0";' >> /etc/apt/apt.conf.d/10no--check-valid-until && \
    apt-get -qq update && \
    apt-get install -y -qq libsecret-1-dev curl && \
    npm config set registry https://mirrors-dev.tongyuan.cc/npm && \
    npm install -g pnpm@9 && \
    pnpm -v && \
    pnpm config set registry https://mirrors-dev.tongyuan.cc/npm && \
    pnpm install --frozen-lockfile && \
    pnpm -C apps/askme-webapp-mohub exec prisma generate

# 构建
RUN pnpm run build:libs && \
    pnpm run --filter "./apps/askme-webapp-mohub" build


WORKDIR /app/apps/askme-webapp-mohub
