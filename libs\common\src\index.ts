import { z } from 'zod/v4';
/**
 * 返回 "Hello, World!" 字符串。
 * @returns "Hello, World!"
 */
export function helloworld(): string {
  return 'Hello, World!';
}

/**
 * Chat模型ViewModel。
 * - id: Chat的UUID标识符。
 * - createdAt: Chat创建的日期。
 * - title: Chat的标题。
 */
export interface ChatModel {
  /**
   * Chat的UUID标识符。
   */
  id: string;
  /**
   * Chat创建的日期。
   */
  createdAt: Date;
  /**
   * Chat的标题。
   */
  title: string;
}

/**
 * 首页推荐按钮的View Model类型。
 * - title: 首页推荐对话的大标题。
 * - label: 首页推荐对话的小标题。
 * - action: 实际发送的User Message。
 */
export interface SuggestedActionModel {
  /**
   * 首页推荐对话的大标题。
   */
  title: string;
  /**
   * 首页推荐对话的小标题。
   */
  label: string;
  /**
   * 实际发送的User Message。
   */
  action: string;
}

/** 聊天模型配置 */
export interface ModelConfig {
  /**
   * 模型id
   */
  id: string;
  /**
   * 前端显示的模型名称
   */
  name: string;
  /**
   * 前端显示的模型描述
   */
  description: string;
}

export interface CodeSnippet {
  language: string;
  code: string;
}
/** Embedding API Response Types */
export interface EmbeddingResponse {
  /** 模型编码 */
  model: string;
  /** 结果类型，目前为 list */
  object: 'list';
  /** 模型生成的数组结果 */
  data: EmbeddingData[];
  /** 本次模型调用的 tokens 数量统计 */
  usage: EmbeddingUsage;
}

interface EmbeddingData {
  /** 结果下标 */
  index: number;
  /** 结果类型，目前为 embedding */
  object: 'embedding';
  /** embedding的处理结果，返回向量化表征的数组 */
  embedding: number[];
}

interface EmbeddingUsage {
  /** 用户输入的 tokens 数量 */
  prompt_tokens: number;
  /** 模型输出的 tokens 数量 */
  completion_tokens: number;
  /** 总 tokens 数量 */
  total_tokens: number;
}

/**
 * 描述有向的Message类型
 * 除了自身的id外，还需要有一个指向其他Message的preId
 */
export interface OrderedMessage {
  /** 消息的唯一标识符 */
  id: string;
  /** 前一条消息的ID，如果前一条消息指向根节点则为null */
  preId: string | null;
}

export const UserPreferancesCookiesSchema = z.object({
  /**
   * 选择的模型
   * ⚠️ legacy config，用于支持ModelSelector组件。
   * 暂时没用上，保留，可能以后有用
   */
  selectedModel: z.string(),
  /**
   * 是否开启深度思考
   * 深度思考选项放在cookie里的原因：
   * 1. 后端可以拿到选项从而选择模型。
   * 2. ssr时可以预先在服务端渲染出用户上次点击状态，避免水合的时间差导致的前端按钮状态突然变化。
   */
  isReasoningEnabled: z.boolean(),
});

export type UserPreferancesCookies = z.infer<typeof UserPreferancesCookiesSchema>;

export interface RAGSearchResultMetadata {
  location: string;
  external_link?: string;
  internal_link?: string;
  title: string;
  project_name: string;
  chunk_index: number;
  created_at: string;
  image_url: string | null;
}

export interface RAGSearchResult {
  id: string;
  score: number;
  content: string;
  metadata: RAGSearchResultMetadata;
}

export interface RAGSearchResponse {
  results: RAGSearchResult[];
}

export interface FileInfo {
  /** 文件系统中的文件引用 */
  file: File;
  /** 上传后返回的uuid */
  uuid: string | undefined;
  /** 文件的上传状态 */
  uploadStatus: 'notStarted' | 'uploading' | 'uploaded' | 'uploadFailed';
}

/** bot消息中的外部溯源链接 */
export interface ExternalLinkPart {
  type: 'externalLink';
  /** 溯源的外部链接URL */
  url: string;
  /** 溯源文本标题 */
  title: string;
  /** 溯源文本切片 */
  chunkText: string;
}

// 还可以union其他自定义数据
export type MessageAnnotation = ExternalLinkPart;

/** chat搜索结果 */
export interface ChatSearchResult {
  /** 标题 */
  title: string;
  /** 部分大模型回复文本 */
  detail: string;
  /** 日期字符串 */
  dateStr: string;
  /** chatId */
  chatId: string;
}

export { smoothStream } from './smooth-stream';
export * from './messageTree';
export * from './switchActions';
export * from './context';
export * from './Chat/chat.svelte';
export * from './Chat/chat-context.svelte';
export * from './useLocalStorage.svelte';
export * from './useCookies.svelte';
