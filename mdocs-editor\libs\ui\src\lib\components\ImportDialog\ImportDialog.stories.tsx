import type { Meta, StoryObj } from '@storybook/react-vite';
import { ImportDialog } from './ImportDialog';

const meta: Meta<typeof ImportDialog> = {
  title: 'Components/ImportDialog',
  component: ImportDialog,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: '导入 Library 对话框组件，支持文件拖拽和选择上传。',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    open: {
      description: '对话框开启状态',
      control: 'boolean',
    },
    onOpenChange: {
      description: '对话框状态变更回调',
    },
    onImport: {
      description: '导入文件回调函数',
    },
  },
};

export default meta;
type Story = StoryObj<typeof ImportDialog>;



/**
 * 默认状态
 */
export const Default: Story = {
  args: {
    open: true,
    onOpenChange: (open: boolean) => console.log('onOpenChange', open),
    onImport: async (file: File) => {
      console.log('onImport', { name: file.name, size: file.size, type: file.type });
    },
    allowedFileTypes: '.json,.txt',
  },
};

/**
 * 导入失败演示
 */
export const ImportError: Story = {
  args: {
    open: true,
    onOpenChange: (open: boolean) => console.log('onOpenChange', open),
    onImport: async (file: File) => {
      console.log('onImport', { name: file.name, size: file.size, type: file.type });
      // 模拟错误
      throw new Error('文件导入错误');
    },
    allowedFileTypes: '.json,.txt',
  },
};


