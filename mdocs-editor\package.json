{"name": "mdocs-editor-monorepo", "version": "0.0.0", "description": "MWORKS 文档编辑器", "scripts": {"build:libs": "pnpm --filter \"./libs/*\" build", "build:app": "pnpm build:libs && pnpm -C apps/mdocs-editor build", "format": "biome format --write", "lint": "biome check && pnpm -r apps/mdocs-editor lint", "test": "pnpm -r test"}, "packageManager": "pnpm@10.10.0", "devDependencies": {"@biomejs/biome": "^2.1.1", "next": "15.3.5"}, "pnpm": {"peerDependencyRules": {"ignoreMissing": ["react", "react-dom"], "allowedVersions": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}}, "onlyBuiltDependencies": ["@prisma/client", "@prisma/engines", "@swc/core", "@tailwindcss/oxide", "esbuild", "prisma", "sharp", "unrs-resolver"]}}