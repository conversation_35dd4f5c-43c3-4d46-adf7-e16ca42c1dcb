import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from './$types';
import { error } from '@sveltejs/kit';
import * as LogFormat from '@askme/lib-common/logformat';
import { json } from '@sveltejs/kit';
import type { ChatSearchResult } from '@askme/lib-common';
import serverCtx from '$lib/context.server';
import { findSubstringWithLength } from '$lib/utils';
import { searchChatFromDB } from '$lib/serverUtils';

// 从数据库中搜索聊天记录
// GET "/api/search?content=xxx&page=1"
export const GET = (async ({ locals, url }) => {
  if (!locals.user) {
    console.debug(LogFormat.debug('Unauthorized in /search GET'));
    error(401, 'Unauthorized');
  }
  const searchContent = url.searchParams.get('content');
  if (!searchContent || searchContent.length === 0) {
    return json([]);
  }
  const page = parseInt(url.searchParams.get('page') || '1');
  const ITEM_COUNT = 10; // 每页显示的条目数

  const searchMessages = await searchChatFromDB(
    serverCtx.prisma,
    searchContent,
    parseInt(locals.user.id),
    page,
    ITEM_COUNT,
  );

  const MAX_LENGTH = 35;
  const searchResult = searchMessages.map((m) => {
    return {
      chatId: m.chatId,
      title: m.chatTitle,
      dateStr: m.createdAt.toUTCString(),
      detail: findSubstringWithLength(m.matchedText, searchContent, MAX_LENGTH),
    } as ChatSearchResult;
  });
  return json(searchResult);
}) satisfies RequestHandler;
