<script lang="ts">
  import type { Snippet } from 'svelte';
  import {
    setChatContext,
    KeyedChatStore,
    setChatIdContext,
    UseCookies,
    setUserPreferancesCookiesContext,
    setVoteContext,
    UserPreferancesCookiesSchema,
  } from '@askme/lib-common';
  import type { UserPreferancesCookies } from '@askme/lib-common';
  import { SvelteMap } from 'svelte/reactivity';
  import { Toaster } from '../src/lib/components/ui/sonner/index.js';

  let { children }: { children: Snippet } = $props();

  const chatId = $state({ chatId: '1' });
  setChatIdContext(() => chatId);
  const keyedChatStore = $state<KeyedChatStore>(new KeyedChatStore());
  const store = keyedChatStore.get('1');
  store.messages = {
    child: [
      {
        id: '1',
        role: 'user',
        content: 'Hello',
        parts: [{ type: 'text', text: 'Hello' }],
        createdAt: new Date(),
        child: [
          {
            id: '2',
            role: 'assistant',
            content: 'Response 1',
            parts: [{ type: 'text', text: 'Response 1' }],
            createdAt: new Date(),
            child: [],
          },
          {
            id: '3',
            role: 'assistant',
            content: 'Response 2',
            parts: [{ type: 'text', text: 'Response 2' }],
            createdAt: new Date(),
            child: [],
          },
          {
            id: '4',
            role: 'assistant',
            content: 'Response 3',
            parts: [{ type: 'text', text: 'Response 3' }],
            createdAt: new Date(),
            child: [],
          },
        ],
      },
    ],
    infoMap: new Map([
      [null, { level: 0, parentMsgId: null, childCount: 1 }],
      ['1', { level: 1, parentMsgId: null, childCount: 3 }],
      ['2', { level: 2, parentMsgId: '1', childCount: 0 }],
      ['3', { level: 2, parentMsgId: '1', childCount: 0 }],
      ['4', { level: 2, parentMsgId: '1', childCount: 0 }],
    ]),
  };
  setChatContext(keyedChatStore);

  export function userPreferancesCookiesValidator(v: UserPreferancesCookies) {
    try {
      UserPreferancesCookiesSchema.parse(v);
      return true;
    } catch {
      return false;
    }
  }

  const userPreferancesCookies = $state(
    new UseCookies(
      'userPreferances',
      { selectedModel: 'glm-4-airx', isReasoningEnabled: true },
      userPreferancesCookiesValidator,
    ),
  );
  setUserPreferancesCookiesContext(userPreferancesCookies);
  setVoteContext(new SvelteMap<string, boolean>([['1', true]]));
</script>

<Toaster position="top-center" theme="light" richColors />
<div>
  {@render children()}
</div>
