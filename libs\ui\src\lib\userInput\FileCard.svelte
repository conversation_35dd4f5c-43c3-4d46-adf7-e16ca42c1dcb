<script lang="ts">
  import Image from '@lucide/svelte/icons/image';
  import Files from '@lucide/svelte/icons/files';
  import { cn } from '$lib/utils';
  import X from '@lucide/svelte/icons/x';
  import * as Tooltip from '$lib/components/ui/tooltip';
  import * as Dialog from '$lib/components/ui/dialog';
  import type { ClassValue } from 'svelte/elements';

  interface Props {
    /** @prop {string} uuid - 上传后文件的uuid. */
    uuid?: string;
    /** @prop {'uploading' | 'uploaded'} status - 文件上传状态. */
    status: 'notStarted' | 'uploading' | 'uploaded' | 'uploadFailed';
    /** @prop {(event: MouseEvent) => void} onclose - 关闭按钮的回调函数（若不传入，右上角不会出现关闭按钮。） */
    onClose?: (event: MouseEvent) => void;
    /** @prop {string} fileName - 文件名 */
    fileName: string;
    /** @prop {number} fileSize - 文件大小 */
    fileSize: number;
    /** @prop {File} [file] - 图片文件对象（用于预览） */
    imgFile?: File;
    /** @prop {ClassValue} class - 自定义样式 */
    class?: ClassValue;
  }
  const { fileName, fileSize, uuid, status, onClose, imgFile, class: className }: Props = $props();

  const fileExtension = $derived(fileName.split('.').pop()?.toUpperCase());
  const fileSizeString = $derived.by(() => {
    const BYTES_IN_KB = 1024;
    if (fileSize < BYTES_IN_KB) {
      return `${fileSize}B`;
    } else if (fileSize < BYTES_IN_KB * BYTES_IN_KB) {
      return `${(fileSize / BYTES_IN_KB).toFixed(2)}KB`;
    } else {
      return `${(fileSize / BYTES_IN_KB / BYTES_IN_KB).toFixed(2)}MB`;
    }
  });
  const fileType: 'image' | 'other' = $derived.by(() => {
    const ext = fileExtension || '';
    // 图片类型
    if (['JPG', 'JPEG', 'PNG', 'GIF', 'SVG', 'WEBP', 'BMP'].includes(ext)) return 'image';
    // 默认
    return 'other';
  });

  let isPreviewOpen = $state(false);
  function showPreview() {
    if (fileType === 'image') {
      isPreviewOpen = true;
    }
  }
</script>

<!--
@component
FileCard用于显示上传的文件。

- 用法:
  ``` svelte
  <FileCard
    fileName="文件名.jpg"
    fileSize={1024}
    uuid="uuid"
    status="uploading"
    {onClose}
  />
  ```
-->
<Tooltip.Provider delayDuration={1}>
  <Tooltip.Root>
    <Tooltip.Trigger>
      <div
        class={cn('group bg-muted relative flex  gap-2 rounded-lg border p-2', className)}
        onclick={() => fileType === 'image' && showPreview()}
        onkeydown={(e) => e.key === 'Enter' && fileType === 'image' && showPreview()}
        role="button"
        tabindex="0"
      >
        <div class="shrink-0 self-center">
          {#if fileType === 'image'}
            {#if imgFile && status !== 'uploaded'}
              <img class="h-8 w-8" src={URL.createObjectURL(imgFile)} alt={fileName} />
            {:else if status === 'uploaded'}
              <img class="h-8 w-8" src={`/api/files/${uuid}?ext=${fileExtension}`} alt={fileName} />
            {:else}
              <Image class="h-8 w-8" />
            {/if}
          {:else}
            <Files class="h-8 w-8" />
          {/if}
        </div>

        <div class="flex min-w-0 flex-col items-start">
          <span class="block w-full truncate text-left text-sm font-medium">
            {fileName}
          </span>

          {#if status === 'notStarted'}
            <span class="block truncate text-xs text-gray-500">等待上传...</span>
          {:else if status === 'uploading'}
            <span class="block truncate text-xs text-gray-500">上传中...</span>
          {:else if status === 'uploaded'}
            <span class="block truncate text-xs text-gray-500"
              >{fileExtension} {fileSizeString}</span
            >
          {:else if status === 'uploadFailed'}
            <span class="block truncate text-xs text-red-500">上传失败</span>
          {/if}
        </div>

        {#if onClose}
          {#if status === 'uploadFailed'}
            <button
              class="absolute -top-2 -right-2 rounded-full bg-red-500 p-1 text-white shadow-sm"
              onclick={onClose}
            >
              <X size={14} />
              <span class="sr-only">关闭</span>
            </button>
          {:else}
            <button
              class="absolute -top-2 -right-2 hidden rounded-full bg-white p-1 text-gray-500 shadow-sm group-hover:block hover:bg-gray-100 hover:text-gray-700"
              onclick={onClose}
            >
              <X size={14} />
              <span class="sr-only">关闭</span>
            </button>
          {/if}
        {/if}
      </div>
    </Tooltip.Trigger>
    <Tooltip.Content>
      <p>{fileName}</p>
    </Tooltip.Content>
  </Tooltip.Root>
</Tooltip.Provider>

<Dialog.Root bind:open={isPreviewOpen}>
  <Dialog.Content class="h-5/6 max-w-fit overflow-y-auto">
    <Dialog.Header>
      <Dialog.Title>{fileName}</Dialog.Title>
    </Dialog.Header>
    <img src={`/api/files/${uuid}?ext=${fileExtension}`} alt={fileName} />
  </Dialog.Content>
</Dialog.Root>
