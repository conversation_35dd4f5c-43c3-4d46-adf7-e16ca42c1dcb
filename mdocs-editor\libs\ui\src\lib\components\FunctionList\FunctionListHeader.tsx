interface FunctionListHeaderProps {
  /** 函数库名称 */
  libraryName: string;
  /** 函数总数 */
  functionCount: number;
}

/**
 * FunctionListHeader 组件
 * 
 * 显示函数库名称和函数数量统计
 */
export function FunctionListHeader({
  libraryName,
  functionCount
}: FunctionListHeaderProps) {
  return (
    <div className="px-4 py-4 bg-white">
      <div className="flex items-center justify-between">
        <div className="min-w-0 flex-1">
          <h2
            title={libraryName}
            className="text-lg font-semibold text-gray-900 truncate"
          >
            {libraryName}
          </h2>
          <span className="text-sm text-gray-500 mt-0.5">
            {functionCount} 个函数
          </span>
        </div>
      </div>
    </div>
  );
}
