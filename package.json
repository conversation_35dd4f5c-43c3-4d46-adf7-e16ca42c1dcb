{"name": "askme-webapp-monorepo", "version": "1.1.0", "scripts": {"build:libs": "pnpm --filter \"./libs/*\" build", "format": "pnpm -r format", "check": "pnpm -r check", "lint": "pnpm -r lint", "update-versions": "node scripts/update-versions.js", "check-versions": "node scripts/update-versions.js --check"}, "peerDependencies": {"svelte": "^5.0.0"}, "devDependencies": {"@askme/lib-common": "link:libs/common", "@askme/lib-ui": "link:libs/ui/dist", "@eslint/compat": "^1.3.0", "@eslint/js": "^9.29.0", "@fontsource/fira-mono": "^5.2.6", "@internationalized/date": "^3.8.2", "@lucide/svelte": "0.514.0", "@neoconfetti/svelte": "^2.2.2", "@playwright/test": "^1.53.0", "@sveltejs/adapter-node": "^5.2.12", "@sveltejs/kit": "2.21.4", "@sveltejs/package": "^2.3.11", "@sveltejs/vite-plugin-svelte": "5.1.0", "@tailwindcss/vite": "^4.1.8", "@testing-library/jest-dom": "^6.6.3", "@testing-library/svelte": "^5.2.8", "bits-ui": "2.8.10", "clsx": "^2.1.1", "eslint": "^9.29.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-svelte": "3.9.2", "formsnap": "^2.0.1", "globals": "^16.2.0", "jsdom": "^26.1.0", "paneforge": "1.0.0-next.5", "prettier": "^3.5.3", "prettier-plugin-svelte": "^3.4.0", "prettier-plugin-tailwindcss": "^0.6.12", "publint": "^0.3.12", "svelte": "5.33.19", "svelte-check": "^4.2.1", "svelte-sonner": "^1.0.5", "tailwind-merge": "^3.3.1", "tailwind-variants": "^1.0.0", "tailwindcss": "^4.1.8", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.3.4", "typescript": "^5.8.3", "typescript-eslint": "^8.34.0", "vaul-svelte": "1.0.0-next.7", "vite": "6.3.5", "vitest": "^3.2.3"}, "pnpm": {"onlyBuiltDependencies": ["esbuild", "svelte-preprocess"]}}