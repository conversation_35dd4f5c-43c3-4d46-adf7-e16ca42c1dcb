import { Table, TableHeader, TableRow, TableHead, TableBody, TableCell } from "../ui/table";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "../ui/dialog";
import { Button } from "../ui/button";
import { ExternalLink } from "lucide-react";
import type { FunctionInfo } from "./FunctionLibraryOverview";

export function FunctionsTab({ functions, onFunctionClick, dialogOpen, setDialogOpen }: {
  functions: FunctionInfo[];
  onFunctionClick?: (name: string) => void;
  dialogOpen: string | null;
  setDialogOpen: (name: string | null) => void;
}) {
  return (
    <div className="overflow-y-auto overflow-x-auto h-[500px]">
      <Table className="min-w-[800px]">
        <TableHeader>
          <TableRow>
            <TableHead className="w-[160px] min-w-[140px]">名称</TableHead>
            <TableHead className="max-w-[350px] min-w-[200px]">简介</TableHead>
            <TableHead className="w-[80px] min-w-[60px]">得分</TableHead>
            <TableHead className="w-[120px] min-w-[100px]">更新作者</TableHead>
            <TableHead className="w-[140px] min-w-[120px]">上次更新时间</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {functions.map((fn) => (
            <TableRow key={fn.name}>
              <TableCell className="font-medium w-[160px] min-w-[140px]">
                <div className="space-y-1">
                  <div className="flex items-center gap-2">
                    <Button
                      variant="link"
                      className="h-auto p-0 font-medium text-left justify-start max-w-[140px]"
                      onClick={() => onFunctionClick && onFunctionClick(fn.name)}
                      title={fn.name}
                    >
                      <span className="truncate mr-1">{fn.name}</span>
                      <ExternalLink className="h-3 w-3 flex-shrink-0" />
                    </Button>
                    {/* 弃用标志 */}
                    {fn.deprecated && (
                      <span className="text-xs text-orange-600 bg-orange-50 border border-orange-200 px-1.5 py-0.5 rounded flex-shrink-0">
                        弃用
                      </span>
                    )}
                  </div>
                </div>
              </TableCell>
              <TableCell className="max-w-[350px] min-w-[200px]">
                <div
                  className="text-gray-800 cursor-pointer text-sm leading-relaxed"
                  style={{
                    display: "-webkit-box",
                    WebkitLineClamp: 3,
                    WebkitBoxOrient: "vertical",
                    overflow: "hidden",
                    textOverflow: "ellipsis",
                    wordBreak: 'break-all',
                    whiteSpace: 'normal',
                  }}
                  title="点击查看详情"
                  onClick={() => setDialogOpen(fn.name)}
                >
                  {fn.summary}
                </div>
                <Dialog open={dialogOpen === fn.name} onOpenChange={open => open ? setDialogOpen(fn.name) : setDialogOpen(null)}>
                  <DialogContent className="max-w-[500px] max-h-[400px] flex flex-col">
                    <DialogHeader>
                      <DialogTitle>函数简介</DialogTitle>
                    </DialogHeader>
                    <div className="flex-1 overflow-auto">
                      <div className="text-base text-gray-900 break-all whitespace-pre-line mb-4">
                        {fn.summary}
                      </div>
                    </div>
                    <DialogFooter>
                      <button
                        className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
                        onClick={() => setDialogOpen(null)}
                      >
                        关闭
                      </button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              </TableCell>
              <TableCell className="w-[80px] break-all">{fn.score || '-'}</TableCell>
              <TableCell className="w-[120px] min-w-[100px]">
                <div className="truncate text-sm" title={fn.lastUpdatedBy}>
                  {fn.lastUpdatedBy || '-'}
                </div>
              </TableCell>
              <TableCell className="w-[140px] min-w-[120px]">
                <div className="truncate text-sm " title={fn.lastUpdatedAt ? fn.lastUpdatedAt.toLocaleString() : undefined}>
                  {fn.lastUpdatedAt ? fn.lastUpdatedAt.toLocaleDateString() : '-'}
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}
export default FunctionsTab; 