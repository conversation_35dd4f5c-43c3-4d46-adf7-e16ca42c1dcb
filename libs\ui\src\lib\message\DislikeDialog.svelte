<script lang="ts">
  import * as Dialog from '$lib/components/ui/dialog/index.js';
  import Button from '$lib/components/ui/button/button.svelte';
  import * as RadioGroup from '$lib/components/ui/radio-group/index.js';
  import { Label } from '$lib/components/ui/label/index.js';
  import Textarea from '$lib/components/ui/textarea/textarea.svelte';
  import { toast } from 'svelte-sonner';
  import { getVoteContext } from '@askme/lib-common';

  interface Props {
    /** @prop {string} messageId - ChatBot的消息Id. */
    messageId: string;
    /** @prop {boolean} isDislikeDialogOpen -  控制是否打开对话框. */
    isDislikeDialogOpen: boolean;
  }
  let { messageId, isDislikeDialogOpen = $bindable() }: Props = $props();

  let dislikeReason = $state('');
  let customReason = $state('');
  let submitReason = $derived(dislikeReason === 'other' ? customReason : dislikeReason);

  const voteMap = getVoteContext();

  function onDislike() {
    isDislikeDialogOpen = false;
    toast.promise(
      fetch(`/api/vote/like/${messageId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ like: false, reason: submitReason }),
      }),
      {
        loading: '提交反馈中',
        success: () => {
          voteMap.set(messageId, false);
          return '已提交';
        },
        error: () => {
          return '发生错误';
        },
      },
    );
    customReason = '';
    dislikeReason = '';
  }
</script>

<!--
@component
DislikeDialog是当用户点踩后的反馈对话框。

- 用法:
  ``` svelte
  <DislikeDialog
    {isDislikeDialogOpen}
    {messageId}
  />
  ```
  -->
<Dialog.Root bind:open={isDislikeDialogOpen}>
  <Dialog.Content class="sm:max-w-[425px]">
    <Dialog.Header>
      <Dialog.Title>反馈</Dialog.Title>
      <Dialog.Description>对此回答不满意的原因：</Dialog.Description>
    </Dialog.Header>
    <RadioGroup.Root bind:value={dislikeReason}>
      <div class="flex items-center space-x-2">
        <RadioGroup.Item value="有害/不安全" id="r1" />
        <Label for="r1">有害/不安全</Label>
      </div>
      <div class="flex items-center space-x-2">
        <RadioGroup.Item value="虚假信息或存在事实错误" id="r2" />
        <Label for="r2">虚假信息或存在事实错误</Label>
      </div>
      <div class="flex items-center space-x-2">
        <RadioGroup.Item value="回复与MMORKS产品无关" id="r3" />
        <Label for="r3">回复与MMORKS产品无关</Label>
      </div>
      <div class="flex items-center space-x-2">
        <RadioGroup.Item value="other" id="r4" />
        <Label for="r4">其他</Label>
      </div>
    </RadioGroup.Root>
    {#if dislikeReason === 'other'}
      <Textarea
        placeholder="请在此填写对此回答不满意的原因"
        bind:value={customReason}
        class="max-h-[100px] resize-none"
      />
    {/if}
    <Dialog.Footer>
      <Button variant="outline" onclick={() => (isDislikeDialogOpen = false)}>取消</Button>
      <Button onclick={onDislike}>提交</Button>
    </Dialog.Footer>
  </Dialog.Content>
</Dialog.Root>
