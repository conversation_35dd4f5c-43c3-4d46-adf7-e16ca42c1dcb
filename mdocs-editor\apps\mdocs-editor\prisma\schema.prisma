// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
  // You might want to adjust the output path based on your project structure
  output   = "../src/lib/generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}
model JlLibrary {
  id        Int      @id @default(autoincrement())
  name      String   @unique @db.VarChar(255)
  latestHistoryId Int? @unique @map("latest_history_id")
  latest    JlLibraryHistory? @relation("latest",fields: [latestHistoryId], references: [id])
  history   JlLibraryHistory[] @relation("histories")
}
model JlLibraryHistory {
  id        Int      @id @default(autoincrement())
  version   String   @db.VarChar(20)
  summary   String? 
  creator   String   @db.VarChar(255)
  createdAt DateTime @default(now())
  extra     Json  
  libraryId Int      @map("library_id")
  libraryWhenLatest   JlLibrary? @relation("latest")
  library   JlLibrary @relation("histories", fields: [libraryId], references: [id], onDelete: Cascade)
  functions JlFunction[] @relation("functions")
  @@unique([version, libraryId])
}

model JlFunction {
  id          Int      @id @default(autoincrement())
  historyId   Int      @map("history_id") 
  name        String   @db.VarChar(255) 
  summary     String?  
  description String?  
  jsonContent Json
  lastUpdatedBy String?  @db.VarChar(255)
  lastUpdatedAt DateTime @default(now()) @updatedAt
  createdAt     DateTime @default(now())

  history JlLibraryHistory @relation("functions", fields: [historyId], references: [id], onDelete: Cascade)
  @@index([historyId])
  @@index([name])
  @@unique([name, historyId])
}
