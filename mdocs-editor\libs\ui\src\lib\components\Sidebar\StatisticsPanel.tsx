import { ScrollArea } from "../ui/scroll-area";
import { Book<PERSON><PERSON>, Hash, Clock } from "lucide-react";

/**
 * @summary 统计信息面板的属性接口
 * @description 定义了 StatisticsPanel 组件可接收的所有属性
 * @property statisticsInfo 统计信息（可选）
 */
export interface StatisticsPanelProps {
  statisticsInfo: {
    functionCount: number;
    parameterCount: number;
    lastModified?: string;
  };
}

/**
 * @summary 统计信息面板
 * @description 展示函数库的统计信息，包括函数数量、参数总数和最后修改时间(暂定)
 * @param props StatisticsPanelProps 组件属性
 * @returns React.FC 渲染的统计信息面板
 *
 * @example
 * ```typescript
 * <StatisticsPanel
 *   statisticsInfo={{
 *     functionCount: 12,
 *     parameterCount: 37,
 *     lastModified: "2024-06-10 15:23:45"
 *   }}
 * />
 * ```
 */
export function StatisticsPanel({ statisticsInfo }: StatisticsPanelProps) {
  const stats = [
    {
      label: "函数数量",
      value: statisticsInfo.functionCount,
      icon: BookOpen,
      color: "text-blue-600 bg-blue-50",
    },
    {
      label: "参数总数",
      value: statisticsInfo.parameterCount,
      icon: Hash,
      color: "text-green-600 bg-green-50",
    }
  ];

  return (
    <ScrollArea className="h-full">
      <div className="p-4 space-y-4">
        {/* 统计卡片 */}
        <div className="grid grid-cols-1 gap-3">
          {stats.map((stat, index) => (
            <div key={index} className={`p-4 rounded-lg border ${stat.color}`}>
              <div className="flex items-center justify-between mb-2">
                <stat.icon className="w-5 h-5" />
              </div>
              <div className="text-2xl font-bold mb-1">{stat.value.toLocaleString()}</div>
              <div className="text-sm font-medium">{stat.label}</div>
            </div>
          ))}
        </div>

        {/* 最后修改时间 */}
        {statisticsInfo.lastModified && (
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <Clock className="w-4 h-4 text-gray-600" />
              <span className="font-medium text-gray-700">最后修改</span>
            </div>
            <div className="text-sm text-gray-600 break-words">
              {statisticsInfo.lastModified}
            </div>
          </div>
        )}
      </div>
    </ScrollArea>
  );
}


