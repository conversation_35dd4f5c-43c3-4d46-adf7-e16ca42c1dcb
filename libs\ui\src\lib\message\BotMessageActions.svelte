<script lang="ts">
  import MessageSwitcher from './MessageSwitcher.svelte';
  import { Button } from '$lib/components/ui/button/index.js';
  import ThumbsUp from '@lucide/svelte/icons/thumbs-up';
  import ThumbsDown from '@lucide/svelte/icons/thumbs-down';
  import Copy from '@lucide/svelte/icons/copy';
  import RefreshCw from '@lucide/svelte/icons/refresh-cw';
  import * as Tooltip from '$lib/components/ui/tooltip/index.js';

  interface Props {
    /** @prop {(event: MouseEvent) => void} onCopy - 复制按钮的回调函数 */
    onCopy: (event: MouseEvent) => void;
    /** @prop {(event: MouseEvent) => void} onLike - 点赞按钮的回调函数 */
    onLike: (event: MouseEvent) => void;
    /** @prop {(event: MouseEvent) => void} onDislike - 点踩按钮的回调函数 */
    onDislike: (event: MouseEvent) => void;
    /** @prop {(event: MouseEvent) => void} onReload - 重新加载按钮的回调函数 */
    onReload: (event: MouseEvent) => void;
    /** @prop {'noAction' | 'liked' | 'disliked'} likeStatus - 是否已经赞过：'noAction',无操作;'liked',已点赞;'disliked',已点踩 */
    likeStatus: 'noAction' | 'liked' | 'disliked';
    /** @prop {string} messageId - 该MessageAction对应的消息Id. */
    messageId: string;
  }
  const { messageId, onCopy, onLike, onDislike, onReload, likeStatus }: Props = $props();
</script>

<!--
@component
BotMessageActions是LLM回复下方的按钮。

- 用法:
  ``` svelte
  <BotMessageActions
    onCopy={() => console.log('Copy!')}
    onLike={() => console.log('Like!')}
    onDislike={() => console.log('Dislike')}
    {likeStatus}
  />
  ```
  -->
<div class="flex flex-row gap-2">
  <MessageSwitcher {messageId} />

  <Tooltip.Provider>
    <Tooltip.Root>
      <Tooltip.Trigger>
        {#snippet child({ props })}
          <Button
            {...props}
            class="text-muted-foreground pointer-events-auto! h-fit px-2 py-1"
            variant="outline"
            onclick={onCopy}
          >
            <Copy size={16} />
          </Button>
        {/snippet}
      </Tooltip.Trigger>
      <Tooltip.Content>
        <p>复制</p>
      </Tooltip.Content>
    </Tooltip.Root>
  </Tooltip.Provider>

  <Tooltip.Provider>
    <Tooltip.Root>
      <Tooltip.Trigger>
        {#snippet child({ props })}
          <Button
            {...props}
            class="text-muted-foreground pointer-events-auto! h-fit px-2 py-1"
            variant="outline"
            onclick={onReload}
          >
            <RefreshCw size={16} />
          </Button>
        {/snippet}
      </Tooltip.Trigger>
      <Tooltip.Content>
        <p>重新生成</p>
      </Tooltip.Content>
    </Tooltip.Root>
  </Tooltip.Provider>

  <Tooltip.Provider>
    <Tooltip.Root>
      <Tooltip.Trigger>
        {#snippet child({ props })}
          <Button
            {...props}
            class="text-muted-foreground pointer-events-auto! h-fit px-2 py-1"
            variant="outline"
            onclick={onLike}
            disabled={likeStatus === 'liked'}
          >
            <ThumbsUp
              size={16}
              fill={likeStatus === 'liked' ? 'var(--color-foreground)' : 'var(--color-background)'}
            />
          </Button>
        {/snippet}
      </Tooltip.Trigger>
      <Tooltip.Content>
        <p>喜欢</p>
      </Tooltip.Content>
    </Tooltip.Root>
  </Tooltip.Provider>

  <Tooltip.Provider>
    <Tooltip.Root>
      <Tooltip.Trigger>
        {#snippet child({ props })}
          <Button
            {...props}
            class="text-muted-foreground pointer-events-auto! h-fit px-2 py-1"
            variant="outline"
            onclick={onDislike}
            disabled={likeStatus === 'disliked'}
          >
            <ThumbsDown
              size={16}
              fill={likeStatus === 'disliked'
                ? 'var(--color-foreground)'
                : 'var(--color-background)'}
            />
          </Button>
        {/snippet}
      </Tooltip.Trigger>
      <Tooltip.Content>
        <p>不喜欢</p>
      </Tooltip.Content>
    </Tooltip.Root>
  </Tooltip.Provider>
</div>
