{"name": "@askme/lib-common", "private": true, "version": "1.1.0", "main": "src/index.ts", "exports": {".": "./src/index.ts", "./logformat": "./src/logformat.ts", "./chat": "./src/Chat/chat.svelte.ts"}, "scripts": {"build": "tsc", "format": "prettier --write .", "check": "tsc --noEmit", "lint": "prettier --check . && eslint .", "test": "vitest"}, "devDependencies": {"svelte": "5.33.19", "typescript": "^5.8.3"}, "type": "module", "dependencies": {"@ai-sdk/provider": "^1.1.3", "@ai-sdk/provider-utils": "^2.2.8", "@ai-sdk/ui-utils": "^1.2.11", "@types/js-cookie": "^3.0.6", "ai": "^4.3.16", "js-cookie": "^3.0.5", "zod": "^3.25.58"}}