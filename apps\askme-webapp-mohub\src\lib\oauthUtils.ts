import { config } from '$lib/config.server';
import { PrismaClient } from '$lib/generated/prisma';
import type { Session } from '$lib/generated/prisma';
import * as LogFormat from '@askme/lib-common/logformat';
import serverCtx from '$lib/context.server';
import type { UserInfo } from './types';

/**
 * 同元oauth的access token刷新逻辑
 */
async function refreshTongYuanAccessToken(refreshToken: string): Promise<{
  accessToken: string;
  refreshToken: string;
  expiresAt: Date;
}> {
  const refreshURL = new URL('/authentication/oauth2/token', config.oauth.baseurl);
  const authHeader = buildOauthAuthorizationHeader(
    config.oauth.application_id,
    config.oauth.application_secret,
  );
  const formData = new FormData();
  formData.append('grant_type', 'refresh_token');
  formData.append('refresh_token', refreshToken);
  let tokenResponseJson: {
    code: number;
    data: {
      access_token: string;
      expires_in: number;
      refresh_token: string;
      id_token: string;
      scope: string;
      token_type: string;
    };
    message: string;
    time: number;
  };
  try {
    const response = await fetch(refreshURL, {
      method: 'POST',
      headers: {
        Authorization: authHeader,
      },
      body: formData,
    });
    tokenResponseJson = await response.json();
    if (!response.ok) {
      throw new Error('Failed to validate authorization code.');
    }
  } catch (error) {
    throw new Error(`Failed to validate authorization code:${error}`);
  }
  const MS_IN_S = 1000;
  return {
    accessToken: tokenResponseJson.data.access_token,
    refreshToken: tokenResponseJson.data.refresh_token,
    expiresAt: new Date(Date.now() + tokenResponseJson.data.expires_in * MS_IN_S),
  };
}

/**
 * Keycloak oauth的access token刷新逻辑
 */
async function refreshKeycloakAccessToken(refreshToken: string): Promise<{
  accessToken: string;
  refreshToken: string;
  expiresAt: Date;
}> {
  const refreshURL = new URL('/protocol/openid-connect/token', config.oauth.baseurl);
  const authHeader = buildOauthAuthorizationHeader(
    config.oauth.application_id,
    config.oauth.application_secret,
  );
  const formData = new FormData();
  formData.append('grant_type', 'refresh_token');
  formData.append('refresh_token', refreshToken);

  let tokenResponseJson: {
    access_token: string;
    expires_in: number;
    refresh_token: string;
    token_type: string;
    scope?: string;
  };

  try {
    const response = await fetch(refreshURL, {
      method: 'POST',
      headers: {
        Authorization: authHeader,
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: formData,
    });
    tokenResponseJson = await response.json();
    if (!response.ok) {
      throw new Error('Failed to refresh Keycloak access token.');
    }
  } catch (error) {
    throw new Error(`Failed to refresh Keycloak access token:${error}`);
  }

  const MS_IN_S = 1000;
  return {
    accessToken: tokenResponseJson.access_token,
    refreshToken: tokenResponseJson.refresh_token,
    expiresAt: new Date(Date.now() + tokenResponseJson.expires_in * MS_IN_S),
  };
}

/**
 * 验证用户OAuth access token,如有需要续期access token并更新session对象. 任何错误都返回null
 * @param prisma prisma客户端
 * @param session prisma session对象
 * @returns sessionWithUser，若验证失败返回null
 */
export async function validateOAuthAccessToken(
  prisma: PrismaClient,
  session: Session | null,
  oauthProvider: string,
): Promise<Session | null> {
  if (!session || !session.oauthExpiresAt || !session.refreshToken) {
    return null;
  }
  // 用户Oauth token续期，若刷新access token失败视为认证失效，删除session，退出登陆。若出现不支持的oauth提供者，抛出异常
  if (Date.now() >= session.oauthExpiresAt.getTime()) {
    if (oauthProvider === 'GITLAB') {
      if (!serverCtx.oauth) {
        throw new Error('OAuth client not initialized,please check OAUTH_PROVIDER in .env file');
      }
      let newTokens;
      try {
        newTokens = await serverCtx.oauth.gitlab.refreshAccessToken(session.refreshToken);
      } catch (error) {
        console.error(
          LogFormat.error(
            'Failed to refresh GitLab access token:' + error + '. sessionId:' + session.id,
          ),
        );
        return null;
      }
      session.accessToken = newTokens.accessToken();
      session.refreshToken = newTokens.refreshToken();
      session.oauthExpiresAt = newTokens.accessTokenExpiresAt();
    } else if (oauthProvider === 'TONGYUAN') {
      let newTokens;
      try {
        newTokens = await refreshTongYuanAccessToken(session.refreshToken);
      } catch (error) {
        console.error(
          LogFormat.error(
            'Failed to refresh TongYuan access token:' + error + '. sessionId:' + session.id,
          ),
        );
        return null;
      }
      session.accessToken = newTokens.accessToken;
      session.refreshToken = newTokens.refreshToken;
      session.oauthExpiresAt = newTokens.expiresAt;
    } else if (oauthProvider === 'KEYCLOAK') {
      let newTokens;
      try {
        newTokens = await refreshKeycloakAccessToken(session.refreshToken);
      } catch (error) {
        console.error(
          LogFormat.error(
            'Failed to refresh Keycloak access token:' + error + '. sessionId:' + session.id,
          ),
        );
        return null;
      }
      session.accessToken = newTokens.accessToken;
      session.refreshToken = newTokens.refreshToken;
      session.oauthExpiresAt = newTokens.expiresAt;
    } else {
      throw new Error('Unsupported OAUTH_PROVIDER: ' + config.oauth.provider);
    }

    // 申请到新access token后，写入数据库
    try {
      await prisma.session.update({
        where: {
          id: session.id,
        },
        data: {
          accessToken: session.accessToken,
          refreshToken: session.refreshToken,
          oauthExpiresAt: session.oauthExpiresAt,
        },
      });
    } catch (error) {
      // 数据库更新OAuth token失败，log报错，退出登陆
      console.error(
        LogFormat.error('Failed to update session:' + error + '. sessionId:' + session.id),
      );
      return null;
    }
  }

  return session;
}

export function buildOauthAuthorizationHeader(applicationId: string, applicationSecret: string) {
  return `Basic ${Buffer.from(`${applicationId}:${applicationSecret}`).toString('base64')}`;
}

/**
 * 根据OAuth提供者和access token,从OAuth提供者获取用户信息,获取失败将抛出异常
 * @param provider OAuth提供者
 * @param accessToken OAuth access token
 * @returns 用户信息对象
 */
export async function getUserInfoByOAuthProvider(
  provider: string,
  accessToken: string,
): Promise<UserInfo> {
  const oauthUserInfoResponse = await fetch(config.oauth.userinfo_url, {
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
  });

  if (!oauthUserInfoResponse.ok) {
    throw new Error('Failed to fetch user info.');
  }

  if (config.oauth.provider.toUpperCase() === 'GITLAB') {
    const gitlabUser = await oauthUserInfoResponse.json();
    return {
      UID: gitlabUser.sub,
      username: gitlabUser.name,
      email: gitlabUser.email,
      provider: 'GITLAB',
    } as UserInfo;
  } else if (config.oauth.provider.toUpperCase() === 'TONGYUAN') {
    const tongyuanUser: {
      code: number;
      data: {
        sub: string;
        openid: string;
        roles: string[];
        nickname: string;
        initPassword: string;
        email?: string;
        status: boolean;
      };
      message: string;
      time: number;
    } = await oauthUserInfoResponse.json();
    if (tongyuanUser.code !== 200) {
      throw new Error('Failed to fetch user info.');
    }
    return {
      UID: tongyuanUser.data.openid,
      username: tongyuanUser.data.nickname,
      email: tongyuanUser.data.email,
      provider: 'TONGYUAN',
    } as UserInfo;
  } else if (config.oauth.provider.toUpperCase() === 'KEYCLOAK') {
    const keycloakUser: {
      sub: string;
      name?: string;
      preferred_username?: string;
      email?: string;
      email_verified?: boolean;
    } = await oauthUserInfoResponse.json();
    return {
      UID: keycloakUser.sub,
      username: keycloakUser.preferred_username || keycloakUser.name || keycloakUser.sub,
      email: keycloakUser.email,
      provider: 'KEYCLOAK',
    } as UserInfo;
  } else {
    throw new Error('Unsupported OAUTH_PROVIDER: ' + provider);
  }
}
