import { ScrollArea } from "../ui/scroll-area";
import { AlertCircle, Check, AlertTriangle, Shield } from "lucide-react";

/**
 * @summary 校验结果面板的属性接口
 * @description 定义了 ValidationPanel 组件可接收的所有属性
 * @property validationInfo 校验信息（可选）
 */
export interface ValidationPanelProps {
  validationInfo?: {
    errors: string[];
    warnings: string[];
  } | null;
}

/**
 * @summary 校验结果面板
 * @description 展示函数库的校验结果，包括错误和警告
 * @param props ValidationPanelProps 组件属性
 * @returns React.FC 渲染的校验结果面板
 *
 */
export function ValidationPanel({ validationInfo }: ValidationPanelProps) {
  // 计算问题总数（仅在有校验信息时）
  const totalIssues = validationInfo ? validationInfo.errors.length + validationInfo.warnings.length : 0;

  return (
    <ScrollArea className="h-full">
      <div className="p-4 space-y-4">
        {!validationInfo ? (
          // 未进行校验的状态
          <div className="text-center text-gray-500 py-12">
            <span className="text-sm font-medium text-gray-600">等待校验</span>
            <br></br>
            <span className="text-xs text-gray-400 mt-1">请先进行函数校验</span>
          </div>
        ) : totalIssues === 0 ? (
          // 校验通过的状态
          <div className="text-center text-gray-500 py-12">
            <Check className="w-6 h-6 mx-auto text-green-500 mb-2" />
            <span className="text-sm font-medium text-green-600">校验通过</span>
            <br></br>
            <span className="text-xs text-gray-400 mt-1">未发现问题</span>
          </div>
        ) : (
          // 有问题的状态
          <>
            {/* 概览 */}
            <div className="bg-gray-50 p-3 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <Shield className="w-4 h-4 text-gray-600" />
                <span className="font-medium text-gray-700">校验概览</span>
              </div>
              <div className="flex gap-4 text-sm">
                <span className="text-red-600">
                  {validationInfo.errors.length} 错误
                </span>
                <span className="text-yellow-600">
                  {validationInfo.warnings.length} 警告
                </span>
              </div>
            </div>

            {/* 错误列表 */}
            {validationInfo.errors.length > 0 && (
              <div className="space-y-2">
                <h4 className="font-medium text-red-600 flex items-center gap-2">
                  <AlertCircle className="w-4 h-4" />
                  错误 ({validationInfo.errors.length})
                </h4>
                <div className="space-y-2">
                  {validationInfo.errors.map((error, index) => (
                    <div key={index} className="flex items-start gap-3 p-3 bg-red-50 border border-red-200 rounded-lg">
                      <AlertCircle className="w-4 h-4 text-red-500 mt-0.5 flex-shrink-0" />
                      <div className="flex-1 min-w-0">
                        <span className="text-sm text-red-700 break-words leading-relaxed">{error}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
            
            {/* 警告列表 */}
            {validationInfo.warnings.length > 0 && (
              <div className="space-y-2">
                <h4 className="font-medium text-yellow-600 flex items-center gap-2">
                  <AlertTriangle className="w-4 h-4" />
                  警告 ({validationInfo.warnings.length})
                </h4>
                <div className="space-y-2">
                  {validationInfo.warnings.map((warning, index) => (
                    <div key={index} className="flex items-start gap-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                      <AlertTriangle className="w-4 h-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                      <div className="flex-1 min-w-0">
                        <span className="text-sm text-yellow-700 break-words leading-relaxed">{warning}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </>
        )}
      </div>
    </ScrollArea>
  );
}
