<script lang="ts">
  import ChatPage from '$lib/components/ChatPage.svelte';
  import { convertMsg } from '$lib/utils';
  import type { PageProps } from './$types';
  import { buildMessageTree, getChatIdContext } from '@askme/lib-common';

  const { data }: PageProps = $props();

  // 新建对话时，重设chatId上下文为undefined
  $effect(() => {
    const chatIdContext = getChatIdContext();
    chatIdContext().chatId = data.chatId;
  });
</script>

<ChatPage
  chatId={data.chatId}
  messageTreeRoot={buildMessageTree(data.messages, convertMsg)}
  suggestedActions={data.suggestedActions}
  isSSO={data.isSSO}
  enableThemeSwitcher={data.enableThemeSwitcher}
  overviewTitle={data.defaultTitle}
/>
