<script lang="ts">
  import { Button } from '$lib/components/ui/button/index.js';
  import ChevronLeft from '@lucide/svelte/icons/chevron-left';
  import ChevronRight from '@lucide/svelte/icons/chevron-right';
  import { getChatIdContext, getChatContext, insertAction } from '@askme/lib-common';

  interface Props {
    /** @prop {string} messageId - 消息id. */
    messageId: string;
  }

  const { messageId }: Props = $props();

  const chatIdContext = getChatIdContext();
  const keyedChatStore = getChatContext();

  const chatId = $derived(chatIdContext().chatId);
  const chatStore = $derived(chatId ? keyedChatStore.get(chatId) : null);
  const infoMap = $derived(chatStore?.messages.infoMap);
  const parentMsgId = $derived(infoMap?.get(messageId)?.parentMsgId);
  const totalMsgCount = $derived(parentMsgId ? (infoMap?.get(parentMsgId)?.childCount ?? 1) : 1);
  const switchActions = $derived(chatStore?.switchActions);
  const curMsgIndex = $derived(
    switchActions?.find((a) => a.messageId === parentMsgId)?.childIndex ?? 0,
  );

  function onRight() {
    if (chatStore && parentMsgId && infoMap) {
      chatStore.switchActions = insertAction(
        chatStore.switchActions,
        { messageId: parentMsgId, childIndex: curMsgIndex + 1 },
        infoMap,
      );
    }
  }

  function onLeft() {
    if (chatStore && parentMsgId && infoMap) {
      chatStore.switchActions = insertAction(
        chatStore.switchActions,
        { messageId: parentMsgId, childIndex: curMsgIndex - 1 },
        infoMap,
      );
    }
  }

  let displayMsgIndex = $derived(curMsgIndex + 1);
</script>

<!--
@component
MessageSwitcher用于切换多次生成的用户消息。

- 用法:
  ``` svelte
  <MessageSwitcher
    {messageId}
  />
  ```
-->
{#if totalMsgCount > 1}
  <Button
    class="text-muted-foreground pointer-events-auto! h-fit px-1 py-1"
    variant="ghost"
    disabled={displayMsgIndex === 1}
    onclick={onLeft}
  >
    <ChevronLeft size={16} />
  </Button>

  {displayMsgIndex}/{totalMsgCount}

  <Button
    class="text-muted-foreground pointer-events-auto! h-fit px-1 py-1"
    variant="ghost"
    disabled={displayMsgIndex === totalMsgCount}
    onclick={onRight}
  >
    <ChevronRight size={16} />
  </Button>
{/if}
