import type { OrderedMessage } from '.';
import type { UIMessage } from 'ai';
import type { SwitchAction } from './switchActions';

/** Message节点对应存放的节点信息 */
export interface TreeNodeInfo {
  /** 记录节点在树中的层数 */
  level: number;
  /** 记录父节点MessageId */
  parentMsgId: string | null;
  /** 记录子节点的数量
   * （⚠️之所以不从child.length计算，是因为在前端的线性消息列表是UIMessage类型，实际上不会保存child，能拿到的只有自己的Message id）*/
  childCount: number;
}

// ⚠️：树的操作记录使用外部数据结构SwitchAction[]维护
// 这是为了分离用户UI操作和数据保存的关注点，保持message tree数据保存和用户操作记录的纯粹性

/** 前端的Message树节点 */
export type MessageTreeNode = UIMessage & {
  /** 树节点的子节点，下一条后继消息。（包括重新生成的消息） */
  child: MessageTreeNode[];
};

/** 前端的Message树根节点
 * 尽量使用封装好的函数去操作MessageTreeRoot（除非你看过MessageTree的实现知道自己在做什么）
 */
export type MessageTreeRoot = {
  /** 树的根节点的子节点 */
  child: MessageTreeNode[];
  // ⚠️注意用$state初始化一个MessageTreeRoot时，下面这个Map不是响应式的，这符合预期
  // ⚠️按道理可以$derived去响应式的更新。但是考虑到:
  // 1.每次递归可能导致比较高的性能开销
  // 2.实际上只需要在messages初始化时执行一次dfs构建infoMap，之后插入节点时更新map即可
  // 因此这个属性的更改实际上是手动维护的，在使用时注意这一点
  /** 为每个MessageId记录信息 */
  infoMap: Map<string | null, TreeNodeInfo>;
  // ⚠️这里之所以不使用一个nodeMap去实现o(1)的节点查找
  // 是因为svlete不会代理map对象，nodeMap和child中实际维护了不同的node对象引用
  // 这容易造成数据不一致性，占用双倍内存空间，提高数据结构维护难度
  // 所有的messageNode都通过findMessageNodeById去查找
};

/**
 * 初始化空的MessageTree
 * @returns 空的MessageTree
 */
export function initMessageTree(): MessageTreeRoot {
  return {
    child: [],
    infoMap: new Map<string | null, TreeNodeInfo>([
      [null, { level: 0, parentMsgId: null, childCount: 0 }],
    ]),
  };
}

/** 从有向的消息列表构建MessageTreeNode树 */
export function buildMessageTree<T extends OrderedMessage>(
  messages: T[],
  convertFunc: (msg: T) => UIMessage,
): MessageTreeRoot {
  const root = initMessageTree();
  if (messages.length === 0) return root;

  const tempNodeMap = new Map<string, MessageTreeNode>();

  messages.forEach((msg) => {
    const node: MessageTreeNode = {
      ...convertFunc(msg),
      child: [],
    };
    tempNodeMap.set(msg.id, node);
  });

  // 用于统计每个节点的子节点数量
  const childCountMap = new Map<string, number>();

  messages.forEach((msg) => {
    const currentNode = tempNodeMap.get(msg.id)!;

    if (msg.preId) {
      const parentNode = tempNodeMap.get(msg.preId);
      if (parentNode) {
        parentNode.child.push(currentNode);
        // 更新父节点的子节点计数
        childCountMap.set(msg.preId, (childCountMap.get(msg.preId) || 0) + 1);
        root.infoMap.set(currentNode.id, {
          level: (root.infoMap.get(parentNode.id)?.level ?? 1) + 1,
          parentMsgId: parentNode.id,
          childCount: 0, // 初始化为0，后面会更新
        });
      }
    } else {
      root.child.push(currentNode);
      root.infoMap.set(currentNode.id, {
        level: 1,
        parentMsgId: null,
        childCount: 0, // 初始化为0，后面会更新
      });
    }
  });

  // 更新所有节点的childCount
  childCountMap.forEach((count, nodeId) => {
    const nodeInfo = root.infoMap.get(nodeId);
    if (nodeInfo) {
      nodeInfo.childCount = count;
    }
  });

  // 更新根节点的childCount
  const rootInfo = root.infoMap.get(null);
  if (rootInfo) {
    rootInfo.childCount = root.child.length;
  }

  return root;
}

/** 向MessageTree中插入新节点 */
export function insertMessageNode(
  root: MessageTreeRoot,
  message: UIMessage,
  parentId: string | null,
) {
  const existingNode = findMessageNodeById(message.id, root);
  if (existingNode) {
    throw new Error(`Message with id ${message.id} already exists in the tree`);
  }

  const newNode: MessageTreeNode = {
    ...message,
    child: [],
  };

  if (parentId === null || parentId.length === 0) {
    root.child.push(newNode);
    root.infoMap.set(newNode.id, {
      level: 1,
      parentMsgId: null,
      childCount: 0,
    });
    // 更新根节点的childCount
    const rootInfo = root.infoMap.get(null);
    if (rootInfo) {
      rootInfo.childCount = root.child.length;
    }
  } else {
    const parentNode = findMessageNodeById(parentId, root);
    if (!parentNode) {
      throw new Error(`Parent node with id ${parentId} not found in the tree`);
    }
    parentNode.child.push(newNode);
    // 更新新节点的信息
    root.infoMap.set(newNode.id, {
      level: (root.infoMap.get(parentId)?.level ?? 1) + 1,
      parentMsgId: parentId,
      childCount: 0,
    });
    // 更新父节点的childCount
    const parentInfo = root.infoMap.get(parentId);
    if (parentInfo) {
      parentInfo.childCount = parentNode.child.length;
    }
  }

  return root;
}

/** 更新MessageTree中已有的节点的内容（注意只改内容，不改树结构） */
export function updateMessageNode(root: MessageTreeRoot, message: UIMessage) {
  const existingNode = findMessageNodeById(message.id, root);
  if (!existingNode) {
    throw new Error(`Message with id ${message.id} not found in the tree`);
  }

  Object.assign(existingNode, {
    ...message,
    child: existingNode.child,
  });
}
/** 将MessageTree转换为前端看到的线性记录
 * @param root MessageTree的根节点
 * @param switchActions 用户的切换动作记录
 * @returns 前端看到的线性聊天记录(⚠️返回UIMessage[]而不是MessageTreeNode[]以避免序列化时的循环引用错误)
 */
export function toLinearRecord(root: MessageTreeRoot, switchActions: SwitchAction[]): UIMessage[] {
  const result: MessageTreeNode[] = [];
  // 如果根节点没有子节点，直接返回空数组
  if (root.child.length === 0) {
    return result;
  }
  let currentNode: MessageTreeNode | MessageTreeRoot = root;
  let nextNodeIndex = 0;
  let currentActionIndex = 0;

  while (true) {
    // 确定下一个节点的索引
    if (currentActionIndex < switchActions.length) {
      const currentAction = switchActions[currentActionIndex];

      // 检查当前操作是否匹配当前节点
      if (
        currentAction.messageId ===
        (currentNode === root ? null : (currentNode as MessageTreeNode).id)
      ) {
        currentActionIndex++;
        // 验证索引是否有效
        const childNodes = currentNode.child;
        if (currentAction.childIndex < childNodes.length && currentAction.childIndex >= 0) {
          nextNodeIndex = currentAction.childIndex;
        }
      }
    }
    if (currentNode.child.length === 0) {
      break;
    }

    // 移动到下一个节点并记录
    currentNode = currentNode.child[nextNodeIndex];
    result.push(currentNode as MessageTreeNode);

    // 重置下一个节点的索引为0(用于下一次迭代)
    nextNodeIndex = 0;
  }
  // 过滤掉child属性，返回UIMessage[]。这里parent和child的unused是预期行为。
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  return result.map(({ child, ...uiMessage }) => uiMessage as UIMessage);
}

/**
 * 根据id查找树节点
 * @param id message id
 * @param root 树的根节点
 * @returns 找到的节点，如果没有找到返回null
 */
export function findMessageNodeById(id: string, root: MessageTreeRoot): MessageTreeNode | null {
  // 遍历根节点下的所有子节点
  for (const rootNode of root.child) {
    // 检查当前根节点
    if (rootNode.id === id) {
      return rootNode;
    }

    // 广度优先搜索子节点
    const queue: MessageTreeNode[] = [...rootNode.child];
    while (queue.length > 0) {
      const node = queue.shift()!;
      if (node.id === id) {
        return node;
      }
      queue.push(...node.child);
    }
  }

  return null;
}

export function isNodeExist(root: MessageTreeRoot, id: string): boolean {
  return root.infoMap.has(id);
}
