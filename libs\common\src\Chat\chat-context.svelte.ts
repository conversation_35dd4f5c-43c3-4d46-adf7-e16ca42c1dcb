import type { J<PERSON>NValue } from '@ai-sdk/ui-utils';
import { KeyedStore } from './utils.svelte';
import { initMessageTree, toLinearRecord, type MessageTreeRoot } from '../messageTree';
import { type SwitchAction } from '../switchActions';
import { getContext, setContext, hasContext } from 'svelte';

// 为每个Chat维护的状态
export class ChatStore {
  messages = $state<MessageTreeRoot>(initMessageTree());
  data = $state<JSONValue[]>();
  status = $state<'submitted' | 'streaming' | 'ready' | 'error'>('ready');
  error = $state<Error>();
  switchActions = $state<SwitchAction[]>([]);
  linearMessages = $derived(toLinearRecord(this.messages, this.switchActions));
}

export class KeyedChatStore extends KeyedStore<ChatStore> {
  constructor(value?: Iterable<readonly [string, ChatStore]> | null | undefined) {
    super(ChatStore, value);
  }
}

const key = Symbol('chat');

export function setChatContext(keyedChatStore: KeyedChatStore) {
  setContext(key, keyedChatStore);
}

export function getChatContext() {
  return getContext(key) as KeyedChatStore;
}

export function hasChatContext() {
  return hasContext(key);
}
