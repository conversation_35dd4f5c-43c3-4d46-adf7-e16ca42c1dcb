<script>
  import PanelLeft from '@lucide/svelte/icons/panel-left';
  import { Button } from '$lib/components/ui/button';
  import { useSidebar } from '$lib/components/ui/sidebar';
  import { Tooltip, TooltipContent, TooltipTrigger } from '$lib/components/ui/tooltip';
  import MessageSquarePlus from '@lucide/svelte/icons/message-square-plus';
  import { goto } from '$app/navigation';
  import { innerWidth } from 'svelte/reactivity/window';
  const sidebar = useSidebar();
</script>

<Tooltip>
  <TooltipTrigger>
    {#snippet child({ props })}
      <Button
        {...props}
        onclick={() => {
          sidebar.toggle();
        }}
        variant="outline"
        class="md:h-fit md:px-2"
      >
        <PanelLeft />
      </Button>
    {/snippet}
  </TooltipTrigger>
  <TooltipContent align="start">侧边栏</TooltipContent>
</Tooltip>

{#if !sidebar.open || (innerWidth.current ?? 768) < 768}
  <Button
    variant="outline"
    class="h-fit px-2"
    onclick={() => {
      goto('/chat', { invalidate: ['app:index'] });
    }}
  >
    <MessageSquarePlus />
    <span>新对话</span>
  </Button>
{/if}
