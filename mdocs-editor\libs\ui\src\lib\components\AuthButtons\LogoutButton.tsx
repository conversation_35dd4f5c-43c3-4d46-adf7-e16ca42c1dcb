import { LogOut } from "lucide-react";
import { Button } from "../ui/button";

export interface LogoutButtonProps {
  /** 登出按钮跳转的URL */
  url: string;
  /** 登出按钮内容 */
  content?: string;
  /** 按钮样式变体 */
  variant?: "default" | "outline" | "secondary" | "ghost" | "link" | "destructive";
  /** 是否禁用 */
  disabled?: boolean;
  /** 自定义类名 */
  className?: string;
}

/**
 * LogoutButton 用于展示登出按钮。
 * 
 * @example
 * ```tsx
 * <LogoutButton 
 *   url="/logout" 
 *   content="登出"
 * />
 * ```
 */
export function LogoutButton({
  url,
  content = "登出",
  variant = "outline",
  disabled = false,
  className = "",
}:LogoutButtonProps){
  return (
    <Button 
      variant={variant} 
      disabled={disabled}
      className={className}
      onClick={() => window.location.href = url}
    >
        <LogOut 
          size={20} 
          className={"mr-2"}
        />
      
      {content}
    </Button>
  );
}; 
export default LogoutButton;