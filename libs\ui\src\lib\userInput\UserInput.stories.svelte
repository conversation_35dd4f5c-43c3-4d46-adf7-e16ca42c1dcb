<script module lang="ts">
  //    👆 notice the module context, defineMeta does not work in a regular <script> tag - instance
  import { defineMeta } from '@storybook/addon-svelte-csf';
  import UserInput from './UserInput.svelte';
  import { Chat } from '@askme/lib-common/chat';

  //      👇 Get the Story component from the return value
  const { Story } = defineMeta({
    title: 'UI/UserInput',
    component: UserInput,
    decorators: [],
    parameters: {},
  });
</script>

<script lang="ts">
  const chat = new Chat();
</script>

<Story name="Default" args={{ chat, MAX_FILES: 5, MAX_FILE_SIZE_IN_MB: 10 }} />
