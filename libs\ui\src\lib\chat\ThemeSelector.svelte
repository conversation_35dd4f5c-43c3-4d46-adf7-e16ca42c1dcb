<script lang="ts">
  import SunIcon from '@lucide/svelte/icons/sun';
  import MoonIcon from '@lucide/svelte/icons/moon';

  import { setMode, setTheme } from 'mode-watcher';
  import * as DropdownMenu from '$lib/components/ui/dropdown-menu/index.js';
  import { buttonVariants } from '$lib/components/ui/button/index.js';
</script>

<DropdownMenu.Root>
  <DropdownMenu.Trigger class={buttonVariants({ variant: 'outline', size: 'icon' })}>
    <SunIcon
      class="h-[1.2rem] w-[1.2rem] scale-100 rotate-0 !transition-all dark:scale-0 dark:-rotate-90"
    />
    <MoonIcon
      class="absolute h-[1.2rem] w-[1.2rem] scale-0 rotate-90 !transition-all dark:scale-100 dark:rotate-0"
    />
    <span class="sr-only">Toggle theme</span>
  </DropdownMenu.Trigger>
  <DropdownMenu.Content align="end">
    <DropdownMenu.Item
      onclick={() => {
        setMode('light');
        setTheme('default');
      }}>白色主题</DropdownMenu.Item
    >
    <DropdownMenu.Item
      onclick={() => {
        setMode('light');
        setTheme('orange');
      }}>橙色主题</DropdownMenu.Item
    >
    <DropdownMenu.Item
      onclick={() => {
        setMode('dark');
        setTheme('default');
      }}>黑色主题</DropdownMenu.Item
    >
  </DropdownMenu.Content>
</DropdownMenu.Root>
