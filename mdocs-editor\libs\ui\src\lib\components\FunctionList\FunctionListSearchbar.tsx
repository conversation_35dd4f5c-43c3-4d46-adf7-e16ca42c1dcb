import { Search, X } from 'lucide-react';
import { Input } from '../ui/input';
import { Button } from '../ui/button';

interface FunctionListSearchbarProps {
  /** 搜索关键词 */
  searchTerm: string;
  /** 搜索变化回调 */
  onSearchChange: (term: string) => void;
  /** 占位符文本 */
  placeholder?: string;
}

/**
 * FunctionListSearchbar 组件
 * 
 * 搜索栏，支持函数名称和摘要搜索
 */
export function FunctionListSearchbar({
  searchTerm,
  onSearchChange,
  placeholder = "搜索函数名称或摘要..."
}: FunctionListSearchbarProps) {
  // 清除搜索
  const handleClear = () => {
    onSearchChange('');
  };

  return (
    <div className="relative flex-1">
      {/* 搜索图标 */}
      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />

      {/* 搜索输入框 */}
      <Input
        type="text"
        placeholder={placeholder}
        value={searchTerm}
        onChange={(e) => onSearchChange(e.target.value)}
        className="pl-10 pr-10 h-9 text-sm"
      />

      {/* 清除按钮 */}
      {searchTerm && (
        <Button
          variant="ghost"
          size="sm"
          onClick={handleClear}
          className="absolute right-1 top-1/2 transform -translate-y-1/2 h-7 w-7 p-0 hover:bg-gray-100"
        >
          <X className="h-3 w-3" />
        </Button>
      )}
    </div>
  );
}
