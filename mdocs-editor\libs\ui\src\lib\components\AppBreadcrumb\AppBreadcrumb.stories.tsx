import type { <PERSON>a, StoryObj } from "@storybook/react-vite";
import { AppBreadcrumb } from "./AppBreadcrumb";

const meta: Meta<typeof AppBreadcrumb> = {
  title: "Components/AppBreadcrumb",
  component: AppBreadcrumb,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    items: [{ label: "主页", href: "/" }],
  },
};

export const CurrentPageWithVersion: Story = {
  args: {
    items: [
      { label: "主页", href: "/" },
      { label: "函数库概览"},
      {
        label: "lib1",
        versionSelector: {
          value: "latest",
          options: [
            { value: "latest", label: "latest" },
            { value: "v1.0.0", label: "v1.0.0" },
            { value: "v0.9.0", label: "v0.9.0" },
          ],
          onVersionChange: (value: string) => {
            console.log("Version changed:", value);
          },
        }
      },
    ],
  },
};

export const MiddlePageWithVersion: Story = {
  args: {
    items: [
      { label: "主页", href: "/" },
      { label: "函数库概览", href: "/function-library" },
      {
        label: "lib1",
        href: "/function-library/lib1",
        versionSelector: {
          value: "v1.0.0",
          options: [
            { value: "latest", label: "latest" },
            { value: "v1.0.0", label: "v1.0.0" },
            { value: "v0.9.0", label: "v0.9.0" },
          ],
          onVersionChange: (value: string) => {
            console.log("Version changed:", value);
          },
        }
      },
      { label: "mean 函数" }, // 没有 href，所以不可点击
    ],
  },
};
