import { Button } from "../ui/button";

export interface LoginButtonProps {
  /** 登录按钮跳转的URL */
  url: string;
  /** 登录按钮内容 */
  content: string;
  /** 登录页面标题 */
  title: string;
  /** 按钮样式变体 */
  variant?: "default" | "outline" | "secondary" | "ghost" | "link" | "destructive";
  /** 是否禁用 */
  disabled?: boolean;
  /** 自定义类名 */
  className?: string;
}

/**
 * LoginButton 用于展示登录页面。
 * 
 * @example
 * ```tsx
 * <LoginButton 
 *   url="/login/gitlab" 
 *   content="GitLab 登录" 
 *   title="欢迎登录"
 * />
 * ```
 */
export function LoginButton({
  url,
  content,
  title,
  variant = "outline",
  disabled = false,
  className = "",
}: LoginButtonProps) {
  return (
    <div className="flex min-h-screen flex-col items-center justify-center">
      <h1 className="mb-8 text-2xl font-semibold">{title}</h1>
      <Button 
        variant={variant} 
        disabled={disabled}
        className={className}
        onClick={() => window.location.href = url}
      >
        {content}
      </Button>
    </div>
  );
}; 
export default LoginButton;