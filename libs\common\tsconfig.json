{"compilerOptions": {"target": "ESNext", "module": "ESNext", "outDir": "lib", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "declaration": true, "declarationDir": "lib/types", "allowImportingTsExtensions": true, "emitDeclarationOnly": true, "moduleResolution": "node"}, "include": ["src"], "exclude": ["node_modules", "**/*.spec.ts"]}