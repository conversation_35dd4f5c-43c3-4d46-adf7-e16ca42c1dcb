import FunctionLibraryOverview, { FunctionInfo } from "./FunctionLibraryOverview";

export default {
    title: "Components/FunctionLibraryOverview",
    component: FunctionLibraryOverview,
    parameters: {
        layout: "centered",
    }
};

const functions: FunctionInfo[] = [
    {
        name: "sum",
        summary: "Calculate the sum of an array.",
        score: 5,
        lastUpdatedAt: new Date("2024-06-01"),
    },
    {
        name: "average",
        summary: "Calculate the average value.",
        score: 4.5,
        lastUpdatedAt: new Date("2024-05-28"),
    },
    {
        name: "1",
        summary: "Get the maximum value.",
        score: 4,
        lastUpdatedAt: new Date("2024-05-20"),
    },
    {
        name: "2",
        summary: "Get the maximum value.",
        score: 4,
        lastUpdatedAt: new Date("2024-05-20"),
    },
    {
        name: "3",
        summary: "Get the maximum value.",
        score: 4,
        lastUpdatedAt: new Date("2024-05-20"),
    },
    {
        name: "4",
        summary: "Get the maximum value.",
        score: 4,
        lastUpdatedAt: new Date("2024-05-20"),
    },
    {
        name: "5",
        summary: "Get the maximum value.",
        score: 4,
        lastUpdatedAt: new Date("2024-05-20"),
    },
    {
        name: "6",
        summary: "Get the maximum value.",
        score: 4,
        lastUpdatedAt: new Date("2024-05-20"),
    },

    {
        name: "7",
        summary: "Get the maximum value.",
        score: 4,
        lastUpdatedAt: new Date("2024-05-20"),
    },
    {
        name: "8",
        summary: "Get the maximum value.",
        score: 4,
        lastUpdatedAt: new Date("2024-05-20"),
    },
    {
        name: "9",
        summary: "Get the maximum value.，Get the maximum value.，Get the maximum value.Get the maximum value.Get the maximum value.Get the maximum value.，Get the maximum value.",
        score: 4,
        lastUpdatedAt: new Date("2024-05-20"),
    },
    {
        name: "10",
        summary: "Get the maximum value.",
        score: 4,
        lastUpdatedAt: new Date("2024-05-20"),
    },
    {
        name: "11",
        summary: "Get the maximum value.",
        score: 4,
        lastUpdatedAt: new Date("2024-05-20"),
    }
];

export const Default = () => (
    <FunctionLibraryOverview
        title="func_lib1"
        summary="A library for data processing."
        maintainer="Alice"
        lastUpdate={new Date("2024-06-01")}
        functions={functions}
        onFunctionClick={(name: string) => {
            console.log("点击了函数名：", name);
        }}
    />
);

export const LongText = () => (
    <FunctionLibraryOverview
        title="Function Library Overview"
        summary="This is a very very very very very very very very very very long summary to test ellipsis and overflow handling in the component.This is a very very very very very very very very very very long summary to test ellipsis and overflow handling in the component.This is a very very very very very very very very very very long summary to test ellipsis and overflow handling in the component.This is a very very very very very very very very very very long summary to test ellipsis and overflow handling in the component.This is a very very very very very very very very very very long summary to test ellipsis and overflow handling in the component."
        maintainer="A very very very very very very very very long maintainer name"
        lastUpdate={new Date("2024-06-01")}
        functions={[
            {
                name: "aVeryVeryVeryVeryVeryVeryVeryVeryVeryVeryLongFunctionNameThatShouldBeTruncated",
                summary: "This is a very very very very very very very very long function summary that should be truncated in the table cell.This is a very very very very very very very very long function summary that should be truncated in the table cell.This is a very very very very very very very very long function summary that should be truncated in the table cell.This is a very very very very very very very very long function summary that should be truncated in the table cell.This is a very very very very very very very very long function summary that should be truncated in the table cell.This is a very very very very very very very very long function summary that should be truncated in the table cell.This is a very very very very very very very very long function summary that should be truncated in the table cell.This is a very very very very very very very very long function summary that should be truncated in the table cell.This is a very very very very very very very very long function summary that should be truncated in the table cell.",
                score: 5,
                lastUpdatedAt: new Date("2024-06-01"),
                deprecated :true
            },
            {
                name: "shortName",
                summary: "Short summary.",
                score: 4.5,
                lastUpdatedAt: new Date("2024-05-28"),
            },
        ]}
        onFunctionClick={(name: string) => alert(name)}
    />
);
