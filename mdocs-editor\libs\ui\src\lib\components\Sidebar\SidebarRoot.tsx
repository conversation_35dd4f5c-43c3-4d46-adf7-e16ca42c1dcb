import React from "react";

/**
 * @interface SidebarLayoutProps
 * @description 用于侧边栏布局的属性类型定义。
 * @property isSidebarOpen 是否打开侧边栏
 * @property onSidebarToggle 侧边栏切换回调
 * @property children 子内容
 * @property iconBar 图标栏
 */
export interface SidebarLayoutProps {
  isSidebarOpen: boolean;
  onSidebarToggle: () => void;
  children: React.ReactNode;
  iconBar: React.ReactNode;
}

/**
 * 侧边栏布局组件
 * @param props SidebarLayoutProps
 */
export function SidebarLayout({
  isSidebarOpen,
  onSidebarToggle,
  children,
  iconBar,
}: SidebarLayoutProps) {
  return (
    <div className="flex h-full">
      {/* 侧边栏内容区 */}
      {isSidebarOpen && (
        <div className="w-64 flex flex-col bg-white border-r shadow-sm h-full">
          <div className="flex p-2 justify border-b">
            <button
              type="button"
              className="text-blue-500 hover:text-blue-600 hover:bg-blue-100 p-2 rounded-full transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-200"
              onClick={onSidebarToggle}
              title="收起侧边栏"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19l7-7-7-7" />
              </svg>
            </button>
          </div>
          <div className="flex-1 overflow-hidden">
            {children}
          </div>
        </div>
      )}
      
      {/* IconBar 区域始终渲染 */}
      <div className="w-12 h-full flex flex-col items-center py-2 bg-gray-50 border-r">
        {React.isValidElement(iconBar) && typeof iconBar.props === 'object' && iconBar.props && 'onTabChange' in iconBar.props
          ? React.cloneElement(iconBar as React.ReactElement<{ onTabChange: (...args: any[]) => void }>, {
              onTabChange: (...args: any[]) => {
                if (!isSidebarOpen) onSidebarToggle();
                (iconBar.props as { onTabChange?: (...args: any[]) => void }).onTabChange?.(...args);
              },
            })
          : iconBar}
      </div>
    </div>
  );
}