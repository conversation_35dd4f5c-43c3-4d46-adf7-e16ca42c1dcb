import { Separator } from "../../ui/separator";
import { CollapsibleSection } from "../components/CollapsibleSection";
import { BasicInfoSection } from "../components/BasicInfoSection";
import { ExamplesSection } from "../components/ExamplesSection";
import { SyntaxesSection } from "../components/SyntaxesSection";
import { ParametersSection } from "../components/ParametersSection";
import { OptionalFieldsSection } from "../components/OptionalFieldsSection";
import { useCollapsibleSections } from "../hooks/useCollapsibleSections";
import type { FunctionContent } from "../FunctionEditor";
/**
 * 表单视图组件的属性接口
 */
export interface FormViewProps {
  /** 函数内容数据 */
  functionData: Partial<FunctionContent>;
  /** 函数数据更新回调 */
  onDataChange: (data: Partial<FunctionContent>) => void;
  /** 是否处于加载状态 */
  isLoading?: boolean;
  /** 可选的版本列表 */
  availableVersions?: Options[];
  /** 参数的可选类型列表 */
  availableTypes?: Options[];
  /** 函数名称 */
  functionName: string;
}

/**
 * 标签选项接口
 */
export interface Options {
  /** 选项值 */
  value: string;
  /** 显示名称 */
  label: string;
  /** 选项描述 */
  description?: string;
}

/**
 * 表单视图组件
 *
 * 负责渲染函数编辑器的表单视图
 */
export const FormView: React.FC<FormViewProps> = ({
  functionData,
  onDataChange,
  isLoading = false,
  availableVersions = [],
  availableTypes = [],
  functionName,
}) => {
  // 折叠状态管理
  const { collapsedSections, toggleSection } = useCollapsibleSections([
    { key: 'examples', defaultExpanded: true },
    { key: 'syntaxes', defaultExpanded: true },
    { key: 'params', defaultExpanded: true },
    { key: 'optional', defaultExpanded: true },
  ]);

  return (
    <div className="space-y-6 p-4 h-full overflow-auto">
      {/* 基本信息部分 - 直接显示，不使用折叠 */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-900">
          基本信息 <span className="text-red-500">*</span>
        </h3>
        <BasicInfoSection
          content={functionData}
          onContentChange={onDataChange || (() => { })}
          isLoading={isLoading}
          availableVersions={availableVersions}
          functionName={functionName}
        />
      </div>

      <Separator />

      {/* 参数管理部分 */}
      <CollapsibleSection
        title="参数列表 *"
        sectionKey="params"
        collapsedSections={collapsedSections}
        onToggleSection={toggleSection}
      >
        <ParametersSection
          content={functionData}
          onContentChange={onDataChange}
          isLoading={isLoading}
          typeOptions={availableTypes}
        />
      </CollapsibleSection>

      <Separator />

      {/* 语法管理部分 */}
      <CollapsibleSection
        title="语法定义 *"
        sectionKey="syntaxes"
        collapsedSections={collapsedSections}
        onToggleSection={toggleSection}
      >
        <SyntaxesSection
          content={functionData}
          onContentChange={onDataChange}
          isLoading={isLoading}
          functionName={functionName}
        />
      </CollapsibleSection>

      <Separator />

      {/* 示例管理部分 */}
      <CollapsibleSection
        title="函数示例 *"
        sectionKey="examples"
        collapsedSections={collapsedSections}
        onToggleSection={toggleSection}
      >
        <ExamplesSection
          content={functionData}
          onContentChange={onDataChange}
          isLoading={isLoading}
        />
      </CollapsibleSection>

      <Separator />

      {/* 可选字段部分 */}
      <CollapsibleSection
        title="可选字段"
        sectionKey="optional"
        collapsedSections={collapsedSections}
        onToggleSection={toggleSection}
        defaultExpanded={false}
      >
        <OptionalFieldsSection
          content={functionData}
          onContentChange={onDataChange}
          isLoading={isLoading}
        />
      </CollapsibleSection>
    </div>
  );
};