{"name": "@mdocs/ui", "private": true, "version": "0.0.0", "type": "module", "module": "dist/ui.js", "main": "dist/ui.cjs", "types": "dist/types/index.d.ts", "style": "dist/ui.css", "sideEffects": ["**/*.css"], "files": ["dist"], "scripts": {"build": "vite build", "watch": "vite build --watch", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"@mdocs/schema": "workspace:*", "@monaco-editor/react": "^4.7.0", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.525.0", "react": "catalog:react19", "react-dom": "catalog:react19", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@storybook/addon-docs": "^9.0.16", "@storybook/react-vite": "^9.0.16", "@tailwindcss/vite": "catalog:react19", "@types/react": "catalog:react19", "@types/react-dom": "catalog:react19", "@vitejs/plugin-react-swc": "catalog:react19", "storybook": "^9.0.16", "tailwindcss": "catalog:react19", "tw-animate-css": "^1.3.5", "typescript": "catalog:dev", "vite": "catalog:dev", "vite-plugin-dts": "catalog:dev"}}