<script lang="ts">
  import { BotMessage, UserMessage, BotThinkingMessage } from '$lib/message';
  import botAvatar from '$lib/assets/icon/MWORKS_Copilot.png';
  import { Chat } from '@askme/lib-common/chat';
  import { Button } from '$lib/components/ui/button';
  import type { ClassValue } from 'svelte/elements';
  import type { UIAttachment } from '@askme/lib-common';
  import ChevronDown from '@lucide/svelte/icons/chevron-down';
  import { cn } from '$lib/utils';
  import { onMount } from 'svelte';

  interface Props {
    /** @prop {Chat} chatClient - Chat实例. */
    chatClient: Chat;
    /** @prop {ClassValue} class - 自定义样式. */
    class?: ClassValue;
  }

  let { chatClient, class: className }: Props = $props();

  let botThinkingDivRef = $state<HTMLDivElement>();
  let botMsgDivRef = $state<HTMLDivElement>();
  let chatPanelRef = $state<HTMLDivElement>(); // 引用滚动容器

  // 用于渲染消息列表，可被临时覆盖
  let messages = $derived(chatClient.linearMessages);
  let latestBotMessageID = $derived(
    chatClient.linearMessages.findLast((m) => m.role === 'assistant')?.id,
  );
  let isStreaming = $derived(chatClient.status === 'streaming');
  let isSubmitted = $derived(chatClient.status === 'submitted');
  let isReady = $derived(chatClient.status === 'ready');

  // 滚动条到底部的距离
  // ⚠️由于dom元素的scrollHeight等属性不是响应式变量，所以不能直接derived更新
  let distanceToBottom = $state(0);
  // 跟踪自动滚动（用户想查看历史输出还是即时输出）
  let isAutoScroll = $state(true);
  // 记录上一次滚动位置，用于判断滚动方向
  let lastScrollTop = 0;
  // 记录总的可滚动区域高度
  let lastScrollHeight = 0;
  // 用户点击了滚动到底部按钮，或切换chat路由时
  let isUserClickThenScroll = false;
  // 用户提交消息时，标识一条新消息的开始
  let isNewSubmitStart = false;

  const SHOW_SCROLL_TO_BOTTOM_BUTTON_THRESHOLD = 20;
  // 滚动到底部按钮是否显示
  let isScrollBottomButtonShow = $derived(
    distanceToBottom > SHOW_SCROLL_TO_BOTTOM_BUTTON_THRESHOLD,
  );

  $effect(() => {
    // 如果不自动滚动 且LLM正在输出，或是reday状态，则不执行任何自动滚动逻辑
    if ((!isAutoScroll && isStreaming) || isReady) {
      return;
    }

    // 如果设定自动滚动，滚动到最新的机器人消息
    if (chatClient.linearMessages && isAutoScroll && !isSubmitted) {
      botMsgDivRef?.scrollIntoView({ block: 'end' });
    }
  });

  $effect(() => {
    // 当LLM回复完毕，并且用户正在追踪最新回复
    // 这个effect只在用户提交消息后执行一次
    if (isReady && isAutoScroll && chatPanelRef && isNewSubmitStart) {
      const GO_DOWN_PX = 40;
      // 再往下尝试滚动40px，显示完整的BotMessageAction
      chatPanelRef.scrollTop += GO_DOWN_PX;
    }
    isNewSubmitStart = false;
  });

  $effect(() => {
    // 用户提交消息，自动滚动到botThinking组件
    if (isSubmitted) {
      isNewSubmitStart = true;
      botThinkingDivRef?.scrollIntoView({ behavior: 'smooth', block: 'end' });
    }
  });

  // 路由切换时滚动到底部
  $effect(() => {
    if (chatClient.id && chatPanelRef) {
      onClickScrollToBottom();
    }
  });

  function onClickScrollToBottom() {
    chatPanelRef!.scrollTop = chatPanelRef!.scrollHeight;
    isUserClickThenScroll = true;
    isAutoScroll = true;
  }

  function handleScroll(event: Event) {
    const element = event.currentTarget as HTMLDivElement;
    // 计算滚动条距离底部的距离
    distanceToBottom = element.scrollHeight - element.scrollTop - element.clientHeight;
    if (isUserClickThenScroll) {
      isUserClickThenScroll = false;
      return;
    }

    // 用户向下滚动到距离底部200像素以内时，重新启用自动滚动
    const SCROLL_DOWN_THRESHOLD = 200;
    // 计算滚动条距离顶部的距离
    const currentScrollTop = element.scrollTop;
    // ⚠️： markdown渲染时，较低概率发生内容重排后（例如列表渲染），scrollHeight高度变小，小于上次渲染时的高度。
    // 这种情况下，不进行是否禁用自动滚动的判断
    if (lastScrollHeight <= element.scrollHeight) {
      // ⚠️ 向下滚动到底时有时会出现currentScrollTop < lastScrollTop的情况,设置一个阈值防止取消自动滚动
      const THREADHOLD = 1;
      if (currentScrollTop + THREADHOLD < lastScrollTop) {
        // Scrolling up - 任何向上滚动都禁用自动滚动
        isAutoScroll = false;
      } else if (distanceToBottom <= SCROLL_DOWN_THRESHOLD) {
        //如果向下滚动到距离底部200像素以内，用户想查看即时输出
        isAutoScroll = true;
      }
    }
    // 更新上一次滚动位置
    lastScrollTop = currentScrollTop <= 0 ? 0 : currentScrollTop; // 处理滚动到顶部的情况(safari上有可能为负值)
    // 更新上一次滚动高度
    lastScrollHeight = element.scrollHeight;
  }

  // 更新distanceToBottom
  function updateDistanceToBottom(chatPanel: HTMLDivElement) {
    const hasVerticalScrollbar = chatPanel.scrollHeight > chatPanel.clientHeight;
    if (hasVerticalScrollbar) {
      distanceToBottom = chatPanel.scrollHeight - chatPanel.scrollTop - chatPanel.clientHeight;
    } else {
      distanceToBottom = 0;
    }
  }

  onMount(() => {
    // 在组件内dom变化的时候，更新distanceToBottom
    const mutationObserver = new MutationObserver(() => {
      const DELAY_IN_MS = 200;
      // 判断是否出现垂直滚动条
      // ⚠️：之所以要在MutationObserver中判断，是因为当滚动条消失时，不会触发handleScroll，因此distanceToBottom的值不会更新
      // 因此通过监听dom变化来更新distanceToBottom

      // ⚠️：之所以要延迟200ms，是因为深度思考弹出有动画效果，dom的改变不直接触发滚动条的出现，因此延迟200ms再判断是否出现滚动条
      setTimeout(() => {
        if (!chatPanelRef) {
          return;
        }
        updateDistanceToBottom(chatPanelRef);
      }, DELAY_IN_MS);
    });
    // 在resize组件的时候，更新distanceToBottom
    const resizeObserver = new ResizeObserver(() => {
      if (!chatPanelRef) {
        return;
      }
      updateDistanceToBottom(chatPanelRef);
    });
    mutationObserver.observe(chatPanelRef!, { subtree: true, childList: true });
    resizeObserver.observe(chatPanelRef!);
    return () => {
      mutationObserver.disconnect();
      resizeObserver.disconnect();
    };
  });
</script>

<!--
@component
ChatPanel是用于显示用户和Bot对话的组件。

- 用法:
  ``` svelte
  <div class="flex ...">
    <ChatPanel {chatClient} class={} />
  </div>
  ```
-->
<div class={cn('relative', className)}>
  <div
    bind:this={chatPanelRef}
    class="scroll-container flex min-h-0 flex-1 flex-col gap-6 overflow-y-auto px-[max(10vw,32px)] pt-8"
    onscroll={handleScroll}
  >
    {#each messages as message (message.id)}
      {#if message.role === 'user'}
        {#each message.parts as part, index (index)}
          {#if part.type === 'text'}
            <UserMessage
              class="ml-auto w-fit md:max-w-[75%] "
              message={part.text}
              attachments={message.experimental_attachments as UIAttachment[]}
            />
          {/if}
        {/each}
      {:else if message.role === 'assistant'}
        <div bind:this={botMsgDivRef}>
          <BotMessage
            showActions={!(isStreaming && latestBotMessageID === message.id)}
            avatarSrc={botAvatar}
            {message}
            reload={() => {
              chatClient.reload({}, message.id);
              const reloadIndex = messages.findIndex((m) => m.id === message.id);
              messages = messages.slice(0, reloadIndex);
            }}
          />
        </div>
      {/if}
    {/each}

    {#if isReady && chatClient.linearMessages.length > 0 && chatClient.linearMessages.at(-1)!.role === 'user'}
      <div class="flex items-center gap-2 px-3 py-2">
        <span class="text-red-500">消息生成失败</span>
        <Button
          variant="outline"
          size="sm"
          onclick={() => {
            chatClient.reload();
          }}
        >
          重试
        </Button>
      </div>
    {/if}

    {#if isSubmitted}
      <div bind:this={botThinkingDivRef} class="pb-4">
        <BotThinkingMessage avatarSrc={botAvatar} thinkingMessage="思考中..." />
      </div>
    {/if}
  </div>

  {#if isScrollBottomButtonShow}
    <Button
      variant="outline"
      size="icon"
      onclick={onClickScrollToBottom}
      class="absolute right-[max(10vw,32px)] bottom-4 z-10"
    >
      <ChevronDown class="h-5 w-5" />
    </Button>
  {/if}
</div>

<style>
  .scroll-container {
    scrollbar-gutter: stable;
  }
</style>
