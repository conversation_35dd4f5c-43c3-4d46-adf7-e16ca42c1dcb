# mono repo

## 添加/删除依赖

添加/删除依赖，需要处理的是根目录下的 `package.json` 中的 `devdependencies` 字段。
子项目本身则根据实际情况添加对应的依赖至 `dependencies` 或 `devdependencies`。

## 子项目

子项目统一命名为 `@askme/xxx`，`xxx` 为项目名称。
作为 library 的子项目如果需要被其他项目引用，则需要在根目录的 `package.json` 下的 `dependencies` 中添加对应记录，如：

```json
{
  "dependencies": {
    "@askme/lib-common": "link:./libs/common"
  }
}
```

上面的 `link:./libs/common` 是针对纯 typescript 项目。如果是 react/vue/svelte 等需要经过打包构建的项目，
则 link 对象应该为构建后的目录，如：

```json
{
  "dependencies": {
    "@askme/lib-ui": "link:./libs/ui/dist"
  }
}
```

此时对 `@askme/lib-ui` 的修改，必须重新构建后才能在其他项目中看到效果。
