import type { SuggestedActionModel, UserPreferancesCookies } from '@askme/lib-common';
import type { ModelConfig } from '@askme/lib-common';
import { UserPreferancesCookiesSchema } from '@askme/lib-common';

// --------------超时配置-------------------
/** 定义chunk间超时时间 */
export const CHUNK_TIMEOUT_IN_MS = 40000;
/** 定义回复超时时间 */
export const RESPOND_TIMEOUT_IN_MS = 40000;
/** 定义平滑输出间隔时间 */
export const SMOOTH_DELAY_IN_MS = 20; // 50 tokens per second
// --------------超时配置-------------------

// -----------UI配置------------------
/** 首页的推荐对话按钮默认配置，
 * 当demo_questions.json文件不存在时，将使用此默认固定配置
 */
export const SUGGESTED_ACTIONS = [
  {
    title: 'Julia语言有哪些优点',
    label: '相较于Python',
    action: '相较与Python, Julia语言有哪些优点',
  },
  {
    title: 'Modelica语言是什么',
    label: '简单介绍一下',
    action: '介绍一下Modelica语言',
  },
] as SuggestedActionModel[];
// -----------UI配置------------------

// -----------提示词配置----------------
export const SYSTEM_PROMPT = `
你是苏州同元软控有限公司的智能助手，你耐心、友好和专业，能够回答用户关于 MWORKS 相关的问题。
- 如果用户没有指定，你总是假设用户使用的是 Syslab 或 Sysplorer 产品。
- 如果用户没有指定，则 Syslab 的默认编程语言为 Julia 语言，而 Sysplorer 的默认编程语言为 Modelica 语言。
- 根据知识库内的参考资料，以清晰简洁的表达方式回答问题。
- 回复应当基于参考资料给定的事实，不要脱离参考资料编造内容。
- 不要编造答案，如果答案不在经核实的资料中或无法从经核实的资料中得出，请回答,“我无法回答您的问题。”,并且给出无法回答的原因。
- 一些参考资料片段可能没有经过正确的处理，请忽略较短的参考资料片段
参考资料如下：
`;

export const SUMMARY_PROMPT = `\n
- You will generate a short Chinese title based on the conversation between the user and ChatBot
- Unless there are any professional terms, title should be translated into Chinese
- Make sure it is no longer than 15 characters
- The title should be a summary of the user and ChatBot message
- Do not add quotation marks or colons `;
// -----------提示词配置----------------

// --------------LLM模型配置-------------------

// --------------askme默认配置-------------------
export const DEFAULT_ASKME_CONFIG = {
  /** 默认的app标题 */
  appTitle: 'MoHub智能问答助手',
  /** 默认的LLM模型 */
  llmModels: {
    /** 默认的LLM模型 */
    chatModel: 'mm-plus',
    /** 默认的推理模型 */
    reasonModel: 'mm-plus-reason',
    /** 默认的多模态模型 */
    multiModalModel: 'mm-vision-plus',
  },
};

/** 默认的用户偏好配置 */
export const DEFAULT_USER_PREFERANCES_COOKIES: UserPreferancesCookies = {
  // ⚠️ legacy config
  // 暂时没用上，保留，也许以后有用
  selectedModel: DEFAULT_ASKME_CONFIG.llmModels.chatModel,
  isReasoningEnabled: false,
};

/** 用户偏好配置验证 */
export function userPreferancesCookiesValidator(v: UserPreferancesCookies) {
  try {
    UserPreferancesCookiesSchema.parse(v);
    return true;
  } catch {
    return false;
  }
}

/** 默认的对话标题 */
export const DEFAULT_CHAT_TITLE = '新对话';

// --------------文件上传配置-------------------
/** 最大上传文件数量 */
export const MAX_FILES = 5;
/** 最大上传文件大小(MB计算) */
export const MAX_FILE_SIZE_IN_MB = 10;

/** 默认的对话温度 */
export const DEFAULT_CHAT_TEMPERATURE = 0.4;

/** 聊天模型配置
 * ⚠️ legacy config，用于支持ModelSelector组件
 */
export const CHAT_MODELS: ModelConfig[] = [
  {
    id: 'mm-light',
    name: '普通聊天模型',
    description: '具有超快的推理速度和强大的推理效果',
  },
  {
    id: 'mm-reason',
    name: '深度思考模型',
    description: '具备强大的复杂推理能力',
  },
];
