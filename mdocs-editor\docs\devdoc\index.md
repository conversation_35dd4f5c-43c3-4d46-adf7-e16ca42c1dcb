# 概览

本项目是一个基于 React + Shadcn + NextJS 的 monorepo 项目，以 TypeScript 为主要开发语言，以 `pnpm` 作为包管理工具。

## 项目结构

mdocs-editor-monorepo 是一个 monorepo 单一项目仓库，包含以下几个部分：

```
mdocs-editor-monorepo
├── apps
│   ├── mdocs-editor         # 文档编辑器
├── libs
│   ├── ui                   # UI 组件库
│   └── schema               # 数据模型
├── docs                     # 文档
├── pnpm-lock.yaml
└── package.json
```
## 如何本地启动项目

1. 安装依赖（根目录下）

```bash
pnpm install
```

2. 启动前端开发环境

```bash
cd apps/mdocs-editor
pnpm dev
```

