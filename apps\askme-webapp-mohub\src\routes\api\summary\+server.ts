import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from './$types';
import { json, error } from '@sveltejs/kit';
import { generateText } from 'ai';
import serverCtx from '$lib/context.server';
import * as LogFormat from '@askme/lib-common/logformat';
import { RESPOND_TIMEOUT_IN_MS, SUMMARY_PROMPT } from '$lib/config';
import { getOpenAIforUser } from '$lib/models';
import type { Message } from '$lib/generated/prisma';
import { convertMsg } from '$lib/utils';
import { hotReload } from '$lib/hotRelod';

export const POST = (async ({ request, locals }) => {
  if (!locals.user) {
    console.debug(LogFormat.debug('Unauthorized in /api/summary POST'));
    error(401, 'Unauthorized');
  }

  const requestJson = await request.json();

  let messages: Message[] = [];
  try {
    messages = await serverCtx.prisma.message.findMany({
      where: {
        chatId: requestJson.chatID,
      },
      orderBy: {
        createdAt: 'asc',
      },
    });
  } catch (error) {
    console.error(LogFormat.error('Failed to find messages:' + error));
  }

  const prompt = messages
    .map((m) => {
      const uiMessage = convertMsg(m);
      const textParts = uiMessage.parts.filter((p) => p.type === 'text');
      return `${uiMessage.role}: ${textParts.map((p) => p.text).join('\n')}`;
    })
    .join('\n');

  const provider = getOpenAIforUser(locals.session.accessToken);

  // --------------langfuse标题总结埋点---------
  const trace = serverCtx.langfuse.trace({
    sessionId: requestJson.chatID,
    id: requestJson.chatID,
    name: 'summary-' + requestJson.chatID,
    userId: locals.user.id.toString(),
    input: prompt,
    tags: ['title-summary'],
  });
  const generation = trace.generation({
    id: requestJson.chatID,
    name: 'summary-' + requestJson.chatID,
    model: hotReload.get_askme_config().llmModels.chatModel,
    input: prompt,
    metadata: {
      httpRoute: '/api/summary',
    },
  });
  // --------------langfuse标题总结埋点---------

  try {
    // 调用LLM生成首轮对话标题
    const { text, usage } = await generateText({
      model: provider.languageModel('CHAT_MODEL'),
      prompt,
      system: SUMMARY_PROMPT,
      abortSignal: AbortSignal.timeout(RESPOND_TIMEOUT_IN_MS),
    });
    // --------------langfuse标题总结埋点---------
    trace.update({
      output: text,
    });
    generation.end({
      output: text,
      usage: {
        promptTokens: usage.promptTokens,
        totalTokens: usage.totalTokens,
        completionTokens: usage.completionTokens,
      },
    });
    // --------------langfuse标题总结埋点---------

    try {
      // 更新新标题到数据库Chat表中
      await serverCtx.prisma.chat.update({
        where: {
          id: requestJson.chatID,
          deletedAt: null,
        },
        data: {
          title: text,
        },
      });
    } catch (error) {
      console.error(LogFormat.error('Sidebar item title failed to write to database: ' + error));
    }
    return json({ text }, { status: 201 });
  } catch (error) {
    console.error(LogFormat.error('Generate title text error:' + error));
    return json({ error: 'Generate title text error' }, { status: 500 });
  }
}) satisfies RequestHandler;
