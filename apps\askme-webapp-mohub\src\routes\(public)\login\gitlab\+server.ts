import { generateState } from 'arctic';
import serverCtx from '$lib/context.server';
import type { RequestEvent } from '@sveltejs/kit';
import * as LogFormat from '@askme/lib-common/logformat';
import { config } from '$lib/config.server';

// 点击登陆按钮后，执行认证前的准备工作
export async function GET(event: RequestEvent): Promise<Response> {
  if (config.oauth.provider.toUpperCase() !== 'GITLAB') {
    return new Response('Unauthorized', {
      status: 401,
    });
  }

  if (!serverCtx.oauth) {
    console.error(
      LogFormat.error('OAuth client not initialized,please check OAUTH_PROVIDER in .env file'),
    );
    process.exit(1);
  }

  const state = generateState();

  const url = serverCtx.oauth.gitlab.createAuthorizationURL(state, []);
  url.href = url.href + '&scope=' + config.oauth.scopes.join(' ');
  // 安全机制，通过比较站点cookie和回调中的随机state值来防止CSRF攻击
  // （攻击人拿不到cookie里的state，没法替代用户发起OAuth验证）
  event.cookies.set('gitlab_oauth_state', state, {
    path: '/',
    httpOnly: true,
    maxAge: 60 * 10,
    sameSite: 'lax',
  });

  return new Response(null, {
    status: 302,
    headers: {
      Location: url.toString(),
    },
  });
}
