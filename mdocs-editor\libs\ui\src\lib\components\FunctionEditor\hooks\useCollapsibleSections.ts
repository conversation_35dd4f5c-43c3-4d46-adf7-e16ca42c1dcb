import { useState } from "react";

/**
 * 折叠状态管理接口
 * @interface CollapsibleState
 * @description 用于折叠状态管理的属性类型定义。
 * @property collapsedSections 折叠状态映射
 * @property toggleSection 切换折叠状态的函数
 */
export interface CollapsibleState {
  /** 折叠状态映射 */
  collapsedSections: Record<string, boolean>;
  /** 切换折叠状态的函数 */
  toggleSection: (sectionKey: string) => void;
}

/**
 * 折叠部分的配置接口
 * @interface SectionConfig
 * @description 用于折叠部分的配置类型定义。
 * @property key 部分的唯一标识符
 * @property defaultExpanded 是否默认展开
 */
export interface SectionConfig {
  /** 部分的唯一标识符 */
  key: string;
  /** 是否默认展开 */
  defaultExpanded?: boolean;
}

/**
 * 折叠状态管理 Hook
 *
 * 管理多个可折叠部分的展开/折叠状态
 *
 * @param sections 部分配置数组
 * @returns 折叠状态和控制函数
 */
export const useCollapsibleSections = (sections: SectionConfig[]): CollapsibleState => {
  // 初始化折叠状态
  const initialState = sections.reduce(
    (acc, section) => {
      acc[section.key] = !(section.defaultExpanded ?? true);
      return acc;
    },
    {} as Record<string, boolean>,
  );

  const [collapsedSections, setCollapsedSections] = useState<Record<string, boolean>>(initialState);

  /**
   * 切换指定部分的折叠状态
   * @param sectionKey 部分的唯一标识符
   */
  const toggleSection = (sectionKey: string) => {
    setCollapsedSections((prev) => ({
      ...prev,
      [sectionKey]: !prev[sectionKey],
    }));
  };

  return {
    collapsedSections,
    toggleSection,
  };
};
