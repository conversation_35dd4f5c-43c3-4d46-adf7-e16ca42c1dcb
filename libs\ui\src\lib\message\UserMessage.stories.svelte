<script module lang="ts">
  //    👆 notice the module context, defineMeta does not work in a regular <script> tag - instance
  import { defineMeta } from '@storybook/addon-svelte-csf';
  import UserMessage from './UserMessage.svelte';

  //      👇 Get the Story component from the return value
  const { Story } = defineMeta({
    title: 'UI/UserMessage',
    component: UserMessage,
    decorators: [],
    parameters: {},
  });
</script>

<Story
  name="Default"
  args={{
    message:
      '我是一个用户。我是一个用户。我是一个用户。我是一个用户。我是一个用户。我是一个用户。我是一个用户。我是一个用户。',
  }}
/>
<Story
  name="Style"
  args={{
    message:
      '我是一个用户。我是一个用户。我是一个用户。我是一个用户。我是一个用户。我是一个用户。我是一个用户。我是一个用户。',
    class: 'break-all rounded-lg bg-primary px-3 py-2 text-primary-foreground',
  }}
/>
