# askme webapp

[![preview-storybook](https://img.shields.io/badge/%E9%A2%84%E8%A7%88-Storybook-blue)](http://ai.gitpages.tongyuan.cc/askme/askme-webapp-mono/main/storybook)

askme 是一个基于 LLM 的类 ChatGPT Web App，可以用于问答、对话等任务。

askme 项目旨在支持以下两种环境的部署：

* 与 MoHub 集成的 MWORKS 智能问答助手: [内网预览环境](https://askme-test.mohub.net)
* 独立部署的同元智能问答助手: [内网预览环境](https://askme.tongyuan.cc:5051)

这个项目是一个 mono repo 项目，包含了多个子项目：

- `apps/askme-webapp-mohub`：MWORKS 智能问答助手
- `apps/askme-webapp-tongyuan`： 同元智能助手【暂未开始】
- `lib/ui`: askme 的通用 UI 组件库（基于 Svelte 开发）
- `lib/common`: askme 的通用工具库

项目以 typescript 为主要开发语言，使用 pnpm 作为包管理工具。

## 部署

部署配置见 [deployments](./deployments) 目录。
