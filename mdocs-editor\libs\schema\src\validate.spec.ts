import { describe, expect, it } from "vitest";
import { check_library, validate_library } from "./validate";

describe("Schema Validation", () => {
  it("wrong json test with wrong type", () => {
    const input: string = `
    {
        "summary": "图形",
        "functions": {
            "plot":{
                "tags": ["visualization", "chart"],
                "since": "default",
                "syntaxes": "default",
                "examples": "default",
                "params": "default"
            }
        }
    }
  `;
    const result = validate_library(JSON.parse(input));
    expect(result.length).toBeGreaterThan(0);
  });
  it("correct json test", () => {
    const input: string = `
    {
      "summary": {
        "zh": "绘图",
        "en": "Plot"
      },
      "functions": {
        "plot": {
          "name": "plot",
          "summary": {
            "zh": "二维线图",
            "en": "2-D Line Plot"
          },
          "tags": [
            "#/i18n/tagGraphics",
            "#/i18n/tag2D3DPlots",
            "#/i18n/tagLinePlots"
          ],
          "since": "2025a",
          "deprecated": false,
          "syntaxes": {
            "X_Y": {
              "description": {
                "zh": "创建 Y 中数据对 X 中对应值的二维线图。……",
                "en": "..."
              },
              "group": 1,
              "params": ["#params/X", "#params/Y"],
              "examples": ["#examples/createLinePlot"]
            },
            "X_Y_fmt": {
              "description": {
                "zh": "设置线型、标记符号和颜色。",
                "en": "..."
              },
              "group": 1,
              "params": ["#params/X", "#params/Y", "#params/fmt"]
            },
            "Xn_Yn": {
              "description": {
                "zh": "绘制多个X、Y对组的图，所有线条都使用相同的坐标区。",
                "en": "..."
              },
              "group": 1,
              "params": ["#params/X", "#params/Y"],
              "repeated": true,
              "example": ["#examples/plotMultiLines"]
            },
            "Xn_Yn_fmtn": {
              "description": {
                "zh": "设置每个线条的线型、标记符号和颜色。您可以混用 X、Y、fmt 三元组和 X、Y 对组：例如，plot(X1,Y1,X2,Y2,fmt2,X3,Y3)。",
                "en": "..."
              },
              "group": 1,
              "params": ["#params/X", "#params/Y", "#params/fmt"],
              "repeated": true
            },
            "Y": {
              "description": {
                "zh": "创建 Y 中数据对每个值索引的二维线图。……",
                "en": "..."
              },
              "group": 2,
              "params": ["#params/Y"]
            },
            "Y_fmt": {
              "description": {
                "zh": "设置线型、标记符号和颜色。",
                "en": "..."
              },
              "group": 2,
              "params": ["#params/Y", "#params/fmt"]
            },
            "__KeyValue": {
              "description": {
                "zh": "使用一个或多个 Key=Value 对组参数指定线条属性。可以将此选项与前面语法中的任何输入参数组合一起使用。名称-值对组设置将应用于绘制的所有线条。",
                "en": "..."
              },
              "group": 3,
              "params": ["#params/color", "#params/linestyle", "#params/marker"],
              "usageGroupAt": "beforeParams"
            },
            "ax__": {
              "description": {
                "zh": "将在由 ax 指定的坐标区中，而不是在当前坐标区（gca）中创建线条。选项 ax 可以位于前面的语法中的任何输入参数组合之前。",
                "en": "..."
              },
              "group": 3,
              "params": ["#params/ax"],
              "usageGroupAt": "afterParams"
            },
            "h": {
              "description": {
                "zh": "返回由图形线条对象组成的列向量。在创建特定的图形线条后，可以使用 h 修改其属性。",
                "en": "..."
              },
              "group": 4,
              "params": ["#params/h"],
              "usageGroupAt": "beforeParams"
            }
          },
          "examples": {
            "createLinePlot": {
              "summary": {
                "zh": "创建线图",
                "en": "..."
              },
              "description": {
                "zh": "将 x 创建为由 0 和 2π 之间的线性间隔值组成的向量。在各值之间使用递增量 π/100。将 y 创建为 x 的正弦值。创建数据的线图。……",
                "en": "..."
              },
              "syntaxes": ["#syntaxes/X_Y"]
            },
            "plotMultiLines": {
              "summary": {
                "zh": "绘制多个线条",
                "en": "..."
              },
              "description": {
                "zh": "将 x 定义为 100 个介于 −2π 和 2π 之间的线性间隔值。将 y1 和 y2 定义为 x 的正弦和余弦值。创建上述两个数据集的线图。……",
                "en": "..."
              },
              "syntaxes": ["#syntaxes/Xn_Yn"]
            }
          },
          "params": {
            "X": {
              "summary": {
                "zh": "x 值",
                "en": "..."
              },
              "description": {
                "zh": "x 值，指定为标量、向量或矩阵。",
                "en": "..."
              },
              "kind": "posIn",
              "type": [
                "#/builtins/Scalar",
                "#/builtins/Vector",
                "#/builtins/Matrix"
              ],
              "elemType": [
                "#/builtins/Int16",
                "#/builtins/Int32",
                "#/builtins/Int64",
                "#/builtins/Float16",
                "#/builtins/Float32",
                "#/builtins/Float64"
              ]
            },
            "Y": {
              "summary": {
                "zh": "y 值",
                "en": "..."
              },
              "description": {
                "zh": "y 值，指定为标量、向量或矩阵。要根据特定的 x 值绘图，还必须指定 X。",
                "en": "..."
              },
              "kind": "posIn",
              "type": [
                "#/builtins/Scalar",
                "#/builtins/Vector",
                "#/builtins/Matrix"
              ],
              "elemType": [
                "#/builtins/Int16",
                "#/builtins/Int32",
                "#/builtins/Int64",
                "#/builtins/Float16",
                "#/builtins/Float32",
                "#/builtins/Float64"
              ]
            },
            "fmt": {
              "summary": {
                "zh": "线型、颜色和标记",
                "en": "..."
              },
              "description": {
                "zh": "线型、颜色和标记，指定为包含符号的字符串。符号可以按任意顺序显示。您不需要同时指定所有三个特征（线型、颜色和标记）。例如，如果忽略线型，只指定标记，则绘图只显示标记，不显示线条。……",
                "en": "..."
              },
              "kind": "posIn",
              "type": "#/builtins/String"
            },
            "ax": {
              "summary": {
                "zh": "目标坐标区",
                "en": "..."
              },
              "description": {
                "zh": "目标坐标区，指定为 Axes 对象、PolarAxes 对象。如果不指定坐标区或当前坐标区是笛卡尔坐标区，plot 函数将使用当前坐标区。要在极坐标区上绘图，请指定 PolarAxes 对象作为第一个输入参数，或者使用 polarplot 函数。",
                "en": "..."
              },
              "kind": "posIn",
              "type": ["#/builtins/Axes", "#/builtins/PolarAxes"]
            },
            "color": {
              "summary": {
                "zh": "线条颜色",
                "en": "..."
              },
              "description": {
                "zh": "线条颜色，指定为 RGB 三元组、十六进制颜色代码、颜色名称或短名称。……",
                "en": "..."
              },
              "kind": "keyword",
              "type": ["#/builtins/Color", "#/builtins/RGB", "#/builtins/HexColor"],
              "default": "[0, 0.4470, 0.7410]",
              "examples": ["red", "r"]
            },
            "linestyle": {
              "summary": {
                "zh": "线型",
                "en": "..."
              },
              "description": {
                "zh": "线型，指定为下表中列出的选项之一。……",
                "en": "..."
              },
              "kind": "keyword",
              "type": "#/builtins/LineStyle"
            },
            "marker": {
              "summary": {
                "zh": "标记符号",
                "en": "..."
              },
              "description": {
                "zh": "标记符号，指定为下表中的标记之一。默认情况下，图形线条没有标记。通过指定标记符号沿该线条上的每个数据点添加标记。……",
                "en": "..."
              },
              "kind": "keyword",
              "type": "#/builtins/Marker"
            },
            "h": {
              "summary": {
                "zh": "一个或多个图形线条对象",
                "en": "..."
              },
              "description": {
                "zh": "一个或多个图形线条对象，以标量或向量的形式返回。这些是唯一标识符，可以用来查询和修改特定图形线条的属性。",
                "en": "..."
              },
              "kind": "out",
              "type": ["#/builtins/Scalar", "#/builtins/Vector"]
            }
          },
          "footnote": {
            "zh": "# 参考文献",
            "en": "..."
          },
          "seeAlso": ["#/functions/title"],
          "changelogs": {
            "1.0.0": "**v1.0.0** 正式发布！"
          }
        }
      },
      "classes": {
        "Axes": {
          "summary": {
            "zh": "坐标系",
            "en": "..."
          },
          "description": {
            "zh": "...",
            "en": "..."
          },
          "functions": ["#/functions/plot"],
          "constructor": "#/functions/plot"
        }
      },
      "enums": {
        "ObjectiveSense": {
          "summary": {
            "zh": "...",
            "en": "..."
          },
          "description": {
            "zh": "...",
            "en": "..."
          },
          "variants": {
            "minimize": {
              "summary": {
                "zh": "最小化",
                "en": "..."
              },
              "description": {
                "zh": "最小化，指示优化目标为最小化。",
                "en": "..."
              }
            },
            "min": {
              "summary": {
                "zh": "最小化",
                "en": "..."
              },
              "description": {
                "zh": "最小化，指示优化目标为最小化。",
                "en": "..."
              }
            },
            "maximize": {
              "summary": {
                "zh": "最大化",
                "en": "..."
              },
              "description": {
                "zh": "最大化，指示优化目标为最大化。",
                "en": "..."
              }
            },
            "max": {
              "summary": {
                "zh": "最大化",
                "en": "..."
              },
              "description": {
                "zh": "最大化，指示优化目标为最大化。",
                "en": "..."
              }
            }
          }
        }
      },
      "i18n": {
        "tagGraphics": {
          "zh": "图形",
          "en": "Graphics"
        },
        "tag2D3DPlots": {
          "zh": "二维图和三维图",
          "en": "2-D and 3-D Plots"
        },
        "tagLinePlots": {
          "zh": "线图",
          "en": "Line Plots"
        }
      }
    }

  `;
    const result = check_library(input);
    if (result.length > 0) {
      console.error("Validation errors:", result);
    }
    expect(result.length).toBe(0);
  });
  it("incorrect json with wrong reference", () => {
    const input: string = `
    {
      "summary": {
        "zh": "绘图",
        "en": "Plot"
      },
      "functions": {
        "plot": {
          "name": "plot",
          "summary": {
            "zh": "二维线图",
            "en": "2-D Line Plot"
          },
          "tags": [
            "#/i18n/tagGraphics",
            "#/i18n/tag2D3DPlots",
            "#/i18n/tagLinePlots"
          ],
          "since": "2025a",
          "deprecated": false,
          "syntaxes": {
            "X_Y": {
              "description": {
                "zh": "创建 Y 中数据对 X 中对应值的二维线图。……",
                "en": "..."
              },
              "group": 1,
              "params": ["#params/X", "#params/Y"],
              "examples": ["#examples/createLinePlot"]
            },
            "X_Y_fmt": {
              "description": {
                "zh": "设置线型、标记符号和颜色。",
                "en": "..."
              },
              "group": 1,
              "params": ["#params/X", "#params/Y", "#params/fmt"]
            },
            "Xn_Yn": {
              "description": {
                "zh": "绘制多个X、Y对组的图，所有线条都使用相同的坐标区。",
                "en": "..."
              },
              "group": 1,
              "params": ["#params/X", "#params/Y"],
              "repeated": true,
              "example": ["#examples/plotMultiLines"]
            },
            "Xn_Yn_fmtn": {
              "description": {
                "zh": "设置每个线条的线型、标记符号和颜色。您可以混用 X、Y、fmt 三元组和 X、Y 对组：例如，plot(X1,Y1,X2,Y2,fmt2,X3,Y3)。",
                "en": "..."
              },
              "group": 1,
              "params": ["#params/X", "#params/Y", "#params/fmt"],
              "repeated": true
            },
            "Y": {
              "description": {
                "zh": "创建 Y 中数据对每个值索引的二维线图。……",
                "en": "..."
              },
              "group": 2,
              "params": ["#params/Y"]
            },
            "Y_fmt": {
              "description": {
                "zh": "设置线型、标记符号和颜色。",
                "en": "..."
              },
              "group": 2,
              "params": ["#params/Y", "#params/fmt"]
            },
            "__KeyValue": {
              "description": {
                "zh": "使用一个或多个 Key=Value 对组参数指定线条属性。可以将此选项与前面语法中的任何输入参数组合一起使用。名称-值对组设置将应用于绘制的所有线条。",
                "en": "..."
              },
              "group": 3,
              "params": ["#params/color", "#params/linestyle", "#params/marker"],
              "usageGroupAt": "beforeParams"
            },
            "ax__": {
              "description": {
                "zh": "将在由 ax 指定的坐标区中，而不是在当前坐标区（gca）中创建线条。选项 ax 可以位于前面的语法中的任何输入参数组合之前。",
                "en": "..."
              },
              "group": 3,
              "params": ["#params/ax"],
              "usageGroupAt": "afterParams"
            },
            "h": {
              "description": {
                "zh": "返回由图形线条对象组成的列向量。在创建特定的图形线条后，可以使用 h 修改其属性。",
                "en": "..."
              },
              "group": 4,
              "params": ["#params/h"],
              "usageGroupAt": "beforeParams"
            }
          },
          "examples": {
            "createLinePlot": {
              "summary": {
                "zh": "创建线图",
                "en": "..."
              },
              "description": {
                "zh": "将 x 创建为由 0 和 2π 之间的线性间隔值组成的向量。在各值之间使用递增量 π/100。将 y 创建为 x 的正弦值。创建数据的线图。……",
                "en": "..."
              },
              "syntaxes": ["#syntaxes/X_Y"]
            },
            "plotMultiLines": {
              "summary": {
                "zh": "绘制多个线条",
                "en": "..."
              },
              "description": {
                "zh": "将 x 定义为 100 个介于 −2π 和 2π 之间的线性间隔值。将 y1 和 y2 定义为 x 的正弦和余弦值。创建上述两个数据集的线图。……",
                "en": "..."
              },
              "syntaxes": ["#syntaxes/Xn_Yn"]
            }
          },
          "params": {
            "X___": {
              "summary": {
                "zh": "x 值",
                "en": "..."
              },
              "description": {
                "zh": "x 值，指定为标量、向量或矩阵。",
                "en": "..."
              },
              "kind": "posIn",
              "type": [
                "#/builtins/Scalar",
                "#/builtins/Vector",
                "#/builtins/Matrix"
              ],
              "elemType": [
                "#/builtins/Int16",
                "#/builtins/Int32",
                "#/builtins/Int64",
                "#/builtins/Float16",
                "#/builtins/Float32",
                "#/builtins/Float64"
              ]
            },
            "Y": {
              "summary": {
                "zh": "y 值",
                "en": "..."
              },
              "description": {
                "zh": "y 值，指定为标量、向量或矩阵。要根据特定的 x 值绘图，还必须指定 X。",
                "en": "..."
              },
              "kind": "posIn",
              "type": [
                "#/builtins/Scalar",
                "#/builtins/Vector",
                "#/builtins/Matrix"
              ],
              "elemType": [
                "#/builtins/Int16",
                "#/builtins/Int32",
                "#/builtins/Int64",
                "#/builtins/Float16",
                "#/builtins/Float32",
                "#/builtins/Float64"
              ]
            },
            "fmt": {
              "summary": {
                "zh": "线型、颜色和标记",
                "en": "..."
              },
              "description": {
                "zh": "线型、颜色和标记，指定为包含符号的字符串。符号可以按任意顺序显示。您不需要同时指定所有三个特征（线型、颜色和标记）。例如，如果忽略线型，只指定标记，则绘图只显示标记，不显示线条。……",
                "en": "..."
              },
              "kind": "posIn",
              "type": "#/builtins/String"
            },
            "ax": {
              "summary": {
                "zh": "目标坐标区",
                "en": "..."
              },
              "description": {
                "zh": "目标坐标区，指定为 Axes 对象、PolarAxes 对象。如果不指定坐标区或当前坐标区是笛卡尔坐标区，plot 函数将使用当前坐标区。要在极坐标区上绘图，请指定 PolarAxes 对象作为第一个输入参数，或者使用 polarplot 函数。",
                "en": "..."
              },
              "kind": "posIn",
              "type": ["#/builtins/Axes", "#/builtins/PolarAxes"]
            },
            "color": {
              "summary": {
                "zh": "线条颜色",
                "en": "..."
              },
              "description": {
                "zh": "线条颜色，指定为 RGB 三元组、十六进制颜色代码、颜色名称或短名称。……",
                "en": "..."
              },
              "kind": "keyword",
              "type": ["#/builtins/Color", "#/builtins/RGB", "#/builtins/HexColor"],
              "default": "[0, 0.4470, 0.7410]",
              "examples": ["red", "r"]
            },
            "linestyle": {
              "summary": {
                "zh": "线型",
                "en": "..."
              },
              "description": {
                "zh": "线型，指定为下表中列出的选项之一。……",
                "en": "..."
              },
              "kind": "keyword",
              "type": "#/builtins/LineStyle"
            },
            "marker": {
              "summary": {
                "zh": "标记符号",
                "en": "..."
              },
              "description": {
                "zh": "标记符号，指定为下表中的标记之一。默认情况下，图形线条没有标记。通过指定标记符号沿该线条上的每个数据点添加标记。……",
                "en": "..."
              },
              "kind": "keyword",
              "type": "#/builtins/Marker"
            },
            "h": {
              "summary": {
                "zh": "一个或多个图形线条对象",
                "en": "..."
              },
              "description": {
                "zh": "一个或多个图形线条对象，以标量或向量的形式返回。这些是唯一标识符，可以用来查询和修改特定图形线条的属性。",
                "en": "..."
              },
              "kind": "out",
              "type": ["#/builtins/Scalar", "#/builtins/Vector"]
            }
          },
          "footnote": {
            "zh": "# 参考文献",
            "en": "..."
          },
          "seeAlso": ["#/functions/title"],
          "changelogs": {
            "1.0.0": "**v1.0.0** 正式发布！"
          }
        }
      },
      "classes": {
        "Axes": {
          "summary": {
            "zh": "坐标系",
            "en": "..."
          },
          "description": {
            "zh": "...",
            "en": "..."
          },
          "functions": ["#/functions/plot"],
          "constructor": "#/functions/plot"
        }
      },
      "enums": {
        "ObjectiveSense": {
          "summary": {
            "zh": "...",
            "en": "..."
          },
          "description": {
            "zh": "...",
            "en": "..."
          },
          "variants": {
            "minimize": {
              "summary": {
                "zh": "最小化",
                "en": "..."
              },
              "description": {
                "zh": "最小化，指示优化目标为最小化。",
                "en": "..."
              }
            },
            "min": {
              "summary": {
                "zh": "最小化",
                "en": "..."
              },
              "description": {
                "zh": "最小化，指示优化目标为最小化。",
                "en": "..."
              }
            },
            "maximize": {
              "summary": {
                "zh": "最大化",
                "en": "..."
              },
              "description": {
                "zh": "最大化，指示优化目标为最大化。",
                "en": "..."
              }
            },
            "max": {
              "summary": {
                "zh": "最大化",
                "en": "..."
              },
              "description": {
                "zh": "最大化，指示优化目标为最大化。",
                "en": "..."
              }
            }
          }
        }
      },
      "i18n": {
        "tagGraphics": {
          "zh": "图形",
          "en": "Graphics"
        },
        "tag2D3DPlots": {
          "zh": "二维图和三维图",
          "en": "2-D and 3-D Plots"
        },
        "tagLinePlots": {
          "zh": "线图",
          "en": "Line Plots"
        }
      }
    }
    `;
    const result = check_library(input);
    expect(result.length).toBe(4);
    expect(result[0].message).toBe("被 syntax X_Y 引用的 Parameter X 不存在");
    expect(result[0].scope).toStrictEqual(["functions", "plot", "syntaxes", "X_Y"]);
    expect(result[1].message).toBe("被 syntax X_Y_fmt 引用的 Parameter X 不存在");
    expect(result[1].scope).toStrictEqual(["functions", "plot", "syntaxes", "X_Y_fmt"]);
    expect(result[2].message).toBe("被 syntax Xn_Yn 引用的 Parameter X 不存在");
    expect(result[2].scope).toStrictEqual(["functions", "plot", "syntaxes", "Xn_Yn"]);
    expect(result[3].message).toBe("被 syntax Xn_Yn_fmtn 引用的 Parameter X 不存在");
    expect(result[3].scope).toStrictEqual(["functions", "plot", "syntaxes", "Xn_Yn_fmtn"]);
  });
  it("incorrect json with wrong reference 2", () => {
    const input: string = `
     {
      "summary": {
        "zh": "绘图",
        "en": "Plot"
      },
      "functions": {
        "plot": {
          "name": "plot",
          "summary": {
            "zh": "二维线图",
            "en": "2-D Line Plot"
          },
          "tags": [
            "#/i18n/tagGraphics",
            "#/i18n/tag2D3DPlots",
            "#/i18n/tagLinePlots"
          ],
          "since": "2025a",
          "deprecated": false,
          "syntaxes": {
            "X_Y": {
              "description": {
                "zh": "创建 Y 中数据对 X 中对应值的二维线图。……",
                "en": "..."
              },
              "group": 1,
              "params": ["#params/X__", "#params/Y"],
              "examples": ["#examples/createLinePlot"]
            },
            "X_Y_fmt": {
              "description": {
                "zh": "设置线型、标记符号和颜色。",
                "en": "..."
              },
              "group": 1,
              "params": ["#params/X", "#params/Y", "#params/fmt"]
            },
            "Xn_Yn": {
              "description": {
                "zh": "绘制多个X、Y对组的图，所有线条都使用相同的坐标区。",
                "en": "..."
              },
              "group": 1,
              "params": ["#params/X", "#params/Y"],
              "repeated": true,
              "example": ["#examples/plotMultiLines"]
            },
            "Xn_Yn_fmtn": {
              "description": {
                "zh": "设置每个线条的线型、标记符号和颜色。您可以混用 X、Y、fmt 三元组和 X、Y 对组：例如，plot(X1,Y1,X2,Y2,fmt2,X3,Y3)。",
                "en": "..."
              },
              "group": 1,
              "params": ["#params/X", "#params/Y", "#params/fmt__"],
              "repeated": true
            },
            "Y": {
              "description": {
                "zh": "创建 Y 中数据对每个值索引的二维线图。……",
                "en": "..."
              },
              "group": 2,
              "params": ["#params/Y"]
            },
            "Y_fmt": {
              "description": {
                "zh": "设置线型、标记符号和颜色。",
                "en": "..."
              },
              "group": 2,
              "params": ["#params/Y", "#params/fmt"]
            },
            "__KeyValue": {
              "description": {
                "zh": "使用一个或多个 Key=Value 对组参数指定线条属性。可以将此选项与前面语法中的任何输入参数组合一起使用。名称-值对组设置将应用于绘制的所有线条。",
                "en": "..."
              },
              "group": 3,
              "params": ["#params/color", "#params/linestyle", "#params/marker"],
              "usageGroupAt": "beforeParams"
            },
            "ax": {
              "description": {
                "zh": "将在由 ax 指定的坐标区中，而不是在当前坐标区（gca）中创建线条。选项 ax 可以位于前面的语法中的任何输入参数组合之前。",
                "en": "..."
              },
              "group": 3,
              "params": ["#params/ax"],
              "usageGroupAt": "afterParams"
            },
            "h": {
              "description": {
                "zh": "返回由图形线条对象组成的列向量。在创建特定的图形线条后，可以使用 h 修改其属性。",
                "en": "..."
              },
              "group": 4,
              "params": ["#params/h"],
              "usageGroupAt": "beforeParams"
            }
          },
          "examples": {
            "createLinePlot": {
              "summary": {
                "zh": "创建线图",
                "en": "..."
              },
              "description": {
                "zh": "将 x 创建为由 0 和 2π 之间的线性间隔值组成的向量。在各值之间使用递增量 π/100。将 y 创建为 x 的正弦值。创建数据的线图。……",
                "en": "..."
              },
              "syntaxes": ["#syntaxes/X_Y"]
            },
            "plotMultiLines": {
              "summary": {
                "zh": "绘制多个线条",
                "en": "..."
              },
              "description": {
                "zh": "将 x 定义为 100 个介于 −2π 和 2π 之间的线性间隔值。将 y1 和 y2 定义为 x 的正弦和余弦值。创建上述两个数据集的线图。……",
                "en": "..."
              },
              "syntaxes": ["#syntaxes/Xn_Yn"]
            }
          },
          "params": {
            "X": {
              "summary": {
                "zh": "x 值",
                "en": "..."
              },
              "description": {
                "zh": "x 值，指定为标量、向量或矩阵。",
                "en": "..."
              },
              "kind": "posIn",
              "type": [
                "#/builtins/Scalar",
                "#/builtins/Vector",
                "#/builtins/Matrix"
              ],
              "elemType": [
                "#/builtins/Int16",
                "#/builtins/Int32",
                "#/builtins/Int64",
                "#/builtins/Float16",
                "#/builtins/Float32",
                "#/builtins/Float64"
              ]
            },
            "Y": {
              "summary": {
                "zh": "y 值",
                "en": "..."
              },
              "description": {
                "zh": "y 值，指定为标量、向量或矩阵。要根据特定的 x 值绘图，还必须指定 X。",
                "en": "..."
              },
              "kind": "posIn",
              "type": [
                "#/builtins/Scalar",
                "#/builtins/Vector",
                "#/builtins/Matrix"
              ],
              "elemType": [
                "#/builtins/Int16",
                "#/builtins/Int32",
                "#/builtins/Int64",
                "#/builtins/Float16",
                "#/builtins/Float32",
                "#/builtins/Float64"
              ]
            },
            "fmt": {
              "summary": {
                "zh": "线型、颜色和标记",
                "en": "..."
              },
              "description": {
                "zh": "线型、颜色和标记，指定为包含符号的字符串。符号可以按任意顺序显示。您不需要同时指定所有三个特征（线型、颜色和标记）。例如，如果忽略线型，只指定标记，则绘图只显示标记，不显示线条。……",
                "en": "..."
              },
              "kind": "posIn",
              "type": "#/builtins/String"
            },
            "ax": {
              "summary": {
                "zh": "目标坐标区",
                "en": "..."
              },
              "description": {
                "zh": "目标坐标区，指定为 Axes 对象、PolarAxes 对象。如果不指定坐标区或当前坐标区是笛卡尔坐标区，plot 函数将使用当前坐标区。要在极坐标区上绘图，请指定 PolarAxes 对象作为第一个输入参数，或者使用 polarplot 函数。",
                "en": "..."
              },
              "kind": "posIn",
              "type": ["#/builtins/Axes", "#/builtins/PolarAxes"]
            },
            "color": {
              "summary": {
                "zh": "线条颜色",
                "en": "..."
              },
              "description": {
                "zh": "线条颜色，指定为 RGB 三元组、十六进制颜色代码、颜色名称或短名称。……",
                "en": "..."
              },
              "kind": "keyword",
              "type": ["#/builtins/Color", "#/builtins/RGB", "#/builtins/HexColor"],
              "default": "[0, 0.4470, 0.7410]",
              "examples": ["red", "r"]
            },
            "linestyle": {
              "summary": {
                "zh": "线型",
                "en": "..."
              },
              "description": {
                "zh": "线型，指定为下表中列出的选项之一。……",
                "en": "..."
              },
              "kind": "keyword",
              "type": "#/builtins/LineStyle"
            },
            "marker": {
              "summary": {
                "zh": "标记符号",
                "en": "..."
              },
              "description": {
                "zh": "标记符号，指定为下表中的标记之一。默认情况下，图形线条没有标记。通过指定标记符号沿该线条上的每个数据点添加标记。……",
                "en": "..."
              },
              "kind": "keyword",
              "type": "#/builtins/Marker"
            },
            "h": {
              "summary": {
                "zh": "一个或多个图形线条对象",
                "en": "..."
              },
              "description": {
                "zh": "一个或多个图形线条对象，以标量或向量的形式返回。这些是唯一标识符，可以用来查询和修改特定图形线条的属性。",
                "en": "..."
              },
              "kind": "out",
              "type": ["#/builtins/Scalar", "#/builtins/Vector"]
            }
          },
          "footnote": {
            "zh": "# 参考文献",
            "en": "..."
          },
          "seeAlso": ["#/functions/title"],
          "changelogs": {
            "1.0.0": "**v1.0.0** 正式发布！"
          }
        }
      },
      "classes": {
        "Axes": {
          "summary": {
            "zh": "坐标系",
            "en": "..."
          },
          "description": {
            "zh": "...",
            "en": "..."
          },
          "functions": ["#/functions/plot"],
          "constructor": "#/functions/plot"
        }
      },
      "enums": {
        "ObjectiveSense": {
          "summary": {
            "zh": "...",
            "en": "..."
          },
          "description": {
            "zh": "...",
            "en": "..."
          },
          "variants": {
            "minimize": {
              "summary": {
                "zh": "最小化",
                "en": "..."
              },
              "description": {
                "zh": "最小化，指示优化目标为最小化。",
                "en": "..."
              }
            },
            "min": {
              "summary": {
                "zh": "最小化",
                "en": "..."
              },
              "description": {
                "zh": "最小化，指示优化目标为最小化。",
                "en": "..."
              }
            },
            "maximize": {
              "summary": {
                "zh": "最大化",
                "en": "..."
              },
              "description": {
                "zh": "最大化，指示优化目标为最大化。",
                "en": "..."
              }
            },
            "max": {
              "summary": {
                "zh": "最大化",
                "en": "..."
              },
              "description": {
                "zh": "最大化，指示优化目标为最大化。",
                "en": "..."
              }
            }
          }
        }
      },
      "i18n": {
        "tagGraphics": {
          "zh": "图形",
          "en": "Graphics"
        },
        "tag2D3DPlots": {
          "zh": "二维图和三维图",
          "en": "2-D and 3-D Plots"
        },
        "tagLinePlots": {
          "zh": "线图",
          "en": "Line Plots"
        }
      }
    }
    `;
    const result = check_library(input);
    expect(result.length).toBe(2);
    expect(result[0].message).toBe("被 syntax X_Y 引用的 Parameter X__ 不存在");
    expect(result[0].scope).toStrictEqual(["functions", "plot", "syntaxes", "X_Y"]);
    expect(result[1].message).toBe("被 syntax Xn_Yn_fmtn 引用的 Parameter fmt__ 不存在");
    expect(result[1].scope).toStrictEqual(["functions", "plot", "syntaxes", "Xn_Yn_fmtn"]);
  });
});
