<script module>
  //    👆 notice the module context, defineMeta does not work in a regular <script> tag - instance
  import { defineMeta } from '@storybook/addon-svelte-csf';

  import DislikeDialog from './DislikeDialog.svelte';

  //      👇 Get the Story component from the return value
  const { Story } = defineMeta({
    title: 'UI/DislikeDialog',
    component: DislikeDialog,
    decorators: [
      /* ... */
    ],
    parameters: {
      /* ... */
    },
  });

  let messageId = '1';
  let isDislikeDialogOpen = $state(false);
</script>

<Story name="Default" asChild>
  <button onclick={() => (isDislikeDialogOpen = true)}>Open Dialog</button>
  <DislikeDialog {messageId} bind:isDislikeDialogOpen />
</Story>
