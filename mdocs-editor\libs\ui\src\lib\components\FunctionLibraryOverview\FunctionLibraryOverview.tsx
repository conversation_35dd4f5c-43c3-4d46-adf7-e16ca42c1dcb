import * as React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger, Ta<PERSON>Content } from "../ui/tabs";
import { Card } from "../ui/card";
import OverviewTab from "./OverviewTab";
import DefinitionTab from "./DefinitionTab";
import FunctionsTab from "./FunctionsTab";

export interface FunctionInfo {
    /** 函数名称 */
    name: string;
    /** 函数简介 */
    summary?: string;
    /** 函数评分 */
    score?: number;
    /** 函数最后更新时间 */
    lastUpdatedAt?: Date;
    /** 函数最后更新作者 */
    lastUpdatedBy?: string;
    /** 函数是否已废弃 */
    deprecated?: boolean;
}

export interface FunctionLibraryOverviewProps {
    /** 函数库的概述 */
    summary?: string;
    /** 函数库的维护者 */
    maintainer?: string;
    /** 函数库的最后更新时间 */
    lastUpdate?: string | Date;
    /** 函数列表 */
    functions: FunctionInfo[];
    /** 函数库标题 */
    title: string;
    /** 点击函数名时的回调函数 */
    onFunctionClick?: (name: string) => void;
}

/**
 * @summary 函数库概览组件
 * @description 一个展示函数库信息的组件，包含概览、定义和函数列表三个标签页
 * 
 * 主要功能：
 * - 展示函数库的基本信息（概览标签页）
 * - 显示函数库中所有函数的列表（函数标签页）
 * - 支持函数简介的展开查看
 * - 提供函数定义查看功能（待实现）
 * - 响应式布局，自适应容器大小
 * 
 */
export function FunctionLibraryOverview({
    summary,
    maintainer,
    lastUpdate,
    functions,
    title,
    onFunctionClick,
}: FunctionLibraryOverviewProps) {
    // 当前选中的标签页
    const [tab, setTab] = React.useState("overview");
    // 当前打开的函数简介对话框
    const [dialogOpen, setDialogOpen] = React.useState<string | null>(null);

    return (
        <Card className="w-[1200px] h-[700px] rounded-xl shadow-lg border border-gray-200 bg-white p-6 flex flex-col overflow-hidden">
            {/* 标题区域 */}
            <div className="text-2xl font-bold mb-1 select-none">{title}</div>

            {/* 标签页容器 */}
            <Tabs value={tab} onValueChange={setTab} className="w-full h-full flex flex-col">
                <TabsList className="mb-4">
                    <TabsTrigger value="overview">概览</TabsTrigger>
                    <TabsTrigger value="definition">定义</TabsTrigger>
                    <TabsTrigger value="functions">函数</TabsTrigger>
                </TabsList>

                {/* 概览标签页 */}
                <TabsContent value="overview" className="border rounded-lg p-4">
                    <OverviewTab summary={summary ?? ""} maintainer={maintainer ?? ""} lastUpdate={lastUpdate ?? ""} />
                </TabsContent>

                {/* 函数列表标签页 */}
                <TabsContent value="functions" className="flex-1 border rounded-lg p-4 min-h-0">
                    <FunctionsTab
                        functions={functions}
                        onFunctionClick={onFunctionClick}
                        dialogOpen={dialogOpen}
                        setDialogOpen={setDialogOpen}
                    />
                </TabsContent>

                {/* 定义标签页（待实现） */}
                <TabsContent value="definition" className="border rounded-lg p-4">
                    <DefinitionTab />
                </TabsContent>
            </Tabs>
        </Card>
    );
}
export default FunctionLibraryOverview;