<script module lang="ts">
  //    👆 notice the module context, defineMeta does not work in a regular <script> tag - instance
  import { defineMeta } from '@storybook/addon-svelte-csf';
  import FileCard from './FileCard.svelte';

  //      👇 Get the Story component from the return value
  const { Story } = defineMeta({
    title: 'UI/FileCard',
    component: FileCard,
    decorators: [],
    parameters: {},
  });
</script>

<Story
  name="Default"
  args={{
    fileName: '文件名.jpg',
    fileSize: 1024,
    uuid: 'uuid',
    status: 'uploaded',
    onClose: () => {
      console.log('关闭');
    },
  }}
/>

<Story
  name="Uploading"
  args={{
    fileName: '文件名.jpg',
    fileSize: 1024,
    uuid: 'uuid',
    status: 'uploading',
    onClose: () => {
      console.log('关闭');
    },
  }}
/>
