import React from "react";
import { Button } from "../../../ui/button";

/**
 * 视图类型
 */
export type ViewType = 'form' | 'json' | 'preview';

/**
 * 视图切换组件的属性接口
 * @interface ViewToggleProps
 * @description 用于视图切换组件的属性类型定义。
 * @property currentView 当前视图类型
 * @property onViewChange 视图切换回调
 * @property disabled 是否禁用
 */
export interface ViewToggleProps {
  /** 当前视图类型 */
  currentView: ViewType;
  /** 视图切换回调 */
  onViewChange: (view: ViewType) => void;
  /** 是否禁用 */
  disabled?: boolean;
}

/**
 * 视图切换组件
 *
 * 提供表单视图、JSON 视图和预览视图之间的切换功能
 */
export const ViewToggle: React.FC<ViewToggleProps> = ({
  currentView,
  onViewChange,
  disabled = false,
}) => {
  return (
    <div className="flex items-center gap-1 bg-gray-100 p-1 rounded-lg">
      <Button
        type="button"
        variant={currentView === 'form' ? 'default' : 'ghost'}
        size="sm"
        onClick={() => onViewChange('form')}
        disabled={disabled}
        className="h-8 px-3"
      >
        表单视图
      </Button>
      <Button
        type="button"
        variant={currentView === 'json' ? 'default' : 'ghost'}
        size="sm"
        onClick={() => onViewChange('json')}
        disabled={disabled}
        className="h-8 px-3"
      >
        JSON 视图
      </Button>
      <Button
        type="button"
        variant={currentView === 'preview' ? 'default' : 'ghost'}
        size="sm"
        onClick={() => onViewChange('preview')}
        disabled={disabled}
        className="h-8 px-3"
      >
        预览
      </Button>
    </div>
  );
};
