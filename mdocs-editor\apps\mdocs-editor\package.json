{"name": "@mdocs/editor", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate reset --force && prisma migrate dev --name init"}, "dependencies": {"@mdocs/ui": "workspace:*", "@mdocs/schema": "workspace:*", "next": "15.3.5", "react": "catalog:react19", "react-dom": "catalog:react19", "@prisma/client": "6.12.0"}, "devDependencies": {"@tailwindcss/postcss": "catalog:react19", "@types/node": "catalog:dev", "@types/react": "catalog:react19", "@types/react-dom": "catalog:react19", "eslint-config-next": "15.3.5", "prisma": "6.12.0", "tailwindcss": "catalog:react19", "typescript": "catalog:dev", "tsx": "4.20.3"}}