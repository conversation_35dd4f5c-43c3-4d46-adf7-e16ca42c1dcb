import { 
  Brain, 
  BarChart3, 
  CheckCircle
} from "lucide-react";
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from "../ui/tooltip";

/**
 * @interface IconBarProps
 * @description 用于图标栏的属性类型定义。
 * @property activeTab 当前激活的标签
 * @property onTabChange 标签切换回调
 */
export interface IconBarProps {
  activeTab: "llm" | "statistics" | "validation";
  onTabChange: (tab: "llm" | "statistics" | "validation") => void;
}

const tabs = [
  { 
    id: "llm" as const, 
    icon: Brain, 
    label: "大模型评审",
  },
  { 
    id: "statistics" as const, 
    icon: BarChart3, 
    label: "统计信息",
  },
  { 
    id: "validation" as const, 
    icon: CheckCircle, 
    label: "校验结果",
  },
];

export function IconBar({ activeTab, onTabChange }: IconBarProps) {
  return (
    <TooltipProvider>
      <div className="flex flex-col gap-2 w-full items-center">
        {tabs.map(({ id, icon: Icon, label }) => (
          <Tooltip key={id}>
            <TooltipTrigger asChild>
              <button
                onClick={() => onTabChange(id)}
                className={`
                  w-8 h-8 rounded-lg flex items-center justify-center transition-all duration-200
                  ${activeTab === id 
                    ? `bg-white border text-blue-600` 
                    : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100' // hover:bg-gray-100 鼠标悬停时背景色变化，可选，可删除。
                  }
                `}
                title={label}
              >
                <Icon className="w-4 h-4" />
              </button>
            </TooltipTrigger>
            <TooltipContent side="left">
              <span>{label}</span>
            </TooltipContent>
          </Tooltip>
        ))}
      </div>
    </TooltipProvider>
  );
}
