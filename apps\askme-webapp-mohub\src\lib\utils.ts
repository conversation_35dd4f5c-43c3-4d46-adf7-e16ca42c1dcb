import type { Message } from '$lib/generated/prisma';
import type { JSONValue, UIMessage } from 'ai';
import type { UIAttachment, UserPreferancesCookies } from '@askme/lib-common';
import { userPreferancesCookiesValidator, DEFAULT_USER_PREFERANCES_COOKIES } from '$lib/config';
import * as LogFormat from '@askme/lib-common/logformat';

/** 将Prisma Message转为UIMessage */
export function convertMsg(msg: Message): UIMessage {
  return {
    id: msg.id,
    role: msg.role as UIMessage['role'],
    parts: msg.parts as UIMessage['parts'],
    content: '',
    experimental_attachments: msg.attachments
      ? (JSON.parse(msg.attachments as string) as UIAttachment[])
      : undefined,
    createdAt: msg.createdAt,
    annotations: msg.annotations ? (msg.annotations as JSONValue[]) : undefined,
  };
}
/**
 * 从数组中随机取m个元素，不重复
 * @param array 要取得元素的数组
 * @param m 随机取的元素数
 * @returns 随机取的m个元素
 */
export function getRandomElements<T>(array: T[], m: number): T[] {
  if (m > array.length) {
    throw new Error('m 不能大于数组长度');
  }

  const result: T[] = [];
  const usedIndices = new Set<number>();

  while (result.length < m) {
    const i = Math.floor(Math.random() * array.length);
    if (!usedIndices.has(i)) {
      usedIndices.add(i);
      result.push(array[i]);
    }
  }

  return result;
}

/**
 * 根据validator验证用户偏好cookie数据合法性,不合法说明被不当修改,还原默认用户偏好cookies
 * @param userPreferancesCookiesValue 用户偏好cookie数据字符串
 * @returns 验证后的用户cookie数据
 */
export function normalizeUserPreferancesCookies(userPreferancesCookiesValue: string | null) {
  let finalValue = JSON.stringify(DEFAULT_USER_PREFERANCES_COOKIES);

  //userPreferancesCookiesValue是一个json字符串，需要解析
  if (userPreferancesCookiesValue) {
    try {
      const parsedValue = JSON.parse(userPreferancesCookiesValue) as UserPreferancesCookies;
      // 使用validator验证数据合法性
      if (userPreferancesCookiesValidator(parsedValue)) {
        // 数据合法
        finalValue = userPreferancesCookiesValue;
      }
    } catch {
      console.warn(
        LogFormat.warn('Failed to parse userPreferances cookie value, use default value instead.'),
      );
    }
  }
  return finalValue;
}

/**
 * 根据用户偏好cookie数据和用户消息内容选择模型
 * @param isReasoningEnabled 是否开启深度思考
 * @param lastUserMessage 最后一条用户消息
 */
export function getSelectedModel(isReasoningEnabled: boolean, lastUserMessage: UIMessage): string {
  let selectedModel = isReasoningEnabled ? 'REASON_MODAL' : 'CHAT_MODEL';
  // 附件里有图片，自动切换到多模态模型
  for (const attachment of lastUserMessage.experimental_attachments || []) {
    if (attachment.contentType?.startsWith('image')) {
      selectedModel = 'MULTI_MODAL_MODEL';
      break;
    }
  }
  return selectedModel;
}

/**
 * 在原始字符串中截取包含关键字的子串，并通过指定截取长度来取得关键字周围的字符
 * @param text 原字符串
 * @param keyword 截取子串
 * @param length 截取长度
 * @returns
 */
export function findSubstringWithLength(
  text: string,
  keyword: string,
  length: number,
): string | undefined {
  const index = text.toLowerCase().indexOf(keyword.toLowerCase()); // 只查找第一个匹配

  if (index !== -1) {
    // 如果找到匹配
    const start = Math.max(0, index - Math.floor((length - keyword.length) / 2));
    let substr = text.slice(start, start + length);
    // 如果匹配到的子串不在字符串头，加上省略号
    if (index !== 0) {
      substr = '...' + substr;
    }
    return substr;
  }
  return ''; // 如果未找到，返回空字符串
}
