import { getContext, setContext, hasContext } from 'svelte';
import type { ChatModel, UserPreferancesCookies } from '@askme/lib-common';
import { SvelteMap } from 'svelte/reactivity';
import { UseCookies } from './useCookies.svelte';

// ⚠️ 响应式context设置说明:
// ⚠️ setContext可以<直接设置响应式对象>的情况:数组/svelteMap/svelteSet,包含响应式字段的对象
// ⚠️ setContext需要<设置为返回响应式对象的函数>的情况： 单独的值（字符串，数字等），需要包裹在响应式对象中，setContext传入<返回这个响应式对象的函数>（参考setChatIdContext的使用）

const keyChat = { key: 'chatHistory' };
export function setChatHistoryContext(chatHistory: ChatModel[]) {
  setContext(keyChat, chatHistory);
}

export function getChatHistoryContext() {
  return getContext(keyChat) as ChatModel[];
}

export function hasChatHistoryContext() {
  return hasContext(keyChat);
}

const keyVote = { key: 'vote' };

export function setVoteContext(vote: SvelteMap<string, boolean>) {
  setContext(keyVote, vote);
}

export function getVoteContext() {
  return getContext(keyVote) as SvelteMap<string, boolean>;
}

export function hasVoteContext() {
  return hasContext(keyVote);
}

const keyChatId = { key: 'chatId' };

export function setChatIdContext(chatId: () => { chatId: string | undefined }) {
  setContext(keyChatId, chatId);
}

export function getChatIdContext() {
  return getContext(keyChatId) as () => { chatId: string | undefined };
}

export function hasChatIdContext() {
  return hasContext(keyChatId);
}

const keyUserPreferancesCookies = { key: 'userPreferancesCookies' };

export function setUserPreferancesCookiesContext(
  userPreferancesCookies: UseCookies<UserPreferancesCookies>,
) {
  setContext(keyUserPreferancesCookies, userPreferancesCookies);
}

export function getUserPreferancesCookiesContext() {
  return getContext(keyUserPreferancesCookies) as UseCookies<UserPreferancesCookies>;
}

export function hasUserPreferancesCookiesContext() {
  return hasContext(keyUserPreferancesCookies);
}

// TODO: useLocalstorage用法：
// const keyUserPreferances = { key: 'userPreferances' };

// export function setUserPreferancesContext(userPreferances: LocalStorageHook<UserPreferances>) {
//   setContext(keyUserPreferances, userPreferances);
// }

// export function getUserPreferancesContext() {
//   return getContext(keyUserPreferances) as LocalStorageHook<UserPreferances>;
// }

// export function hasUserPreferancesContext() {
//   return hasContext(keyUserPreferances);
// }
