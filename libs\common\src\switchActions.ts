import type { TreeNodeInfo } from './messageTree';

/** 用户在左右切换重新生成的bot message时，本质是一个动作记录。
 *  记录被切换的节点的父节点消息ID，和选择的子节点索引，若父节点为根节点，messageId为null。
 *  ⚠️为SwitchAction列表插入新action必须调用insertAction方法，否则会有意外情况
 */
export interface SwitchAction {
  /** 父节点消息ID */
  messageId: string | null;
  /** 选择的子节点 */
  childIndex: number;
}
/**
 * 在actions列表中插入新的action
 * @param actions 目前的actions列表
 * @param newAction 待插入动作
 * @param infoMap messageId => TreeNodeInfo 的索引Map
 * @returns
 */
export function insertAction(
  actions: SwitchAction[],
  newAction: SwitchAction,
  infoMap: Map<string | null, TreeNodeInfo>,
): SwitchAction[] {
  const newActionInfo = infoMap.get(newAction.messageId);
  if (newActionInfo === undefined) {
    throw new Error('Message ID not found in info map');
  }

  let insertIndex = 0;
  while (
    insertIndex < actions.length &&
    infoMap.get(actions[insertIndex].messageId)!.level < newActionInfo.level
  ) {
    insertIndex++;
  }

  return [...actions.slice(0, insertIndex), newAction];
}
