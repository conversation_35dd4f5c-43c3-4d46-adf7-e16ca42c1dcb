import { useState } from 'react';
import { Plus, Upload } from 'lucide-react';
import { Button } from '../ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '../ui/tooltip';
import ImportDialog from '../ImportDialog/ImportDialog';

interface FunctionListToolbarProps {
  /** 新建回调 */
  onCreate?: () => void;
  /** 导入回调 */
  onImport?: (file: File) => void;
}

/**
 * FunctionListToolbar 组件
 * 
 * 工具栏，包含新建、导入操作
 * 使用图标 + tooltip 的形式
 */
export function FunctionListToolbar({
  onCreate,
  onImport
}: FunctionListToolbarProps) {
  const [importDialogOpen, setImportDialogOpen] = useState(false);

  // 处理导入提交
  const handleImportSubmit = (file: File) => {
    if (!onImport) return;

    try {
      onImport(file);
      setImportDialogOpen(false);
    } catch (error) {
      console.error('导入函数失败:', error);
      // 保持对话框打开，让用户可以重试
    }
  };



  return (
    <TooltipProvider>
      {/* 新建函数 */}
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onCreate?.()}
            className="h-8 w-8 p-0 hover:bg-blue-50 hover:text-blue-600"
          >
            <Plus className="h-4 w-4" />
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          <p>新建函数</p>
        </TooltipContent>
      </Tooltip>

      {/* 导入函数 */}
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setImportDialogOpen(true)}
            className="h-8 w-8 p-0 hover:bg-blue-50 hover:text-blue-600"
          >
            <Upload className="h-4 w-4" />
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          <p>导入函数</p>
        </TooltipContent>
      </Tooltip>



      {/* 导入对话框 */}
      <ImportDialog
        open={importDialogOpen}
        onOpenChange={setImportDialogOpen}
        onImport={handleImportSubmit}
        allowedFileTypes=".json,.md"
      />
    </TooltipProvider>
  );
}
