import React, { useState } from "react";
import { Button } from "../../ui/button";
import { Input } from "../../ui/input";
import { Label } from "../../ui/label";
import { Plus, X } from "lucide-react";
import { I18nInput } from "./ui/I18nInput";
import { ArrayManager } from "./ui/ArrayManager";
import { useFunctionContent } from "../hooks/useFunctionContent";
import type { I18nText } from "./ui/I18nInput";
import { FunctionContent } from "../FunctionEditor";
import { TagInput } from "./ui/TagInput";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../../ui/select";
import { Options } from "../views/FormView";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "../../ui/dialog";

// MWORKS 元数据格式规范中定义的参数类别选项
const PARAM_KIND_OPTIONS: Options[] = [
  { value: "posIn", label: "位置入参 (posIn)" },
  { value: "keyword", label: "关键词参数 (keyword)" },
  { value: "out", label: "输出参数 (out)" },
];
/**
 * 参数内容接口
 */
interface ParameterContent {
  /**参数摘要 */
  summary: I18nText;
  /**参数的详细描述 */
  description: I18nText;
  /**参数类别：位置输入参数、关键词参数、输出参数 */
  kind: string;
  /**参数类型 */
  type: string | string[];
  /**参数的元素类型 */
  elemType?: string | string[];
  /**参数默认值 */
  default?: string;
  /**参数示例代码（跟examples字段不同，是单数的参数例子） */
  examples?: string[];
}

/**
 * 参数管理部分组件的属性接口
 */
export interface ParametersSectionProps {
  /** 函数内容数据 */
  content: Partial<FunctionContent>;
  /** 内容更新回调函数 */
  onContentChange: (updates: Partial<FunctionContent>) => void;
  /** 是否处于加载状态 */
  isLoading?: boolean;
  /** 可选的参数类型选项，如果不提供则使用默认选项 */
  typeOptions?: Options[];
}

/**
 * 参数管理部分组件
 * 
 * 负责管理函数的参数定义，包括：
 * - 添加/删除参数
 * - 编辑参数名称和描述
 * - 配置参数类型和属性
 */
export const ParametersSection: React.FC<ParametersSectionProps> = ({
  content,
  onContentChange,
  isLoading = false,
  typeOptions = [],
}) => {
  const { updateField } = useFunctionContent({
    content,
    onContentChange,
  });
  // 用于记录当前要删除的参数名
  const [dialogParamName, setDialogParamName] = useState<string | null>(null);

  /**
   * 获取参数记录
   */
  const getParameters = (): Record<string, ParameterContent> => {
    return (content.params as unknown as Record<string, ParameterContent>) || {};
  };

  /**
   * 添加新参数
   */
  const addParameter = () => {
    const params = getParameters();
    const newParamName = `param${Object.keys(params).length + 1}`;
    const newParam: ParameterContent = {
      summary: { zh: '', en: '' },
      description: { zh: '', en: '' },
      kind: 'posIn',
      type: '#/builtins/Any',
    };
    // 新参数放在顶部
    updateField('params', { [newParamName]: newParam, ...params });
  };

  /**
   * 删除参数
   * @param paramName 参数名称
   */
  const removeParameter = (paramName: string) => {
    const params = getParameters();
    const newParams = { ...params };
    delete newParams[paramName];
    updateField('params', newParams);
  };

  /**
   * 重命名参数
   * @param oldName 旧名称
   * @param newName 新名称
   */
  const renameParameter = (oldName: string, newName: string) => {
    if (oldName === newName || !newName.trim()) return;

    const params = getParameters();
    // if (params[newName]) {
    //   alert('参数名已存在！');
    //   return;
    // }

    const param = params[oldName];
    if (param) {
      // 保持原有顺序，只更改参数名
      const newParams: Record<string, ParameterContent> = {};
      Object.entries(params).forEach(([key, val]) => {
        if (key === oldName) {
          newParams[newName] = val;
        } else {
          newParams[key] = val;
        }
      });
      updateField('params', newParams);
    }
  };

  /**
   * 更新参数字段
   * @param paramName 参数名称
   * @param field 字段名
   * @param value 新值
   */
  const updateParameter = (paramName: string, field: keyof ParameterContent, value: any) => {
    const params = getParameters();
    const param = params[paramName];
    if (param) {
      const newParams: Record<string, ParameterContent> = {};
      Object.entries(params).forEach(([key, val]) => {
        if (key === paramName) {
          newParams[key] = { ...val, [field]: value };
        } else {
          newParams[key] = val;
        }
      });
      updateField('params', newParams);
    }
  };

  /**
   * 获取类型数组（用于渲染）
   */
  const getTypeArray = (param: ParameterContent): string[] => {
    return Array.isArray(param.type) ? param.type : (param.type ? [param.type] : []);
  };

  /**
   * 获取元素类型数组（用于渲染）
   */
  const getElemTypeArray = (param: ParameterContent): string[] => {
    return Array.isArray(param.elemType) ? param.elemType : (param.elemType ? [param.elemType] : []);
  };

  /**
   * 添加标签到参数类型
   */
  const handleAddTag = (paramName: string) => (tagValue: string) => {
    const params = getParameters();
    const param = params[paramName];
    if (param) {
      const currentTypes = getTypeArray(param);
      if (!currentTypes.includes(tagValue)) {
        const newTypes = [...currentTypes, tagValue];
        updateParameter(paramName, 'type', newTypes.length === 1 ? newTypes[0] : newTypes);
      }
    }
  };

  /**
   * 从参数类型中移除标签
   */
  const handleRemoveTag = (paramName: string) => (index: number) => {
    const params = getParameters();
    const param = params[paramName];
    if (param) {
      const currentTypes = getTypeArray(param);
      const newTypes = currentTypes.filter((_, i) => i !== index);
      updateParameter(paramName, 'type', newTypes.length === 0 ? undefined : (newTypes.length === 1 ? newTypes[0] : newTypes));
    }
  };

  /**
   * 添加元素类型标签
   */
  const handleAddElemTag = (paramName: string) => (tagValue: string) => {
    const params = getParameters();
    const param = params[paramName];
    if (param) {
      const currentElemTypes = getElemTypeArray(param);
      if (!currentElemTypes.includes(tagValue)) {
        const newElemTypes = [...currentElemTypes, tagValue];
        updateParameter(paramName, 'elemType', newElemTypes.length === 1 ? newElemTypes[0] : newElemTypes);
      }
    }
  };

  /**
   * 从元素类型中移除标签
   */
  const handleRemoveElemTag = (paramName: string) => (index: number) => {
    const params = getParameters();
    const param = params[paramName];
    if (param) {
      const currentElemTypes = getElemTypeArray(param);
      const newElemTypes = currentElemTypes.filter((_, i) => i !== index);
      updateParameter(paramName, 'elemType', newElemTypes.length === 0 ? undefined : (newElemTypes.length === 1 ? newElemTypes[0] : newElemTypes));
    }
  };

  const parameters = getParameters();

  return (
    <div className="space-y-4">
      {/* 添加参数按钮 */}
      <div className="flex items-center justify-between">
        <span className="text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
          {Object.keys(parameters).length} 个参数
        </span>
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={addParameter}
          disabled={isLoading}
        >
          <Plus className="w-4 h-4 mr-1" />
          添加参数
        </Button>
      </div>

      {/* 参数列表 */}
      <div className="space-y-6">
        {Object.entries(parameters).map(([paramName, param]) => (
          <div key={paramName} className="border rounded-lg p-4 space-y-4">
            {/* 参数名称和删除按钮 */}
            <div className="flex items-center justify-between gap-4">
              <div className="flex items-center gap-2 flex-1">
                <Label className="text-base font-medium">参数ID<span className="text-red-500">*</span></Label>
                <Input
                  key={`param-name-${paramName}`}
                  defaultValue={paramName}
                  onBlur={(e) => {
                    const newName = e.target.value.trim();
                    if (newName && newName !== paramName) {
                      renameParameter(paramName, newName);
                    }
                  }}
                  disabled={isLoading}
                  placeholder="参数名称"
                  className="flex-1"
                />
              </div>
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={() => setDialogParamName(paramName)}
              disabled={isLoading}
            >
              <X className="w-4 h-4" />
            </Button>
            <Dialog open={dialogParamName === paramName} onOpenChange={(open) => {
              if (!open) setDialogParamName(null);
            }}>
              <DialogContent className="sm:max-w-md">
                <DialogHeader>
                  <DialogTitle>确认删除</DialogTitle>
                  <DialogDescription>
                    此操作无法撤销。您确定要删除参数 {paramName} 吗？
                  </DialogDescription>
                </DialogHeader>
                <DialogFooter>
                  {/* 取消按钮 - 关闭弹窗 */}
                  <Button onClick={() => setDialogParamName(null)}>
                    取消
                  </Button>
                  {/* 确认删除按钮 - 执行删除并关闭弹窗 */}
                  <Button
                    onClick={() => {
                      removeParameter(paramName);
                      setDialogParamName(null);
                    }}
                  >
                    确认删除
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
            </div>

            {/* 参数基本信息 */}
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label className="text-base font-medium">参数类别 <span className="text-red-500">*</span></Label>
                <Select
                  value={param.kind}
                  onValueChange={(value: string) => {
                    updateParameter(paramName, 'kind', value);
                  }}
                  disabled={isLoading}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder={"请选择参数类别"} />
                  </SelectTrigger>

                  <SelectContent>
                    {PARAM_KIND_OPTIONS.map((opt) => (
                      <SelectItem key={opt.value} value={opt.value} className="py-3">
                        <div className="flex flex-col">
                          <span className="font-medium">{opt.label}</span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label className="text-base font-medium">默认值</Label>
                <Input
                  id={`default-${paramName}`}
                  value={param.default || ''}
                  onChange={(e) => updateParameter(paramName, 'default', e.target.value)}
                  disabled={isLoading}
                  placeholder="参数默认值"
                />
              </div>
            </div>

            {/* 参数摘要 */}
            <I18nInput
              label="参数摘要"
              value={param.summary}
              onChange={(value) => updateParameter(paramName, 'summary', value)}
              disabled={isLoading}
              placeholderZh="参数的简短描述"
              placeholderEn="Brief parameter description"
              idPrefix={`param-summary-${paramName}`}
              required={true}
            />

            {/* 参数详细描述 */}
            <I18nInput
              label="详细描述"
              value={param.description}
              onChange={(value) => updateParameter(paramName, 'description', value)}
              disabled={isLoading}
              type="textarea"
              rows={3}
              placeholderZh="参数的详细说明"
              placeholderEn="Detailed parameter description"
              idPrefix={`param-desc-${paramName}`}
              required={true}
            />

            {/* 参数类型 */}
            <TagInput
              tags={getTypeArray(param)}
              availableTags={typeOptions}
              onTagAdd={handleAddTag(paramName)}
              onTagRemove={handleRemoveTag(paramName)}
              required={true}
              disabled={isLoading}
              label="参数类型"
              placeholder="选择参数类型..."
              className="text-base"
            />

            {/* 元素类型（可选） */}
            <TagInput
              tags={getElemTypeArray(param)}
              availableTags={typeOptions}
              onTagAdd={handleAddElemTag(paramName)}
              onTagRemove={handleRemoveElemTag(paramName)}
              disabled={isLoading}
              label="元素类型"
              placeholder="选择元素类型..."
              className="text-base"
            />

            {/* 参数示例 */}
            <ArrayManager
              label="参数示例"
              value={param.examples || []}
              onChange={(value) => updateParameter(paramName, 'examples', value)}
              disabled={isLoading}
              placeholder="参数使用示例"
              addButtonText="添加示例"
              helpText="参数的使用示例"
            />
          </div>
        ))}

        {/* 空状态提示 */}
        {Object.keys(parameters).length === 0 && (
          <div className="text-center py-1 text-gray-500">
            <span className="mb-2 text-sm">暂无参数定义</span>
            <span className="text-sm">点击上方按钮添加参数</span>
          </div>
        )}
      </div>
    </div>
  );
};
