import { useState, useMemo } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/lib/components/ui/card";
import { Input } from "@/lib/components/ui/input";
import { LibraryTable } from "./components/LibraryTable";
import { LibraryToolbar } from "./components/LibraryToolbar";

/**
 * Library 数据接口
 */
export interface Library {
  /** Library 名称 */
  name: string;
  /** 最新版本 */
  latestVersion?: string;
  /** 最后更新时间 */
  lastUpdated?: Date;
  /** 摘要信息 */
  summary?: string;
}

/**
 * LibraryManager 组件属性接口
 */
export interface LibraryManagerProps {
  /** Library 列表数据 */
  libraries: Library[];
  /** Library 点击回调函数 */
  onLibraryClick?: (library: Library) => void;
  /** 导入 Library 回调函数 */
  onImportLibrary?: (file: File) => void;
  /** 导出 Library 回调函数 */
  onExportLibrary?: (library: Library) => void;
  /** 导出所有 Library 回调函数 */
  onExportAllLibraries?: () => void;
  /** 删除 Library 回调函数 */
  onDeleteLibrary?: (libraryId: string) => void;
  /** 加载状态 */
  loading?: boolean;
}

/**
 * LibraryManager 组件
 * 功能：
 * - 展示 Library 列表
 * - 支持搜索过滤
 * - 支持导入、导出、删除、导出所有操作
 * 
 */
export function LibraryManager({
  libraries,
  onLibraryClick,
  onImportLibrary,
  onExportLibrary,
  onExportAllLibraries,
  onDeleteLibrary,
  loading = false,
}: LibraryManagerProps) {
  const [searchValue, setSearchValue] = useState("");
  const [currentPage, setCurrentPage] = useState(1);

  const pageSize = 10;

  // 过滤库
  const filteredLibraries = useMemo(() => {
    if (!searchValue.trim()) {
      return libraries;
    }

    const searchTerm = searchValue.toLowerCase();
    return libraries.filter(library =>
      library.name.toLowerCase().includes(searchTerm) ||
      library.summary?.toLowerCase().includes(searchTerm)
    );
  }, [libraries, searchValue]);



  // 处理导出
  const handleExport = async (library: Library) => {
    if (onExportLibrary) {
      await onExportLibrary(library);
    }
  };

  // 处理删除
  const handleDelete = async (library: Library) => {
    if (onDeleteLibrary) {
      await onDeleteLibrary(library.name);
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Library 管理</CardTitle>
        <CardDescription>
          管理和维护你的函数库
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* 搜索和操作工具栏 */}
        <div className="flex items-center justify-between gap-4">
          <div className="flex-1 max-w-sm">
            <Input
              type="text"
              placeholder="搜索函数库..."
              value={searchValue}
              onChange={(e) => setSearchValue(e.target.value)}
              disabled={loading}
            />
          </div>
          <LibraryToolbar
            disabled={loading}
            libraryCount={libraries.length}
            onImport={onImportLibrary}
            onExportAll={onExportAllLibraries}
          />
        </div>

        {/* 表格 */}
        <LibraryTable
          libraries={filteredLibraries}
          onLibraryClick={onLibraryClick}
          onExport={handleExport}
          onDelete={handleDelete}
          loading={loading}
          currentPage={currentPage}
          pageSize={pageSize}
          total={filteredLibraries.length}
          onPageChange={setCurrentPage}
        />
      </CardContent>
    </Card>
  );
}
export default LibraryManager;
