import fs from 'fs';
import path from 'path';
import * as LogFormat from '@askme/lib-common/logformat';

// 文件缓存数据结构
type CacheEntry<T> = {
  data: T;
  initialized: boolean;
  fallback: T;
};

// 文件缓存，key为filepath
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const cacheMap: Record<string, CacheEntry<any>> = {};

/**
 * 通用文件读取缓存（带监听）
 * @param file 相对或绝对路径
 * @param parseFn 将文件内容序列化为JS对象的函数，默认返回原始字符串
 * @param fallback 解析失败时返回的默认值
 * @param refreshIntervalInMs 文件监听轮询间隔，单位毫秒，默认10秒
 * @returns 缓存的文件内容被序列化后的对象
 */
export function getCachedFile<T>(
  file: string,
  parseFn: (text: string) => T = (text) => text as unknown as T,
  fallback: T,
  refreshIntervalInMs = 10000,
): T {
  const filePath = path.resolve(file);

  // 初始化缓存item
  if (!cacheMap[filePath]) {
    cacheMap[filePath] = {
      data: fallback,
      fallback,
      initialized: false,
    };
  }

  const entry = cacheMap[filePath];

  // 加载文件
  function loadFile() {
    try {
      const content = fs.readFileSync(filePath, 'utf-8');
      // 根据传入的函数将文件内容序列化为JS对象
      entry.data = parseFn(content);
    } catch (err) {
      console.error(LogFormat.error(`[fileCache] load failed: ${filePath} ,` + err));
      entry.data = entry.fallback;
    }
  }

  // 初始化文件监听
  if (!entry.initialized) {
    loadFile();
    fs.watchFile(filePath, { interval: refreshIntervalInMs }, (curr, prev) => {
      if (curr.mtimeMs !== prev.mtimeMs) {
        console.debug(LogFormat.debug(`[fileCache] file changed: ${filePath}`));
        loadFile();
      }
    });
    entry.initialized = true;
  }

  return entry.data;
}
