#! /bin/bash

set -e

echo "正在部署分支: ${CI_COMMIT_REF_NAME}"

# 拷贝环境变量文件
scp -i id_rsa -o StrictHostKeyChecking=no $CONFIG_ENVFILE gitlab-bot@***********:$DEPLOY_REPO_DIR/deployments/askme/.env

# 远程执行部署命令
ssh -i id_rsa -o StrictHostKeyChecking=no gitlab-bot@*********** <<EOF
    set -e
    cd "$DEPLOY_REPO_DIR/deployments/askme"
    git fetch origin
    git checkout "${CI_COMMIT_REF_NAME}"
    git reset --hard "origin/${CI_COMMIT_REF_NAME}" --
    docker-compose pull
    docker-compose down
    docker-compose up -d
EOF
