# 构建

mdocs-editor 基于 NextJS 开发，并通过 `standalone` 模式构建，从而能够以 docker container 的形式部署。

## 构建

### 镜像构建

由于这是一个 monorepo 多项目仓库，镜像构建需要在项目根目录下进行，而不是在 `apps/mdocs-editor` 目录下。

```bash
docker build -f apps/mdocs-editor/Dockerfile -t mdocs-editor:latest .
```

该项目通过 CI 构建镜像并发布到 [部署仓库](https://git.tongyuan.cc/mdocs/deployment)。

### 本地构建

本地构建分为两阶段进行：

1. 构建所有依赖库: `pnpm run build:libs`
2. 构建文档编辑器: `pnpm --filter ./apps/mdocs-editor build`

构建好的产物位于 `.next` 目录下，为了能够通过 `node` 直接运行，需要将以下三部分内容复制到 `dist` 目录下：

* `.next/standalone/apps/mdocs-editor` --> `dist/`
* `.next/static` --> `dist/.next/static`
* `public` --> `dist/public`

构建后的目录结构如下所示：

```
dist
├── .next
│   ├── ...         # 构建产物
│   └── static      # 构建静态产物: 从 .next/static 复制而来
├── public          # 静态资源
└── server.js       # 启动脚本
```

通过 `node dist/server.js` 即可启动文档编辑器。
当然，这需要确保 node 运行时环境有包含 `next` 在内的相关依赖。
