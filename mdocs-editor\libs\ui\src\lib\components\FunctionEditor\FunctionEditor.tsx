import { useState} from "react";
import { FunctionEditorHeader} from "./components/FunctionEditorHeader";
import { FormView, Options } from "./views/FormView";
import { JsonView } from "./views/JsonView";
import type { JlFunction } from "@mdocs/schema";


/**
 * 函数内容接口
 */
export type FunctionContent = JlFunction;

/**
 * 函数编辑器的属性接口
 */
export interface FunctionEditorProps {
  /** 函数内容数据 */
  functionData?: Partial<FunctionContent>;
  /** 函数数据更新回调 */
  onDataChange?: (data: Partial<FunctionContent>) => void;
  /** 是否处于加载状态 */
  isLoading?: boolean;
  /** 自定义样式类名 */
  className?: string;
  /** 可选的标签列表 */
  availableTags?: Options[];
  /** 函数名称 */
  functionID: string;
  /** 可选的函数引入版本列表 */
  availableVersions?: Options[];
  /** 可选的参数类型列表 */
  availableTypes?: Options[];
}

/**
 * 函数编辑器组件
 * @param props FunctionEditorProps
 */
export function FunctionEditor({
  functionData = {},
  onDataChange,
  isLoading = false,
  className = "",
  availableTags = [],
  functionID,
  availableVersions = [],
  availableTypes = [],
}: FunctionEditorProps) {
  // 视图状态管理
  const [currentView, setCurrentView] = useState<'form' | 'json' | 'preview'>('form');
  const functionName = functionData.name ? functionData.name : functionID;

  return (
    <div className={`h-full flex flex-col min-h-20 ${className}`}>
      {/* 函数编辑器头部 */}
      <FunctionEditorHeader
        functionData={functionData}
        onDataChange={onDataChange || (() => {})}
        currentView={currentView}
        onViewChange={setCurrentView}
        isLoading={isLoading}
        availableTags={availableTags}
        functionName={functionName}
      />

      {/* 内容区域 */}
      <div className={`flex-1 ${currentView === 'form' ? 'overflow-auto' : 'overflow-hidden'}`}>
        {currentView === 'form' ? (
          <FormView
            functionData={functionData}
            onDataChange={onDataChange || (() => {})}
            isLoading={isLoading}
            availableVersions={availableVersions}
            availableTypes={availableTypes}
            functionName={functionName}
          />
        ) : (
          <JsonView
            functionData={functionData}
            onDataChange={onDataChange || (() => {})}
            isLoading={isLoading}
          />
        )}
      </div>
    </div>
  );
};

export default FunctionEditor;
