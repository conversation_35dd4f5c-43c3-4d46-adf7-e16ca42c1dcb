<script lang="ts">
  import { <PERSON><PERSON><PERSON>oggle, <PERSON><PERSON><PERSON><PERSON><PERSON>on, ThemeSelector } from '@askme/lib-ui';

  interface Props {
    isSSO: boolean;
    enableThemeSwitcher: boolean;
  }
  const { isSSO, enableThemeSwitcher }: Props = $props();
</script>

<header class="bg-background sticky top-0 flex items-center justify-between gap-2 p-2">
  <!-- 顶部菜单左侧按钮 -->
  <div class="flex items-center gap-2">
    <SidebarToggle />
  </div>
  <!-- 顶部菜单右侧按钮 -->
  <div class="flex items-center gap-2">
    {#if enableThemeSwitcher}
      <ThemeSelector />
    {/if}
    {#if !isSSO}
      <div class="ml-auto"><LogoutButton /></div>
    {/if}
  </div>
</header>
