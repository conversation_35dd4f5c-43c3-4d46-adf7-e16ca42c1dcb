# MWORKS 同元智能助手 (MoHub 版)

## 构建

构建前需要先构建 `libs/ui` 项目，然后再构建本项目。

```bash
# 在项目根目录下
pnpm build:libs
pnpm run --filter "./apps/askme-webapp-mohub" build

# 生产环境启动
cd apps/askme-webapp-mohub
node -r dotenv/config build/index.js
```

## 配置

- app/askme-webapp-mohub目前需要设置环境变量才能测试运行，具体字段说明参考`.env`文件内注释。
- 各类配置项在src/lib/models.ts和src/lib/config.ts文件内。
- GitLab的`OAUTH_APPLICATION_ID`和`OAUTH_APPLICATION_SECRET`的获取请查看[Gitlab文档](https://docs.gitlab.com/integration/oauth_provider/)
- 开发请记得一定连内网vpn。特别是报错Timeout或者:
- 本地开发gitlab配的是localhost，tongyuan配的是127.0.0.1（tongyuan login不支持localhost域名的回调）。本地开发环境你需要访问对应的域名才能使用对应方式正确登陆。

```
SocketError: other side closed
code: 'UND_ERR_SOCKET'
```

的时候。

## API端点

- `/api/chat/send_message`：发送消息到LLM，获取LLM回复
- `/api/chat`：GET：获取用户的所有聊天记录。DELETE:删除指定聊天。
- `/api/summary`：生成首轮对话标题
- `/api/vote/like/{messageId}`：点赞或点踩消息
