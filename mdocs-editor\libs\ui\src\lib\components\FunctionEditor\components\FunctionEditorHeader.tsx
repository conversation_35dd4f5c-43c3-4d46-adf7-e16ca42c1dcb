import React from "react";
import { ViewToggle, type ViewType } from "./ui/ViewToggle";
import { TagInput } from "./ui/TagInput";
import { useFunctionContent } from "../hooks/useFunctionContent";
import type { FunctionContent } from "../FunctionEditor";
import { Options } from "../views/FormView";

export interface FunctionEditorHeaderProps {
  /** 函数数据 */
  functionData: Partial<FunctionContent>;
  /** 数据更新回调函数 */
  onDataChange: (updates: Partial<FunctionContent>) => void;
  /** 当前视图类型 */
  currentView: ViewType;
  /** 视图切换回调 */
  onViewChange: (view: ViewType) => void;
  /** 是否处于加载状态 */
  isLoading?: boolean;
  /** 可选的标签列表 */
  availableTags?: Options[];
  /** 函数名称 */
  functionName?: string;
}

/**
 * 函数编辑器头部组件
 *
 * 包含：
 * - 标题和视图切换器
 * - 函数标签编辑
 * - 废弃状态编辑
 */
export const FunctionEditorHeader: React.FC<FunctionEditorHeaderProps> = ({
  functionData,
  onDataChange,
  currentView,
  onViewChange,
  isLoading = false,
  availableTags = [],
  functionName,
}) => {
  const { updateField } = useFunctionContent({
    content: functionData,
    onContentChange: onDataChange,
  });

  // 添加标签
  const handleAddTag = (tagValue: string) => {
    const currentTags = functionData.tags || [];
    if (!currentTags.includes(tagValue)) {
      updateField('tags', [...currentTags, tagValue]);
    }
  };

  // 移除标签
  const handleRemoveTag = (index: number) => {
    const newTags = (functionData.tags || []).filter((_, i) => i !== index);
    updateField('tags', newTags);
  };

  return (
    <div className="border-b">
      {/* 标题栏 */}
      <div className="flex items-center justify-between p-4">
        <div className="flex items-center gap-3">
          {functionName && (
            <div className="flex items-center gap-2">
              <h2 className="text-xl font-semibold ">{functionName}</h2>
              {/* 废弃状态放在函数名旁边 */}
              <div className="flex items-center gap-1">
                <input
                  type="checkbox"
                  id="deprecated-header"
                  checked={functionData.deprecated || false}
                  onChange={(e) => updateField('deprecated', e.target.checked)}
                  disabled={isLoading}
                  className="w-3 h-3 text-orange-600 bg-gray-100 border-gray-300 rounded focus:ring-orange-500 focus:ring-1"
                />
                <label htmlFor="deprecated-header" className="text-xs text-gray-600 cursor-pointer">
                  废弃
                </label>
              </div>
            </div>
          )}
        </div>
        <ViewToggle
          currentView={currentView}
          onViewChange={onViewChange}
          disabled={isLoading}
        />
      </div>

      {/* Tagline区域：函数标签 */}
      <div className="px-4 pb-3">
        <TagInput
          tags={functionData.tags || []}
          availableTags={availableTags}
          onTagAdd={handleAddTag}
          onTagRemove={handleRemoveTag}
          disabled={isLoading}
          label="标签"
          placeholder="暂无标签"
        />
      </div>
    </div>
  );
};
