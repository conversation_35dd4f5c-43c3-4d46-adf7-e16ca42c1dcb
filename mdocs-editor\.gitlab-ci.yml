include:
  - 'https://git.tongyuan.cc/syslab/ci/gitlabpages-vitepress/-/raw/main/v1.yaml'

stages:
  - lint
  - test
  - build
  - deploy

variables:
  NODE_VERSION: 22
  PNPM_VERSION: 10.10.0
  NPM_REGISTRY: https://mirrors-dev.tongyuan.cc/npm

cache:
  key:
    files:
      - ${CI_PROJECT_DIR}/pnpm-lock.yaml
  paths:
    - ${CI_PROJECT_DIR}/.pnpm-store/

.rules_on_merge_request:
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH && $CI_OPEN_MERGE_REQUESTS
      when: never
    - if: $CI_COMMIT_BRANCH

.prepare:
  image: node:${NODE_VERSION}
  tags:
    - docker-linux-x86_64
  before_script:
    - node -v
    - npm -v
    - npm config set registry $NPM_REGISTRY
    - npm install -g pnpm@$PNPM_VERSION
    - pnpm -v
    - pnpm config set store-dir $CI_PROJECT_DIR/.pnpm-store
    - pnpm config set registry $NPM_REGISTRY
    - pnpm install --frozen-lockfile

pages:
  stage: build
  extends: .vitepress-pnpm

lint:
  stage: test
  script:
    - pnpm run build:libs
    - pnpm run lint
  rules:
    - !reference [.rules_on_merge_request, rules]
  extends: .prepare

test:
  image: node:${NODE_VERSION}
  stage: test
  script:
    - pnpm run build:libs
    - pnpm run test
  rules:
    - !reference [.rules_on_merge_request, rules]
  extends: .prepare

build-ui:
  image: node:${NODE_VERSION}
  stage: build
  rules:
    - !reference [.rules_on_merge_request, rules]
  extends: .prepare
  script:
    - pnpm run build:libs
    - pnpm -C libs/ui build-storybook
  artifacts:
    paths:
      - libs/ui/.storybook-static

build-editor:
  image: node:${NODE_VERSION}
  stage: build
  rules:
    - !reference [.rules_on_merge_request, rules]
  extends: .prepare
  dependencies: []
  script:
    - pnpm run build:libs
    - pnpm -C apps/mdocs-editor prisma:generate
    - pnpm run build:app
  after_script:
    - |
      if [ "$CI_JOB_STATUS" = "success" ]; then
        echo "Copying build outputs..."
        cp -r ./apps/mdocs-editor/.next/standalone/apps/mdocs-editor dist/
        cp -r ./apps/mdocs-editor/.next/static dist/.next/
        cp -r ./apps/mdocs-editor/public dist/public
      fi
  artifacts:
    paths:
      - dist

build-editor-docker:
  tags:
    - podman
  stage: build
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH && $CI_COMMIT_REF_PROTECTED
    - if: $CI_COMMIT_TAG
  variables:
    IMAGE_PREFIX: git.tongyuan.cc:5050/mdocs/deployment
  script:
    - podman login git.tongyuan.cc:5050 -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD
    - podman build -f apps/mdocs-editor/Dockerfile -t $IMAGE_PREFIX/mdocs-editor:latest .
    - podman push $IMAGE_PREFIX/mdocs-editor:latest
    - |
      if [ -n "$CI_COMMIT_TAG" ]; then
        podman tag $IMAGE_PREFIX/mdocs-editor:latest $IMAGE_PREFIX/mdocs-editor:$CI_COMMIT_TAG
        podman push $IMAGE_PREFIX/mdocs-editor:$CI_COMMIT_TAG
      fi

deploy-editor-dev:
  image: git.tongyuan.cc:5050/syslab/ci/tools/ubuntu:20.04-full-python3.10
  tags:
    - docker
  stage: deploy
  rules:
    - if: '$CI_COMMIT_BRANCH && $CI_COMMIT_BRANCH != "" && $CI_COMMIT_REF_PROTECTED == "true"'
      when: on_success
    - when: never
  needs: ["build-editor-docker"]
  environment:
    name: editor-dev
    url: https://mdocs-dev.tongyuan.cc
  script:
    - |
      if [ -z "$CONFIG_ENVFILE" ]; then
        echo "Error: CONFIG_ENVFILE environment variable is not set"
        exit 1
      fi
    - cp $DEPLOY_SSH_PRIVATE_KEY id_rsa
    - chmod 400 id_rsa
    - bash deployments/mdocs-editor/deploy.sh