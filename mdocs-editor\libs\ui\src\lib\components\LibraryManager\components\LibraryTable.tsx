import { ExternalLink } from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/lib/components/ui/table";
import { Button } from "@/lib/components/ui/button";
import { Skeleton } from "@/lib/components/ui/skeleton";
import { LibraryActions } from "./LibraryActions";
import { Library } from "../LibraryManager";



/**
 * LibraryTable 组件属性接口
 */
export interface LibraryTableProps {
  /** Library 列表数据 */
  libraries: Library[];
  /** Library 点击回调函数 */
  onLibraryClick?: (library: Library) => void;
  /** 导出回调函数 */
  onExport: (library: Library) => void | Promise<void>;
  /** 删除回调函数 */
  onDelete: (library: Library) => void | Promise<void>;
  /** 加载状态 */
  loading?: boolean;
  /** 当前页码 */
  currentPage?: number;
  /** 每页条数 */
  pageSize?: number;
  /** 总条数 */
  total?: number;
  /** 分页变化回调 */
  onPageChange?: (page: number) => void;
}

/**
 * 格式化日期
 */
const formatDate = (date?: Date): string => {
  if (!date) return '-';
  return new Intl.DateTimeFormat('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
  }).format(date);
};

/**
 * LibraryTable 组件
 * 
 * 功能：
 * - 展示 函数库 列表
 * - 支持分页
 * - 响应式设计
 * - 操作菜单
 * 
 */
export function LibraryTable({
  libraries,
  onLibraryClick,
  onExport,
  onDelete,
  loading = false,
  currentPage = 1,
  pageSize = 10,
  total,
  onPageChange,
}: LibraryTableProps) {
  // 统一的表头组件
  const TableHeaderRow = () => (
    <TableHeader>
      <TableRow>
        <TableHead className="min-w-[100px] sm:min-w-[120px]">名称</TableHead>
        <TableHead className="min-w-[120px] sm:min-w-[200px] hidden sm:table-cell">摘要</TableHead>
        <TableHead className="min-w-[80px] sm:w-[100px]">最新版本</TableHead>
        <TableHead className="min-w-[100px] sm:w-[120px]">更新时间</TableHead>
        <TableHead className="w-[60px]">操作</TableHead>
      </TableRow>
    </TableHeader>
  );

  if (loading) {
    return (
      <div className="rounded-md border overflow-hidden">
        <div className="overflow-x-auto">
          <Table>
            <TableHeaderRow />
            <TableBody>
              {Array.from({ length: 5 }).map((_, index) => (
                <TableRow key={index}>
                  <TableCell>
                    <div className="space-y-1">
                      <Skeleton className="h-4 w-[120px]" />
                      <div className="sm:hidden">
                        <Skeleton className="h-3 w-[150px]" />
                      </div>
                      <div className="md:hidden">
                        <Skeleton className="h-3 w-[100px]" />
                      </div>
                    </div>
                  </TableCell>
                  <TableCell className="hidden sm:table-cell">
                    <Skeleton className="h-4 w-[200px]" />
                  </TableCell>
                  <TableCell className="min-w-[80px] sm:w-[100px]">
                    <Skeleton className="h-4 w-[80px]" />
                  </TableCell>
                  <TableCell className="min-w-[100px] sm:w-[120px]">
                    <Skeleton className="h-4 w-[100px]" />
                  </TableCell>
                  <TableCell>
                    <Skeleton className="h-8 w-[60px]" />
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>
    );
  }

  if (libraries.length === 0) {
    return (
      <div className="rounded-md border overflow-hidden">
        <div className="overflow-x-auto">
          <Table>
            <TableHeaderRow />
            <TableBody>
              <TableRow>
                <TableCell colSpan={5} className="h-24 text-center text-muted-foreground">
                  暂无数据
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </div>
      </div>
    );
  }

  // 计算分页数据
  const startIndex = (currentPage - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const paginatedLibraries = libraries.slice(startIndex, endIndex);
  const totalPages = Math.ceil(libraries.length / pageSize);

  return (
    <div className="space-y-4">
      <div className="rounded-md border overflow-hidden">
        <div className="overflow-x-auto">
          <Table>
            <TableHeaderRow />
            <TableBody>
              {paginatedLibraries.map((library) => (
                <TableRow key={library.name}>
                  <TableCell className="font-medium">
                    <div className="space-y-1">
                      <Button
                        variant="link"
                        className="h-auto p-0 font-medium text-left justify-start"
                        onClick={() => onLibraryClick?.(library)}
                      >
                        {library.name}
                        <ExternalLink className="ml-1 h-3 w-3 flex-shrink-0" />
                      </Button>
                      {/* 显示摘要 */}
                      <div className="sm:hidden text-sm text-muted-foreground truncate max-w-[200px]">
                        {library.summary || '-'}
                      </div>
                      {/* 显示版本信息 */}
                      <div className="md:hidden text-xs text-muted-foreground">
                        {library.latestVersion || '-'}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell className="hidden sm:table-cell max-w-[300px]">
                    <div className="truncate" title={library.summary}>
                      {library.summary || '-'}
                    </div>
                  </TableCell>
                  <TableCell className="min-w-[80px] sm:w-[100px]">
                    {library.latestVersion || '-'}
                  </TableCell>
                  <TableCell className="min-w-[100px] sm:w-[120px]">
                    {formatDate(library.lastUpdated)}
                  </TableCell>
                  <TableCell>
                    <LibraryActions
                      library={library}
                      onExport={onExport}
                      onDelete={onDelete}
                    />
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>

      {/* 分页控件 */}
      {totalPages > 1 && (
        <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
          <div className="text-sm text-muted-foreground">
            显示 {startIndex + 1}-{Math.min(endIndex, libraries.length)} 条，共 {total || libraries.length} 条
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => onPageChange?.(currentPage - 1)}
              disabled={currentPage <= 1}
            >
              上一页
            </Button>

            {/* 智能分页显示 */}
            <div className="flex items-center space-x-1">
              {/* 第一页 */}
              {currentPage > 3 && (
                <>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onPageChange?.(1)}
                    className="w-8 h-8 p-0"
                  >
                    1
                  </Button>
                  {currentPage > 4 && <span className="text-muted-foreground">...</span>}
                </>
              )}

              {/* 当前页附近的页码 */}
              {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                const page = Math.max(1, Math.min(totalPages - 4, currentPage - 2)) + i;
                if (page > totalPages) return null;
                return (
                  <Button
                    key={page}
                    variant={page === currentPage ? "default" : "outline"}
                    size="sm"
                    onClick={() => onPageChange?.(page)}
                    className="w-8 h-8 p-0"
                  >
                    {page}
                  </Button>
                );
              })}

              {/* 最后一页 */}
              {currentPage < totalPages - 2 && (
                <>
                  {currentPage < totalPages - 3 && <span className="text-muted-foreground">...</span>}
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onPageChange?.(totalPages)}
                    className="w-8 h-8 p-0"
                  >
                    {totalPages}
                  </Button>
                </>
              )}
            </div>

            <Button
              variant="outline"
              size="sm"
              onClick={() => onPageChange?.(currentPage + 1)}
              disabled={currentPage >= totalPages}
            >
              下一页
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
