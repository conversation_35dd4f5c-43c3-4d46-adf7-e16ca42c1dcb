<script module>
  //    👆 notice the module context, defineMeta does not work in a regular <script> tag - instance
  import { defineMeta } from '@storybook/addon-svelte-csf';

  import CodeBar from './CodeBar.svelte';

  //      👇 Get the Story component from the return value
  const { Story } = defineMeta({
    title: 'UI/CodeBar',
    component: CodeBar,
    decorators: [
      /* ... */
    ],
    parameters: {
      /* ... */
    },
  });
</script>

<Story
  name="Default"
  args={{
    codeSnippet: { language: 'julia', code: 'println("hello world")' },
  }}
/>
