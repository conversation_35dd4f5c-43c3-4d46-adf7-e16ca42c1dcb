{"name": "@askme/lib-ui", "private": true, "version": "1.1.0", "scripts": {"build": "vite build && pnpm run prepack", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "prepack": "svelte-kit sync && svelte-package && publint", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "format": "prettier --write .", "lint": "prettier --check . && eslint .", "test:unit": "vitest", "test": "npm run test:unit -- --run", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "files": ["dist", "!dist/**/*.test.*", "!dist/**/*.spec.*"], "sideEffects": ["**/*.css"], "svelte": "./dist/index.js", "types": "./dist/index.d.ts", "type": "module", "exports": {".": {"types": "./dist/index.d.ts", "svelte": "./dist/index.js"}, "./shadcn-sidebar": "./dist/components/ui/sidebar/index.js", "./shadcn-sonner": "./dist/components/ui/sonner/index.js"}, "peerDependencies": {"svelte": "^5.0.0"}, "devDependencies": {"@storybook/addon-a11y": "^9.0.18", "@storybook/addon-docs": "^9.0.18", "@storybook/addon-svelte-csf": "5.0.7", "@storybook/addon-vitest": "9.0.18", "@storybook/svelte": "9.0.18", "@storybook/sveltekit": "9.0.18", "@sveltejs/adapter-auto": "6.0.1", "@sveltejs/package": "^2.3.11", "@testing-library/jest-dom": "^6.6.3", "@testing-library/svelte": "^5.2.8", "bits-ui": "2.8.10", "clsx": "^2.1.1", "eslint-config-prettier": "^10.1.5", "eslint-plugin-svelte": "3.9.2", "globals": "^16.2.0", "jsdom": "^26.1.0", "mode-watcher": "1.1.0", "prettier": "^3.5.3", "prettier-plugin-svelte": "^3.4.0", "prettier-plugin-tailwindcss": "^0.6.12", "publint": "^0.3.12", "storybook": "^9.0.18", "storybook-addon-mock": "^6.0.1", "svelte": "5.33.19", "svelte-check": "^4.2.1", "svelte-sonner": "^1.0.5", "tailwindcss": "^4.1.8", "typescript": "^5.8.3", "vite": "6.3.5", "vitest": "^3.2.3"}, "dependencies": {"@askme/lib-common": "workspace:*", "@sveltejs/kit": "2.21.3", "ai": "^4.3.16", "highlight.js": "^11.11.1", "katex": "^0.16.22", "rehype-highlight": "^7.0.2", "rehype-katex": "^7.0.1", "remark-math": "^6.0.0", "runed": "^0.31.1", "svelte-exmarkdown": "^5.0.1"}}