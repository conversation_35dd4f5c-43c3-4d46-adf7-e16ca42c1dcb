import {
  generateSessionToken,
  createSession,
  setSessionTokenCookie,
  createUserIfNotExists,
} from '$lib/baseAuth';
import serverCtx from '$lib/context.server';
import { error, type RequestEvent } from '@sveltejs/kit';
import type { OAuth2Tokens } from 'arctic';
import * as LogFormat from '@askme/lib-common/logformat';
import { config } from '$lib/config.server';
import { getUserInfoByOAuthProvider } from '$lib/oauthUtils';

// 处理OAuth2回调
export async function GET(event: RequestEvent): Promise<Response> {
  if (config.oauth.provider.toUpperCase() !== 'GITLAB') {
    return new Response('Unauthorized', {
      status: 401,
    });
  }

  if (!serverCtx.oauth) {
    console.error(
      LogFormat.error('OAuth client not initialized,please check OAUTH_PROVIDER in .env file'),
    );
    process.exit(1);
  }

  // 验证Oauth2 callback 参数
  const code = event.url.searchParams.get('code');
  const state = event.url.searchParams.get('state');
  const storedState = event.cookies.get('gitlab_oauth_state') ?? null;
  if (!code || !state || !storedState) {
    if (!code) {
      console.debug(
        LogFormat.debug('Missing authorization code in OAuth2 callback query parameters.'),
      );
    }

    if (!state) {
      console.debug(LogFormat.debug('Missing state in OAuth2 callback query parameters.'));
    }

    if (!storedState) {
      console.debug(LogFormat.debug('Missing state cookies in OAuth2 callback cookie.'));
    }

    return new Response('Unauthorized', {
      status: 401,
    });
  }
  if (state !== storedState) {
    console.debug(LogFormat.debug('State in OAuth2 callback query parameters does not match.'));
    return new Response('Unauthorized', {
      status: 401,
    });
  }
  // auth code换access token
  let tokens: OAuth2Tokens;
  try {
    tokens = await serverCtx.oauth.gitlab.validateAuthorizationCode(code);
  } catch {
    console.debug(LogFormat.debug('Failed to validate authorization code.'));
    // Invalid code or client credentials
    return new Response('Unauthorized', {
      status: 401,
    });
  }

  let userInfo;
  // 用token请求用户信息
  try {
    userInfo = await getUserInfoByOAuthProvider('GITLAB', tokens.accessToken());
  } catch (errro) {
    console.error(LogFormat.error('Failed to get user info.' + errro + 'Provider:GITLAB'));
    return new Response('Server Error', {
      status: 500,
    });
  }

  let user;
  try {
    user = await createUserIfNotExists(serverCtx.prisma, userInfo, 'GITLAB');
  } catch (e) {
    console.error(LogFormat.error('Failed to create user.' + e));
    return error(500, 'Server Error');
  }
  // 创建会话，设置cookie
  const sessionToken = generateSessionToken();
  const session = await createSession(
    serverCtx.prisma,
    sessionToken,
    tokens.accessToken(),
    tokens.refreshToken(),
    tokens.accessTokenExpiresAt(),
    user.id,
    false,
  );

  setSessionTokenCookie(event, sessionToken, session.expiresAt);

  return new Response(null, {
    status: 302,
    headers: {
      Location: '/',
    },
  });
}
